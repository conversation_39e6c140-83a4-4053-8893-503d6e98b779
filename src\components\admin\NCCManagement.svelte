<script lang="ts">
  import { onMount } from 'svelte';
  import AddSupplierForm from './AddNCCForm.svelte';
  import EditSupplierForm from './EditNCCForm.svelte';

  interface Supplier {
    ma_nha_cung_cap: number;
    ten_nha_cung_cap: string;
    loai_dich_vu: string;
    dia_chi?: string;
    so_dien_thoai?: string;
    email?: string;
    website?: string;
    ghi_chu?: string;
    ngay_tao?: string;
  }

  let nhacungcap: Supplier[] = [];
  let filterednhacungcap: Supplier[] = [];
  let isLoading = true;
  let error: string | null = null;
  let searchQuery = '';
  let currentPage = 1;
  let itemsPerPage = 10;
  let showAddSupplierPage = false;
  let showEditSupplierPage = false;
  let selectedSupplierId: number | null = null;
  let serviceTypeFilter = 'all';

  const serviceTypes = [
    { value: 'all', label: 'Tất cả' },
    { value: 'tour', label: 'Tour' },
    { value: 'khach_san', label: 'Khách sạn' },
    { value: 'phuong_tien', label: 'Phương tiện' },
    { value: 'vui_choi', label: 'Vui chơi' }
  ];

  $: filterednhacungcap = nhacungcap.filter(supplier => {
    const matchesSearch = searchQuery === '' ||
      supplier.ten_nha_cung_cap.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (supplier.dia_chi && supplier.dia_chi.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (supplier.email && supplier.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (supplier.so_dien_thoai && supplier.so_dien_thoai.includes(searchQuery));

    const matchesServiceType = serviceTypeFilter === 'all' || supplier.loai_dich_vu === serviceTypeFilter;

    return matchesSearch && matchesServiceType;
  });

  $: totalPages = Math.ceil(filterednhacungcap.length / itemsPerPage);
  $: startIndex = (currentPage - 1) * itemsPerPage;
  $: endIndex = Math.min(startIndex + itemsPerPage, filterednhacungcap.length);
  $: displayednhacungcap = filterednhacungcap.slice(startIndex, endIndex);

  async function fetchnhacungcap() {
    isLoading = true;
    error = null;
    try {
      const response = await fetch('http://localhost:5000/api/nhacungcap');
      if (!response.ok) {
        throw new Error(`Lỗi ${response.status}: Không thể tải danh sách nhà cung cấp.`);
      }
      const data = await response.json();
      nhacungcap = data.nhacungcap || [];
      isLoading = false;
    } catch (err) {
      console.error('Lỗi khi tải danh sách nhà cung cấp:', err);
      error = err.message;
      isLoading = false;
    }
  }

  async function handleDelete(id: number) {
    if (confirm('Bạn có chắc muốn xóa nhà cung cấp này?')) {
      try {
        const response = await fetch(`http://localhost:5000/api/nhacungcap/${id}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          throw new Error('Không thể xóa nhà cung cấp');
        }

        await fetchnhacungcap();
        alert('Xóa nhà cung cấp thành công!');
      } catch (error) {
        console.error('Lỗi khi xóa nhà cung cấp:', error);
        alert('Có lỗi khi xóa nhà cung cấp: ' + error.message);
      }
    }
  }

  function toggleAddSupplierPage() {
    showAddSupplierPage = !showAddSupplierPage;
    showEditSupplierPage = false;
  }

  function showEditSupplier(id: number) {
    selectedSupplierId = id;
    showEditSupplierPage = true;
    showAddSupplierPage = false;
  }

  function closeEditSupplierPage() {
    showEditSupplierPage = false;
    selectedSupplierId = null;
  }

  function handleSupplierAdded() {
    fetchnhacungcap();
    toggleAddSupplierPage();
  }

  function handleSupplierUpdated() {
    fetchnhacungcap();
    closeEditSupplierPage();
  }

  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
    }
  }

  function formatServiceType(type: string): string {
    const serviceType = serviceTypes.find(st => st.value === type);
    return serviceType ? serviceType.label : type;
  }

  onMount(() => {
    fetchnhacungcap();
  });
</script>

<div class="supplier-management">
  {#if isLoading}
    <div class="loading-indicator">Đang tải dữ liệu nhà cung cấp...</div>
  {:else if error}
    <div class="error-message">{error}</div>
  {:else if !showAddSupplierPage && !showEditSupplierPage}
    <div class="actions">
      <button class="add-button" on:click={toggleAddSupplierPage}>
        <i class="fas fa-plus"></i> Thêm Nhà Cung Cấp Mới
      </button>
      <div class="filters">
        <div class="service-type-filter">
          <label for="service-type-select">Loại dịch vụ:</label>
          <select id="service-type-select" bind:value={serviceTypeFilter}>
            {#each serviceTypes as type}
              <option value={type.value}>{type.label}</option>
            {/each}
          </select>
        </div>
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input
            type="search"
            placeholder="Tìm kiếm theo tên, địa chỉ, email..."
            bind:value={searchQuery}
            aria-label="Tìm kiếm nhà cung cấp"
          >
        </div>
      </div>
    </div>

    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Mã NCC</th>
            <th>Tên nhà cung cấp</th>
            <th>Loại dịch vụ</th>
            <th>Địa chỉ</th>
            <th>Số điện thoại</th>
            <th>Email</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {#if displayednhacungcap.length === 0}
            <tr>
              <td colspan="7" class="no-data">Không có nhà cung cấp nào</td>
            </tr>
          {:else}
            {#each displayednhacungcap as supplier}
              <tr>
                <td>{supplier.ma_nha_cung_cap}</td>
                <td>{supplier.ten_nha_cung_cap}</td>
                <td>
                  <span class="service-type-badge {supplier.loai_dich_vu}">
                    {formatServiceType(supplier.loai_dich_vu)}
                  </span>
                </td>
                <td>{supplier.dia_chi || 'Chưa cập nhật'}</td>
                <td>{supplier.so_dien_thoai || 'Chưa cập nhật'}</td>
                <td>{supplier.email || 'Chưa cập nhật'}</td>
                <td class="actions-cell">
                  <button
                    class="action-btn edit"
                    aria-label="Sửa thông tin"
                    on:click={() => showEditSupplier(supplier.ma_nha_cung_cap)}
                  >
                    <i class="fas fa-edit"></i>
                  </button>
                  <button
                    class="action-btn delete"
                    aria-label="Xóa nhà cung cấp"
                    on:click={() => handleDelete(supplier.ma_nha_cung_cap)}
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>

    {#if totalPages > 1}
      <div class="pagination">
        <button on:click={() => goToPage(currentPage - 1)} disabled={currentPage === 1}>
          Trước
        </button>
        {#each Array(totalPages) as _, index}
          <button
            class:active={currentPage === index + 1}
            on:click={() => goToPage(index + 1)}
          >
            {index + 1}
          </button>
        {/each}
        <button
          on:click={() => goToPage(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Sau
        </button>
      </div>
    {/if}
  {:else if showAddSupplierPage}
    <div class="add-supplier-container">
      <div class="add-supplier-header">
        <button class="back-button" on:click={toggleAddSupplierPage}>
          <i class="fas fa-arrow-left"></i> Quay lại
        </button>
      </div>
      <AddSupplierForm on:supplierAdded={handleSupplierAdded} />
    </div>
  {:else if showEditSupplierPage && selectedSupplierId !== null}
    <div class="edit-supplier-container">
      <div class="edit-supplier-header">
        <button class="back-button" on:click={closeEditSupplierPage}>
          <i class="fas fa-arrow-left"></i> Quay lại
        </button>
      </div>
      <EditSupplierForm
        supplierId={selectedSupplierId}
        on:supplierUpdated={handleSupplierUpdated}
        on:cancel={closeEditSupplierPage}
      />
    </div>
  {/if}
</div>

<style>
  .supplier-management {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 2rem;
  }

  .actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .filters {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
  }

  .service-type-filter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .service-type-filter select {
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #ddd;
    background-color: white;
  }

  .add-button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .add-button:hover {
    background-color: #45a049;
  }

  .search-box {
    position: relative;
    width: 300px;
  }

  .search-box i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
  }

  .search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
  }

  .table-container {
    overflow-x: auto;
    margin-bottom: 1.5rem;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
  }

  th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
  }

  th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
  }

  .service-type-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
  }

  .service-type-badge.tour {
    background-color: #e3f2fd;
    color: #1565c0;
  }

  .service-type-badge.khach_san {
    background-color: #e8f5e9;
    color: #2e7d32;
  }

  .service-type-badge.phuong_tien {
    background-color: #fff3e0;
    color: #e65100;
  }

  .service-type-badge.vui_choi {
    background-color: #f3e5f5;
    color: #6a1b9a;
  }

  .actions-cell {
    display: flex;
    gap: 0.5rem;
  }

  .action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.3s;
  }

  .action-btn.edit {
    color: #2196F3;
  }

  .action-btn.delete {
    color: #F44336;
  }

  .action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
  }

  .pagination button {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background-color: white;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .pagination button.active {
    background-color: #1a237e;
    color: white;
    border-color: #1a237e;
  }

  .pagination button:hover:not(:disabled) {
    background-color: #f1f1f1;
  }

  .pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .loading-indicator, .error-message, .no-data {
    text-align: center;
    padding: 2rem;
    color: #666;
  }

  .error-message {
    color: #F44336;
  }

  .back-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: #1a237e;
    font-weight: 500;
    margin-bottom: 1.5rem;
    border-radius: 4px;
    transition: background-color 0.3s;
  }

  .back-button:hover {
    background-color: rgba(26, 35, 126, 0.05);
  }

  .add-supplier-header, .edit-supplier-header {
    margin-bottom: 1.5rem;
  }
</style>
