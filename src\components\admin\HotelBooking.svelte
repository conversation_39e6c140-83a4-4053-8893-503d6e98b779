<!-- <script lang="ts">
  import { onMount } from 'svelte';
  import { hotelBookings, user } from '../../stores/userStore';

  interface HotelBooking {
    ma_dat_phong: number | string;
    id?: number;
    hotelDetails: {
      ma_khach_san: number | string;
      ten_khach_san: string;
      dia_diem: string;
      hinh_anh: string;
    };
    ten_nguoi_dat?: string;
    email_nguoi_dat?: string;
    sdt_nguoi_dat?: string;
    checkInDate: string;
    checkOutDate: string;
    guests: number;
    rooms: number;
    totalPrice: number;
    bookingDate: string;
    trang_thai?: string;
    status?: string;
  }

  let allHotelBookings: HotelBooking[] = [];
  let currentPage = 1;
  let itemsPerPage = 10;
  let searchQuery = '';
  let isLoading = true;
  let error = '';

  function sortHotelBookings(bookingsToSort: HotelBooking[]): HotelBooking[] {
    return [...bookingsToSort].sort((a, b) => new Date(b.bookingDate).getTime() - new Date(a.bookingDate).getTime());
  }

  $: filteredHotelBookings = sortHotelBookings(allHotelBookings.filter(booking => {
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch =
      (booking.hotelDetails.ten_khach_san && booking.hotelDetails.ten_khach_san.toLowerCase().includes(searchLower)) ||
      (booking.hotelDetails.dia_diem && booking.hotelDetails.dia_diem.toLowerCase().includes(searchLower)) ||
      (booking.ten_nguoi_dat && booking.ten_nguoi_dat.toLowerCase().includes(searchLower)) ||
      (booking.email_nguoi_dat && booking.email_nguoi_dat.toLowerCase().includes(searchLower));

    return matchesSearch;
  }));

  $: totalPages = Math.ceil(filteredHotelBookings.length / itemsPerPage);
  $: startIndex = (currentPage - 1) * itemsPerPage;
  $: displayedHotelBookings = filteredHotelBookings.slice(startIndex, startIndex + itemsPerPage);

  onMount(() => {
    loadAllHotelBookings();

    const unsubscribe = hotelBookings.subscribe(updatedBookings => {
      console.log('Hotel bookings store updated:', updatedBookings.length);

      if (updatedBookings.length > 0 && allHotelBookings.length > 0) {
        allHotelBookings = allHotelBookings.map(booking => {
          const storeBooking = updatedBookings.find((b: any) =>
            (b.id && booking.id && b.id === booking.id) ||
            (b.ma_dat_phong && booking.ma_dat_phong && b.ma_dat_phong === booking.ma_dat_phong)
          );

          if (storeBooking && storeBooking.status) {
            return { ...booking, status: storeBooking.status };
          }

          return booking;
        });
      }
    });

    const handleBookingStatusChanged = (event: CustomEvent) => {
      console.log('HotelBookingManagement: Detected booking status change event:', event.detail);

      if (event.detail && event.detail.bookingType === 'hotel') {
        const { bookingId, newStatus } = event.detail;
        updateBookingStatus(bookingId, newStatus);
      }
    };

    document.addEventListener('bookingStatusChanged', handleBookingStatusChanged);

    return () => {
      unsubscribe();
      document.removeEventListener('bookingStatusChanged', handleBookingStatusChanged);
    };
  });

  async function loadAllHotelBookings() {
    isLoading = true;
    error = '';
    console.log('Đang tải danh sách đặt phòng khách sạn...');

    try {
      const url = 'http://localhost:5000/api/hotel-bookings';
      console.log(`Gửi request đến: ${url}`);

      const response = await fetch(url);
      console.log(`Nhận response với status: ${response.status}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(errorData.message || `Lỗi ${response.status}: Không thể tải danh sách đặt phòng`);
      }

      const data = await response.json();
      console.log('Dữ liệu nhận được từ API:', data);

      allHotelBookings = data.bookings || data || [];
      console.log(`Đã tải ${allHotelBookings.length} đặt phòng từ API`);

    } catch (err) {
      console.error('Lỗi khi tải danh sách đặt phòng:', err);
      error = `Không thể tải danh sách đặt phòng: ${err.message}`;
    } finally {
      if (!error || (error && allHotelBookings.length > 0)) {
         isLoading = false;
      }
    }
  }

  function formatPrice(price: number): string {
    if (price === null || price === undefined || isNaN(price)) return 'N/A';
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' VNĐ';
  }

  function formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('vi-VN');
    } catch (e) {
      return 'Ngày không hợp lệ';
    }
  }

  function getStatusText(status: string | undefined): string {
    if (!status) return 'N/A';

    switch (status) {
      case 'pending': return 'Chờ xác nhận';
      case 'awaiting_payment': return 'Chờ thanh toán';
      case 'confirmed': return 'Đã thanh toán';
      case 'cancelled': return 'Đã hủy';
      case 'completed': return 'Hoàn thành';
      default: return status;
    }
  }

  function getStatusClass(status: string | undefined): string {
    if (!status) return '';

    switch (status) {
      case 'pending': return 'status-pending';
      case 'awaiting_payment': return 'status-warning';
      case 'confirmed': return 'status-success';
      case 'cancelled': return 'status-cancelled';
      case 'completed': return 'status-completed';
      default: return '';
    }
  }

  function getFilenameFromPath(path: string): string {
    if (!path) return '';
    const parts = path.split(/[\/\\]/);
    return parts[parts.length - 1];
  }

  function formatImageUrl(imageUrl: string | undefined): string {
     if (!imageUrl) return '#';
     if (imageUrl.startsWith('data:image')) {
       return imageUrl;
     } else if (imageUrl.startsWith('http')) {
       return imageUrl;
     } else if (imageUrl.startsWith('/')) {
        const filename = getFilenameFromPath(imageUrl);
        return `/images/${filename}`;
     } else {
        const filename = getFilenameFromPath(imageUrl);
        return `/images/${filename}`;
     }
  }

  async function deleteHotelBooking(booking: HotelBooking) {
    const bookingId = booking.ma_dat_phong;
    if (!bookingId) {
        alert('Không tìm thấy mã đặt phòng để xóa.');
        return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa đặt phòng tại "${booking.hotelDetails.ten_khach_san}" (Mã: ${bookingId}) không?`)) {
      try {
        console.log(`Xóa hotel booking ID ${bookingId}`);
        error = '';
        isLoading = true;

        const url = `http://localhost:5000/api/hotel-bookings/${bookingId}`;
        console.log(`Gửi request DELETE đến: ${url}`);

        const response = await fetch(url, {
          method: 'DELETE'
        });

        console.log(`Nhận response với status: ${response.status}`);

        if (!response.ok) {
          let responseData: any = null;
          try {
            responseData = await response.json();
            console.log('Response data:', responseData);
          } catch (jsonError) {
            console.log('Không thể parse response JSON:', jsonError);
          }
          const errorMessage = responseData?.message || `Lỗi ${response.status}: Không thể xóa đặt phòng`;
          throw new Error(errorMessage);
        }

        allHotelBookings = allHotelBookings.filter(b => b.ma_dat_phong !== bookingId);

        alert('Đã xóa đặt phòng thành công');

      } catch (err) {
        console.error('Lỗi khi xóa đặt phòng:', err);
        error = `Không thể xóa đặt phòng: ${err.message}`;
        alert(error);
      } finally {
        isLoading = false;
      }
    }
  }

  async function updateBookingStatus(bookingId: number | string, newStatus: string) {
    try {
      console.log(`HotelBookingManagement: Cập nhật trạng thái cho đặt phòng ID ${bookingId} thành ${newStatus}`);

      const bookingIndex = allHotelBookings.findIndex(b =>
        (b.id && b.id.toString() === bookingId.toString()) ||
        (b.ma_dat_phong && b.ma_dat_phong.toString() === bookingId.toString())
      );

      if (bookingIndex === -1) {
        console.log(`HotelBookingManagement: Không tìm thấy booking với ID ${bookingId}`);
        return;
      }

      allHotelBookings = allHotelBookings.map((booking, index) => {
        if (index === bookingIndex) {
          console.log(`HotelBookingManagement: Cập nhật booking ${bookingId} từ ${booking.status || 'N/A'} thành ${newStatus}`);
          return { ...booking, status: newStatus };
        }
        return booking;
      });

      hotelBookings.update(currentBookings => {
        return currentBookings.map((booking: any) => {
          if ((booking.id && booking.id.toString() === bookingId.toString()) ||
              (booking.ma_dat_phong && booking.ma_dat_phong.toString() === bookingId.toString())) {
            console.log(`HotelBookingManagement: Cập nhật booking trong store từ ${booking.status || 'N/A'} thành ${newStatus}`);
            return { ...booking, status: newStatus };
          }
          return booking;
        });
      });

      try {
        const bookingStatusChangedEvent = new CustomEvent('bookingStatusChanged', {
          detail: {
            bookingId: bookingId,
            bookingType: 'hotel',
            newStatus: newStatus,
            timestamp: new Date().toISOString()
          },
          bubbles: true,
          composed: true
        });

        document.dispatchEvent(bookingStatusChangedEvent);
        window.dispatchEvent(bookingStatusChangedEvent);

        console.log('HotelBookingManagement: Đã phát sự kiện bookingStatusChanged với chi tiết:', {
          bookingId: bookingId,
          bookingType: 'hotel',
          newStatus: newStatus
        });
      } catch (eventError) {
        console.error('HotelBookingManagement: Lỗi khi phát sự kiện bookingStatusChanged:', eventError);
      }

      console.log(`HotelBookingManagement: Đã cập nhật trạng thái booking ${bookingId} thành ${newStatus}`);
    } catch (err) {
      console.error('HotelBookingManagement: Lỗi khi cập nhật trạng thái booking:', err);
    }
  }

  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
    }
  }
</script>

<div class="booking-management hotel-booking-management">
  <div class="actions">
    <div class="filters">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="search"
          placeholder="Tìm theo tên khách sạn, địa điểm, người đặt..."
          bind:value={searchQuery}
          aria-label="Tìm kiếm đặt phòng khách sạn"
        >
      </div>
    </div>
  </div>

  {#if isLoading}
    <div class="loading-indicator">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Đang tải dữ liệu đặt phòng...</p>
    </div>
  {:else if error}
    <div class="error-message">
      <i class="fas fa-exclamation-circle"></i>
      <p>{error}</p>
      <button on:click={loadAllHotelBookings} class="retry-button">
        <i class="fas fa-redo"></i> Thử lại
      </button>
    </div>
  {:else if allHotelBookings.length === 0}
    <div class="no-bookings">
      <i class="fas fa-calendar-times"></i>
      <p>Chưa có đặt phòng khách sạn nào</p>
    </div>
  {:else}
    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Mã đặt</th>
            <th>Hình ảnh KS</th>
            <th>Tên khách sạn</th>
            <th>Địa điểm</th>
            <th>Người đặt</th>
            <th>Email</th>
            <th>Ngày nhận phòng</th>
            <th>Ngày trả phòng</th>
            <th>Khách/Phòng</th>
            <th>Tổng tiền</th>
            <th>Ngày đặt</th>
            <th>Trạng thái</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {#each displayedHotelBookings as booking (booking.ma_dat_phong)}
            <tr>
              <td>#{booking.ma_dat_phong}</td>
              <td class="image-cell">
                {#if booking.hotelDetails.hinh_anh}
                  <img
                    src={formatImageUrl(booking.hotelDetails.hinh_anh)}
                    alt={booking.hotelDetails.ten_khach_san}
                    class="tour-thumbnail"
                    loading="lazy"
                    on:error={(e) => {
                      console.error(`Image failed to load: ${booking.hotelDetails.hinh_anh}`);
                      (e.target as HTMLImageElement).style.display = 'none';
                       const nextEl = (e.target as HTMLImageElement).nextElementSibling;
                       if (nextEl instanceof HTMLElement) {
                         nextEl.style.display = 'flex';
                       }
                    }}
                  />
                  <div class="no-image" style="display: none;">Ảnh lỗi</div>
                {:else}
                  <div class="no-image">Không có ảnh</div>
                {/if}
              </td>
              <td class="hotel-name">{booking.hotelDetails.ten_khach_san}</td>
              <td class="hotel-location">{booking.hotelDetails.dia_diem}</td>
              <td class="customer-name">{booking.ten_nguoi_dat || 'N/A'}</td>
              <td class="customer-email">{booking.email_nguoi_dat || 'N/A'}</td>
              <td class="check-in-date">{formatDate(booking.checkInDate)}</td>
              <td class="check-out-date">{formatDate(booking.checkOutDate)}</td>
              <td class="guests-rooms">{booking.guests} / {booking.rooms}</td>
              <td class="price">{formatPrice(booking.totalPrice)}</td>
              <td class="booking-date">{formatDate(booking.bookingDate)}</td>
              <td>
                {#if booking.status}
                  <span class="status-badge {getStatusClass(booking.status)}">
                    {getStatusText(booking.status)}
                  </span>
                {:else if booking.trang_thai}
                  <span class="status-badge">
                    {booking.trang_thai}
                  </span>
                {:else}
                  <span class="status-badge status-pending">Chờ xác nhận</span>
                {/if}
              </td>
              <td class="actions-cell">
                {#if booking.status === 'pending' || booking.status === 'awaiting_payment'}
                  <button
                    class="action-btn confirm"
                    aria-label="Đánh dấu đã thanh toán"
                    title="Đánh dấu đã thanh toán"
                    on:click={() => updateBookingStatus(booking.id || booking.ma_dat_phong, 'confirmed')}
                  >
                    <i class="fas fa-check"></i>
                  </button>
                {/if}
                <button
                  class="action-btn delete"
                  aria-label="Xóa đặt phòng"
                  title="Xóa đặt phòng"
                  on:click={() => deleteHotelBooking(booking)}
                >
                  <i class="fas fa-trash"></i>
                </button>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>

    {#if totalPages > 1}
      <div class="pagination">
        <button
          class="pagination-btn"
          disabled={currentPage === 1}
          on:click={() => goToPage(1)}
          aria-label="Trang đầu"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button
          class="pagination-btn"
          disabled={currentPage === 1}
          on:click={() => goToPage(currentPage - 1)}
          aria-label="Trang trước"
        >
          <i class="fas fa-angle-left"></i>
        </button>

        <span class="page-info">Trang {currentPage} / {totalPages}</span>

        <button
          class="pagination-btn"
          disabled={currentPage === totalPages}
          on:click={() => goToPage(currentPage + 1)}
          aria-label="Trang sau"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button
          class="pagination-btn"
          disabled={currentPage === totalPages}
          on:click={() => goToPage(totalPages)}
          aria-label="Trang cuối"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
    {/if}
  {/if}
</div>

<style>
  .hotel-name, .hotel-location, .check-in-date, .check-out-date, .guests-rooms, .booking-date {
     white-space: nowrap;
  }

  .booking-management {
    padding: 30px;
    background-color: #f8f9fa;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 100%;
    overflow: hidden;
  }

  .actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
  }

  .filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    flex-grow: 1;
  }

  .search-box {
    position: relative;
    flex-grow: 1;
    max-width: 400px;
  }

  .search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
  }

  .search-box input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }

  .search-box input:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .loading-indicator, .error-message, .no-bookings {
    text-align: center;
    padding: 3rem;
    color: #666;
  }

  .loading-indicator i, .error-message i, .no-bookings i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
  }

  .loading-indicator i { color: #42a5f5; }
  .error-message i { color: #f44336; }
  .no-bookings i { color: #ccc; }

  .retry-button {
    display: inline-block;
    margin-top: 1rem;
    padding: 0.75rem 1.5rem;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: transform 0.3s ease;
    font-size: 0.9rem;
  }
  .retry-button:hover { transform: translateY(-2px); background: #e53935; }
  .retry-button i { font-size: 0.9rem !important; margin-right: 0.5rem; margin-bottom: 0 !important; }

  .table-container {
    overflow-x: auto;
    margin-bottom: 25px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }

  table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
    font-size: 14px;
    min-width: 1000px;
  }

  th, td {
    padding: 14px 16px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
  }

  th {
    background-color: #e9ecef;
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
  }

  tr:last-child td { border-bottom: none; }
  tr:hover td { background-color: #f8f9fa; }

  .image-cell { width: 100px; }
  .tour-thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  .no-image {
    width: 80px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: #adb5bd;
    font-size: 12px;
    border-radius: 4px;
    border: 1px dashed #ced4da;
  }

  .customer-name, .hotel-name { font-weight: 500; color: #212529; }
  .customer-email, .hotel-location { font-size: 13px; color: #6c757d; }
  .price { font-weight: 500; color: #28a745; white-space: nowrap; }

  .status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
  }

  .status-pending {
    background-color: #fff8e1;
    color: #ff8f00;
  }

  .status-warning {
    background-color: #fff3cd;
    color: #856404;
  }

  .status-success {
    background-color: #d4edda;
    color: #155724;
  }

  .status-cancelled {
    background-color: #ffebee;
    color: #c62828;
  }

  .status-completed {
    background-color: #e0e7ff;
    color: #3730a3;
  }

  .actions-cell { white-space: nowrap; }
  .action-btn {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: all 0.2s ease;
    margin-right: 5px;
  }
  .action-btn:last-child { margin-right: 0; }
  .action-btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); }
  .action-btn.confirm { background-color: #28a745; }
  .action-btn.confirm:hover { background-color: #218838; }
  .action-btn.delete { background-color: #dc3545; }
  .action-btn.delete:hover { background-color: #c82333; }

  .pagination { display: flex; justify-content: center; align-items: center; gap: 10px; margin-top: 20px; }
  .pagination-btn {
    width: 36px;
    height: 36px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  .pagination-btn:hover:not(:disabled) { background-color: #e9ecef; border-color: #ced4da; }
  .pagination-btn:disabled { opacity: 0.5; cursor: not-allowed; }
  .page-info { font-size: 14px; color: #495057; margin: 0 10px; }
</style> -->
