<script>
    import axios from 'axios';
    import { goto } from '$app/navigation';
    let email = '';
    let message = '';
    let success = false;
    let loading = false;
  
    const handleSubmit = async () => {
      loading = true;
      message = '';
      try {
        const { data } = await axios.post('http://localhost:5000/api/forgot-password', { email });
        message = data.message || 'Vui lòng kiểm tra email để đặt lại mật khẩu.';
        success = true;
      } catch (err) {
        message = err.response?.data?.message || 'Đ<PERSON> x<PERSON>y ra lỗi, vui lòng thử lại!';
        success = false;
      } finally {
        loading = false;
      }
    };
</script>

<svelte:head>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</svelte:head>

<style>
/* <PERSON><PERSON><PERSON>ng nền cho trang */
.forgot-password-page {
  background: linear-gradient(135deg, #3494e6, #ec6ead);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  animation: fadeIn 1s ease-in-out forwards;
}

/* Form với hiệu ứng đẹp mắt */
.form-container {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  padding: 2.5rem 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 420px;
  opacity: 0;
  animation: formFadeIn 1s ease-in-out 0.5s forwards;
}

/* Tiêu đề */
h1 {
  text-align: center;
  color: #2c3e50;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  letter-spacing: 1px;
}

/* Text mô tả */
.description {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 2rem;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Các trường nhập liệu */
input {
  width: 100%;
  padding: 0.85rem 1.2rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease-in-out;
  box-sizing: border-box;
}

input:focus {
  outline: none;
  border-color: #3494e6;
  box-shadow: 0 0 8px rgba(52, 148, 230, 0.2);
}

/* Nút gửi */
button {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #3494e6, #3494e6);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 4px 12px rgba(52, 148, 230, 0.2);
}

button:hover {
  background: linear-gradient(135deg, #2980b9, #3494e6);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(52, 148, 230, 0.3);
}

button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Thông báo */
.message {
  text-align: center;
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.9rem;
}

.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Link quay lại */
.back-link {
  display: block;
  text-align: center;
  margin-top: 1.5rem;
  color: #3494e6;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease-in-out;
}

.back-link:hover {
  color: #2980b9;
  text-decoration: underline;
}

/* Các hiệu ứng animation */
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes formFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

<div class="forgot-password-page">
  <div class="form-container">
    <h1>Quên mật khẩu</h1>
    <p class="description">
      Vui lòng nhập địa chỉ email của bạn. Chúng tôi sẽ gửi hướng dẫn đặt lại mật khẩu đến email của bạn.
    </p>

    <form on:submit|preventDefault={handleSubmit}>
      {#if message}
        <div class="message {success ? 'success' : 'error'}">
          {message}
        </div>
      {/if}

      <input
        type="email"
        placeholder="Nhập địa chỉ email"
        bind:value={email}
        required
      />

      <button type="submit" disabled={loading}>
        {loading ? 'Đang xử lý...' : 'Gửi yêu cầu'}
      </button>
    </form>

    <a href="/login" class="back-link">Quay lại đăng nhập</a>
  </div>
</div>
