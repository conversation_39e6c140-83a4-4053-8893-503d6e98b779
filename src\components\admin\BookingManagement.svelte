<script lang="ts">
  import { onMount } from 'svelte';
  import { bookings, user } from '../../stores/userStore';

  interface Booking {
    ma_dat_tour: number;
    ma_tour: number;
    ma_nguoi_dung: number;
    ten_tour: string;
    ten_khach_hang?: string;
    email?: string;
    so_dien_thoai?: string;
    dia_diem: string;
    hinh_anh: string;
    ngay_bat_dau: string;
    ngay_ket_thuc: string;
    ngay_dat: string;
    so_nguoi: number;
    gia: number;
    tong_tien?: number;
    trang_thai: string;
    ghi_chu?: string;
  }

  let allBookings: Booking[] = [];
  let currentPage = 1;
  let itemsPerPage = 10;
  let searchQuery = '';
  let statusFilter = 'all';
  let isLoading = true;
  let error = '';

  function sortBookings(bookingsToSort: Booking[]): Booking[] {
    return [...bookingsToSort].sort((a, b) => a.ma_dat_tour - b.ma_dat_tour);
  }

  function getAdjustedBookingId(bookings: Booking[], currentBooking: Booking): number {
    if (bookings.length === 0) return 1;

    const minBookingId = Math.min(...bookings.map(b => b.ma_dat_tour));

    return currentBooking.ma_dat_tour - minBookingId + 1;
  }

  $: filteredBookings = sortBookings(allBookings.filter(booking => {
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch =
      (booking.ten_tour && booking.ten_tour.toLowerCase().includes(searchLower)) ||
      (booking.ten_khach_hang && booking.ten_khach_hang.toLowerCase().includes(searchLower)) ||
      (booking.email && booking.email.toLowerCase().includes(searchLower)) ||
      (booking.dia_diem && booking.dia_diem.toLowerCase().includes(searchLower)) ||
      (booking.ghi_chu && booking.ghi_chu.toLowerCase().includes(searchLower));

    const matchesStatus = statusFilter === 'all' || booking.trang_thai === statusFilter;

    return matchesSearch && matchesStatus;
  }));

  $: totalPages = Math.ceil(filteredBookings.length / itemsPerPage);
  $: startIndex = (currentPage - 1) * itemsPerPage;
  $: displayedBookings = filteredBookings.slice(startIndex, startIndex + itemsPerPage);

  onMount(() => {
    loadAllBookings();

    const unsubscribe = bookings.subscribe(updatedBookings => {
      allBookings = updatedBookings;

      if ($user) {
        allBookings = allBookings.map(booking => {
          if (booking.ma_nguoi_dung === $user.ma_nguoi_dung) {
            return {
              ...booking,
              ten_khach_hang: booking.ten_khach_hang || $user.ho_ten || '',
              email: booking.email || $user.email || '',
              so_dien_thoai: booking.so_dien_thoai || $user.so_dien_thoai || ''
            };
          }
          return booking;
        });
      }
    });

    return unsubscribe;
  });

  async function loadAllBookings() {
    isLoading = true;
    error = '';
    console.log('Đang tải danh sách đặt tour...');

    try {
      console.log('Kiểm tra dữ liệu từ store:', $bookings.length, 'bookings');
      allBookings = $bookings;

      if (allBookings.length === 0) {
        console.log('Không có dữ liệu trong store, tải từ API');
        const url = 'http://localhost:5000/api/bookings';
        console.log(`Gửi request đến: ${url}`);

        const response = await fetch(url);
        console.log(`Nhận response với status: ${response.status}`);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: response.statusText }));
          throw new Error(errorData.message || `Lỗi ${response.status}: Không thể tải danh sách đặt tour`);
        }

        const data = await response.json();
        console.log('Dữ liệu nhận được từ API:', data);

        allBookings = data.bookings || [];
        console.log(`Đã tải ${allBookings.length} bookings từ API`);

        bookings.set(allBookings);
      }

      if ($user) {
        console.log('Cập nhật thông tin người dùng cho bookings');
        allBookings = allBookings.map(booking => {
          if (booking.ma_nguoi_dung === $user.ma_nguoi_dung) {
            return {
              ...booking,
              ten_khach_hang: booking.ten_khach_hang || $user.ho_ten || '',
              email: booking.email || $user.email || '',
              so_dien_thoai: booking.so_dien_thoai || $user.so_dien_thoai || ''
            };
          }
          return booking;
        });
      }

      console.log('Tải danh sách đặt tour thành công:', allBookings.length, 'bookings');
    } catch (err) {
      console.error('Lỗi khi tải danh sách đặt tour:', err);
      error = `Không thể tải danh sách đặt tour: ${err.message}`;
    } finally {
      isLoading = false;
    }
  }

  function formatPrice(price: number): string {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' VNĐ';
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('vi-VN');
  }

  function getStatusText(status: string): string {
    switch (status) {
      case 'cho_duyet': return 'Chờ duyệt';
      case 'chua_thanh_toan': return 'Chưa thanh toán';
      case 'da_xac_nhan': return 'Đã xác nhận';
      case 'da_thanh_toan': return 'Đã thanh toán';
      case 'da_huy': return 'Đã hủy';
      default: return status;
    }
  }

  function getStatusClass(status: string): string {
    switch (status) {
      case 'cho_duyet': return 'pending';
      case 'chua_thanh_toan': return 'status-warning';
      case 'da_xac_nhan': return 'confirmed';
      case 'da_thanh_toan': return 'status-success';
      case 'da_huy': return 'cancelled';
      default: return '';
    }
  }

  function getFilenameFromPath(path: string): string {
    if (!path) return '';
    const parts = path.split(/[\/\\]/);
    return parts[parts.length - 1];
  }

  function formatImageUrl(imageUrl: string | undefined): string {
    if (!imageUrl) return '#';

    if (imageUrl.startsWith('data:image')) {
      return imageUrl;
    }
    else if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    else if (imageUrl.startsWith('/')) {
      if (imageUrl.includes('static/images/') || imageUrl.includes('images/')) {
        const filename = getFilenameFromPath(imageUrl);
        return `/images/${filename}`;
      } else {
        return `http://localhost:5000${imageUrl}`;
      }
    }
    else {
      if (imageUrl.includes('static/images/') || imageUrl.includes('images/')) {
        const filename = getFilenameFromPath(imageUrl);
        return `/images/${filename}`;
      } else {
        return `/images/${getFilenameFromPath(imageUrl)}`;
      }
    }
  }

  async function deleteBooking(booking: Booking) {
    if (confirm('Bạn có chắc chắn muốn xóa đặt tour này không?')) {
      try {
        console.log(`Xóa booking ID ${booking.ma_dat_tour}`);

        error = '';
        isLoading = true;

        const url = `http://localhost:5000/api/bookings/${booking.ma_dat_tour}`;
        console.log(`Gửi request đến: ${url}`);

        const response = await fetch(url, {
          method: 'DELETE'
        });

        console.log(`Nhận response với status: ${response.status}`);

        let responseData: any = null;
        try {
          responseData = await response.json();
          console.log('Response data:', responseData);
        } catch (jsonError) {
          console.log('Không thể parse response JSON:', jsonError);
        }

        if (!response.ok) {
          const errorMessage = responseData?.message || `Lỗi ${response.status}: Không thể xóa đặt tour`;
          throw new Error(errorMessage);
        }

        bookings.update(currentBookings =>
          currentBookings.filter((b: Booking) => b.ma_dat_tour !== booking.ma_dat_tour)
        );

        allBookings = $bookings;

        alert('Đã xóa đặt tour thành công');

      } catch (err) {
        console.error('Lỗi khi xóa đặt tour:', err);
        error = `Không thể xóa đặt tour: ${err.message}`;
        alert(error);
      } finally {
        isLoading = false;
      }
    }
  }

  async function updateBookingStatus(booking: Booking, newStatus: string) {
    try {
      console.log(`Cập nhật trạng thái cho booking ID ${booking.ma_dat_tour} thành ${newStatus}`);

      error = '';
      isLoading = true;

      const url = `http://localhost:5000/api/bookings/${booking.ma_dat_tour}`;
      console.log(`Gửi request đến: ${url}`);

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          trang_thai: newStatus,
          ma_dat_tour: booking.ma_dat_tour,
          ma_tour: booking.ma_tour,
          ma_nguoi_dung: booking.ma_nguoi_dung,
          so_nguoi: booking.so_nguoi,
          ghi_chu: booking.ghi_chu || null
        })
      });

      console.log(`Nhận response với status: ${response.status}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(errorData.message || `Lỗi ${response.status}: Không thể cập nhật trạng thái đặt tour`);
      }

      bookings.update(currentBookings =>
        currentBookings.map((b: Booking) =>
          b.ma_dat_tour === booking.ma_dat_tour
            ? { ...b, trang_thai: newStatus }
            : b
        )
      );

      allBookings = $bookings;

      alert(`Đã cập nhật trạng thái thành ${getStatusText(newStatus)}`);

      console.log(`Đã cập nhật trạng thái booking ID ${booking.ma_dat_tour} thành ${newStatus} trong cơ sở dữ liệu và store`);

      const bookingStatusChangedEvent = new CustomEvent('bookingStatusChanged', {
        detail: {
          bookingId: booking.ma_dat_tour,
          newStatus: newStatus
        },
        bubbles: true,
        composed: true
      });
      document.dispatchEvent(bookingStatusChangedEvent);

    } catch (err) {
      console.error('Lỗi khi cập nhật trạng thái đặt tour:', err);
      error = `Không thể cập nhật trạng thái đặt tour: ${err.message}`;
      alert(error);
    } finally {
      isLoading = false;
    }
  }

  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
    }
  }
</script>

<div class="booking-management">
  <div class="actions">
    <div class="filters">
      <div class="status-filter">
        <label for="status-select">Lọc theo trạng thái:</label>
        <select id="status-select" bind:value={statusFilter}>
          <option value="all">Tất cả</option>
          <option value="cho_duyet">Chờ duyệt</option>
          <option value="chua_thanh_toan">Chưa thanh toán</option>
          <option value="da_xac_nhan">Đã xác nhận</option>
          <option value="da_thanh_toan">Đã thanh toán</option>
          <option value="da_huy">Đã hủy</option>
        </select>
      </div>
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="search"
          placeholder="Tìm kiếm theo tên tour, khách hàng, email..."
          bind:value={searchQuery}
          aria-label="Tìm kiếm đặt tour"
        >
      </div>
    </div>
  </div>

  {#if isLoading}
    <div class="loading-indicator">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Đang tải dữ liệu đặt tour...</p>
    </div>
  {:else if error}
    <div class="error-message">
      <i class="fas fa-exclamation-circle"></i>
      <p>{error}</p>
      <button on:click={loadAllBookings} class="retry-button">
        <i class="fas fa-redo"></i> Thử lại
      </button>
    </div>
  {:else if allBookings.length === 0}
    <div class="no-bookings">
      <i class="fas fa-calendar-times"></i>
      <p>Chưa có đặt tour nào</p>
    </div>
  {:else}
    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Mã đặt</th>
            <th>Hình ảnh</th>
            <th>Tên khách hàng</th>
            <th>Email</th>
            <th>Số điện thoại</th>
            <th>Tên tour</th>
            <th>Địa điểm</th>
            <th>Ngày tour</th>
            <th>Ngày đặt</th>
            <th>Số người</th>
            <th>Tổng tiền</th>
            <th>Trạng thái</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {#each displayedBookings as booking (booking.ma_dat_tour)}
            <tr>
              <td>#{getAdjustedBookingId(allBookings, booking)}</td>
              <td class="image-cell">
                {#if booking.hinh_anh}
                  <img
                    src={formatImageUrl(booking.hinh_anh)}
                    alt={booking.ten_tour}
                    class="tour-thumbnail"
                    loading="lazy"
                    on:error={(e) => {
                      console.error(`Image failed to load: ${booking.hinh_anh}`);
                      const imgElement = e.target as HTMLImageElement;

                      // Skip fallback for Base64 images
                      if (booking.hinh_anh.startsWith('data:image')) {
                        console.log('Base64 image failed to load, showing error message');
                        imgElement.style.display = 'none';
                        if (imgElement.nextElementSibling instanceof HTMLElement) {
                          imgElement.nextElementSibling.style.display = 'flex';
                        }
                      }
                      // Try static/images path for images that might be in the static folder
                      else if (booking.hinh_anh.includes('static/images/') || booking.hinh_anh.includes('images/')) {
                        const filename = getFilenameFromPath(booking.hinh_anh);
                        const staticUrl = `/images/${filename}`;
                        console.log('Trying static images URL:', staticUrl);

                        // Set a new error handler for the static URL attempt
                        imgElement.onerror = () => {
                          console.error(`Static URL also failed: ${staticUrl}`);

                          // Try backend URL as a last resort
                          const backendUrl = `http://localhost:5000${booking.hinh_anh.startsWith('/') ? '' : '/'}${booking.hinh_anh}`;
                          console.log('Trying backend URL as last resort:', backendUrl);

                          // Set final error handler
                          imgElement.onerror = () => {
                            console.error(`All URLs failed for image: ${booking.hinh_anh}`);
                            imgElement.style.display = 'none';
                            if (imgElement.nextElementSibling instanceof HTMLElement) {
                              imgElement.nextElementSibling.style.display = 'flex';
                            }
                          };

                          // Try the backend URL
                          imgElement.src = backendUrl;
                        };

                        // Try the static URL
                        imgElement.src = staticUrl;
                      }
                      // Try backend URL as fallback for non-http, non-base64 images
                      else if (!booking.hinh_anh.startsWith('http')) {
                        const backendUrl = `http://localhost:5000${booking.hinh_anh.startsWith('/') ? '' : '/'}${booking.hinh_anh}`;
                        console.log('Trying backend URL:', backendUrl);

                        // Set a new error handler for the backend URL attempt
                        imgElement.onerror = () => {
                          console.error(`Backend URL failed: ${backendUrl}`);

                          // Try direct static path as last resort
                          const staticUrl = `/images/${getFilenameFromPath(booking.hinh_anh)}`;
                          console.log('Trying static path as last resort:', staticUrl);

                          // Set final error handler
                          imgElement.onerror = () => {
                            console.error(`All URLs failed for image: ${booking.hinh_anh}`);
                            imgElement.style.display = 'none';
                            if (imgElement.nextElementSibling instanceof HTMLElement) {
                              imgElement.nextElementSibling.style.display = 'flex';
                            }
                          };

                          // Try the static URL
                          imgElement.src = staticUrl;
                        };

                        // Try the backend URL
                        imgElement.src = backendUrl;
                      } else {
                        // If already using a full URL, try static path as fallback
                        const staticUrl = `/images/${getFilenameFromPath(booking.hinh_anh)}`;
                        console.log('Full URL failed, trying static path:', staticUrl);

                        // Set error handler for static path attempt
                        imgElement.onerror = () => {
                          console.error(`Static path also failed: ${staticUrl}`);
                          imgElement.style.display = 'none';
                          if (imgElement.nextElementSibling instanceof HTMLElement) {
                            imgElement.nextElementSibling.style.display = 'flex';
                          }
                        };

                        // Try the static URL
                        imgElement.src = staticUrl;
                      }
                    }}
                  />
                  <div class="no-image" style="display: none;">Ảnh lỗi</div>
                {:else}
                  <div class="no-image">Không có ảnh</div>
                {/if}
              </td>
              <td class="customer-name">{booking.ten_khach_hang || 'Không có tên'}</td>
              <td class="customer-email">{booking.email || 'Không có email'}</td>
              <td class="customer-phone">{booking.so_dien_thoai || 'Không có SĐT'}</td>
              <td class="tour-name">{booking.ten_tour}</td>
              <td class="tour-location">{booking.dia_diem}</td>
              <td class="tour-dates">
                {formatDate(booking.ngay_bat_dau)} - {formatDate(booking.ngay_ket_thuc)}
              </td>
              <td>{formatDate(booking.ngay_dat)}</td>
              <td class="people-count">{booking.so_nguoi}</td>
              <td class="price">
                {#if booking.tong_tien !== undefined && booking.tong_tien !== null && !isNaN(booking.tong_tien)}
                  {formatPrice(booking.tong_tien)}
                {:else if booking.gia && booking.so_nguoi && !isNaN(booking.gia) && !isNaN(booking.so_nguoi)}
                  {formatPrice(booking.gia * booking.so_nguoi)}
                {:else}
                  {formatPrice(0)}
                {/if}
              </td>
              <td>
                <span class="status-badge {getStatusClass(booking.trang_thai)}">
                  {getStatusText(booking.trang_thai)}
                </span>
              </td>
              <td class="actions-cell">
                {#if booking.trang_thai === 'cho_duyet'}
                  <button
                    class="action-btn approve"
                    aria-label="Xác nhận đặt tour"
                    title="Xác nhận đặt tour"
                    on:click={() => updateBookingStatus(booking, 'da_xac_nhan')}
                  >
                    <i class="fas fa-check"></i>
                  </button>
                  <button
                    class="action-btn reject"
                    aria-label="Hủy đặt tour"
                    title="Hủy đặt tour"
                    on:click={() => updateBookingStatus(booking, 'da_huy')}
                  >
                    <i class="fas fa-times"></i>
                  </button>
                {:else if booking.trang_thai === 'da_xac_nhan'}
                  <button
                    class="action-btn cancel-confirm"
                    aria-label="Hủy xác nhận"
                    title="Hủy xác nhận (quay lại trạng thái chờ duyệt)"
                    on:click={() => updateBookingStatus(booking, 'cho_duyet')}
                  >
                    <i class="fas fa-undo"></i>
                  </button>
                {/if}
                <button
                  class="action-btn delete"
                  aria-label="Xóa đặt tour"
                  title="Xóa đặt tour"
                  on:click={() => deleteBooking(booking)}
                >
                  <i class="fas fa-trash"></i>
                </button>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>

    {#if totalPages > 1}
      <div class="pagination">
        <button
          class="pagination-btn"
          disabled={currentPage === 1}
          on:click={() => goToPage(1)}
          aria-label="Trang đầu"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button
          class="pagination-btn"
          disabled={currentPage === 1}
          on:click={() => goToPage(currentPage - 1)}
          aria-label="Trang trước"
        >
          <i class="fas fa-angle-left"></i>
        </button>

        <span class="page-info">Trang {currentPage} / {totalPages}</span>

        <button
          class="pagination-btn"
          disabled={currentPage === totalPages}
          on:click={() => goToPage(currentPage + 1)}
          aria-label="Trang sau"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button
          class="pagination-btn"
          disabled={currentPage === totalPages}
          on:click={() => goToPage(totalPages)}
          aria-label="Trang cuối"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
    {/if}
  {/if}
</div>

<style>
  /* General Styles */
  .booking-management {
    padding: 30px;
    background-color: #f8f9fa;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 100%;
    overflow: hidden;
  }

  .actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
  }

  .filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    flex-grow: 1;
  }

  .status-filter {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .status-filter label {
    font-weight: 500;
    color: #495057;
  }

  .status-filter select {
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    background-color: white;
    font-size: 14px;
    color: #495057;
    cursor: pointer;
  }

  .search-box {
    position: relative;
    flex-grow: 1;
    max-width: 400px;
  }

  .search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
  }

  .search-box input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }

  .search-box input:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  /* Loading and Error States */
  .loading-indicator, .error-message, .no-bookings {
    text-align: center;
    padding: 3rem;
    color: #666;
  }

  .loading-indicator i, .error-message i, .no-bookings i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
  }

  .loading-indicator i {
    color: #42a5f5;
  }

  .error-message i {
    color: #f44336;
  }

  .no-bookings i {
    color: #ccc;
  }

  .retry-button {
    display: inline-block;
    margin-top: 1rem;
    padding: 0.75rem 1.5rem;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: transform 0.3s ease;
    font-size: 0.9rem;
  }

  .retry-button:hover {
    transform: translateY(-2px);
    background: #e53935;
  }

  .retry-button i {
    font-size: 0.9rem !important;
    margin-right: 0.5rem;
    margin-bottom: 0 !important;
  }

  /* Table Styles */
  .table-container {
    overflow-x: auto;
    margin-bottom: 25px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }

  table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
    font-size: 14px;
  }

  th, td {
    padding: 14px 16px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
  }

  th {
    background-color: #e9ecef;
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
  }

  tr:last-child td {
    border-bottom: none;
  }

  tr:hover td {
    background-color: #f8f9fa;
  }

  .image-cell {
    width: 100px;
  }

  .tour-thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .no-image {
    width: 80px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: #adb5bd;
    font-size: 12px;
    border-radius: 4px;
    border: 1px dashed #ced4da;
  }

  .customer-info, .tour-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .customer-name, .tour-name {
    font-weight: 500;
    color: #212529;
  }

  .customer-email, .customer-phone, .tour-location {
    font-size: 13px;
    color: #6c757d;
  }

  .tour-dates, .people-count {
    white-space: nowrap;
  }

  .price {
    font-weight: 500;
    color: #28a745;
    min-width: 150px;
    display: inline-block;
    text-align: right;
    padding-right: 20px;
    margin-top: -50px;
    margin-left: -30px;
  }

  .status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
  }

  .status-badge.confirmed {
    background-color: #d4edda;
    color: #155724;
  }

  .status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
  }

  .status-badge.cancelled {
    background-color: #f8d7da;
    color: #721c24;
  }

  .actions-cell {
    white-space: nowrap;
    display: flex;
    gap: 8px;
  }

  .action-btn {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: all 0.2s ease;
    margin-top: 40px;
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .action-btn.approve {
    background-color: #28a745;
  }

  .action-btn.approve:hover {
    background-color: #218838;
  }

  .action-btn.reject {
    background-color: #ffc107;
    color: #212529;
  }

  .action-btn.reject:hover {
    background-color: #e0a800;
  }

  .action-btn.delete {
    background-color: #dc3545;
  }

  .action-btn.delete:hover {
    background-color: #c82333;
  }

  .action-btn.cancel-confirm {
    background-color: #6c757d;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .action-btn.cancel-confirm:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
  }

  .action-btn.cancel-confirm::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
  }

  .action-btn.cancel-confirm:hover::before {
    left: 100%;
  }

  /* Pagination */
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
  }

  .pagination-btn {
    width: 36px;
    height: 36px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .pagination-btn:hover:not(:disabled) {
    background-color: #e9ecef;
    border-color: #ced4da;
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .page-info {
    font-size: 14px;
    color: #495057;
    margin: 0 10px;
  }
</style>