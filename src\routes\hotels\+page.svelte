<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import Navbar from '../../components/Navbar.svelte';
  import Footer from '../../components/Footer.svelte';
  import CTA from '../../components/CTA.svelte';

  // Define hotel interface
  interface Hotel {
    ma_khach_san: number;
    ten_khach_san: string;
    dia_diem: string;
    dia_chi: string;
    so_dien_thoai?: string;
    so_sao: number;
    gia?: number;
    danh_gia_trung_binh?: number;
    mo_ta?: string;
    hinh_anh?: string;
  }

  // State variables
  let hotels: Hotel[] = [];
  let filteredHotels: Hotel[] = [];
  let isLoading = true;
  let error: string | null = null;

  // Filter variables
  let searchQuery = '';
  let selectedLocation = '';
  let minPrice = '';
  let maxPrice = '';
  let selectedStars = '';

  // Locations for filter dropdown
  let locations: string[] = [];

  // Apply filters to hotels
  function applyFilters() {
    filteredHotels = hotels.filter(hotel => {
      // Search query filter (name, location, address)
      const matchesSearch = searchQuery === '' ||
        hotel.ten_khach_san.toLowerCase().includes(searchQuery.toLowerCase()) ||
        hotel.dia_diem.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (hotel.dia_chi && hotel.dia_chi.toLowerCase().includes(searchQuery.toLowerCase()));

      // Location filter
      const matchesLocation = selectedLocation === '' || hotel.dia_diem === selectedLocation;

      // Price range filter
      const minPriceValue = minPrice ? parseInt(minPrice) : 0;
      const maxPriceValue = maxPrice ? parseInt(maxPrice) : Infinity;
      const matchesPrice = !hotel.gia || (hotel.gia >= minPriceValue && hotel.gia <= maxPriceValue);

      // Star rating filter
      const matchesStars = selectedStars === '' || hotel.so_sao === parseInt(selectedStars);

      return matchesSearch && matchesLocation && matchesPrice && matchesStars;
    });
  }

  // Reset all filters
  function resetFilters() {
    searchQuery = '';
    selectedLocation = '';
    minPrice = '';
    maxPrice = '';
    selectedStars = '';
    filteredHotels = [...hotels];
  }

  // Format price to VND
  function formatPrice(price: number | undefined): string {
    if (!price) return 'Liên hệ';
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  }

  // Format star rating
  function formatStars(stars: number): string {
    if (!stars || stars < 1) return '';
    return '<i class="fas fa-star star-icon"></i>'.repeat(Math.min(stars, 5));
  }

  // Format image URL
  function formatImageUrl(imageUrl: string | undefined): string {
    if (!imageUrl) return '/images/vidu.jpg';
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    if (imageUrl.startsWith('/uploads')) {
      return `http://localhost:5000${imageUrl}`;
    }
    return `/images/${imageUrl}`;
  }

  // Navigate to hotel detail page
  function viewHotelDetails(hotelId: number): void {
    goto(`/hotels_new/${hotelId}`);
  }

  // Fetch hotels from API
  async function fetchHotels() {
    isLoading = true;
    error = null;
    try {
      const response = await fetch('http://localhost:5000/api/khachsan');
      if (!response.ok) {
        throw new Error(`Lỗi ${response.status}: Không thể tải danh sách khách sạn.`);
      }
      const data = await response.json();
      hotels = data.khachsan || [];
      filteredHotels = [...hotels];

      // Extract unique locations for filter dropdown
      const uniqueLocations = new Set<string>();
      hotels.forEach(hotel => {
        if (hotel.dia_diem) {
          uniqueLocations.add(hotel.dia_diem);
        }
      });
      locations = Array.from(uniqueLocations).sort();

      isLoading = false;
    } catch (err) {
      console.error('Lỗi khi tải danh sách khách sạn:', err);
      error = err.message;
      isLoading = false;
    }
  }

  // Initialize component
  onMount(() => {
    fetchHotels();
  });

  // Watch for filter changes
  $: {
    if (hotels.length > 0) {
      applyFilters();
    }
  }
</script>

<svelte:head>
  <title>Danh sách Khách sạn | Travel Agency</title>
  <meta name="description" content="Khám phá và đặt phòng tại các khách sạn tuyệt vời với giá cả phải chăng." />
  <style>
    .star-icon {
      color: #ffcc00 !important;
      margin: 0 1px !important;
      font-size: 0.95rem !important;
      filter: drop-shadow(0 0 2px rgba(255, 204, 0, 0.5)) !important;
      transition: all 0.3s ease !important;
    }

    .hotel-card:hover .star-icon {
      transform: rotate(0deg) scale(1.1) !important;
      animation: starTwinkle 1.5s infinite alternate !important;
    }

    .hotel-card:nth-child(2n) .star-icon {
      animation-delay: 0.2s !important;
    }

    .hotel-card:nth-child(3n) .star-icon {
      animation-delay: 0.4s !important;
    }

    @keyframes starTwinkle {
      0% {
        opacity: 0.8;
        transform: scale(1);
        filter: drop-shadow(0 0 2px rgba(255, 204, 0, 0.5));
      }
      100% {
        opacity: 1;
        transform: scale(1.2);
        filter: drop-shadow(0 0 5px rgba(255, 204, 0, 0.8));
      }
    }
  </style>
</svelte:head>

<Navbar />

<div class="hotels-page">
  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-overlay"></div>
    <div class="hero-content">
      <h1>Khách sạn Tuyệt vời</h1>
      <p>Khám phá và đặt phòng tại các khách sạn tuyệt vời với giá cả phải chăng.</p>
    </div>
  </section>

  <!-- Main Content -->
  <div class="hotels-container">
    <div class="hotels-layout">
      <!-- Filter Section - Left Side -->
      <section class="filter-section">
        <h2>Tìm kiếm Khách sạn</h2>
        <form class="filter-form" on:submit|preventDefault>
          <div class="form-group">
            <label for="search">Tìm kiếm</label>
            <div class="search-input">
              <!-- <i class="fas fa-search"></i> -->
              <input
                type="text"
                id="search"
                placeholder="Tên khách sạn, địa điểm..."
                bind:value={searchQuery}
              />
            </div>
          </div>

          <div class="form-group">
            <label for="location">Địa điểm</label>
            <select id="location" bind:value={selectedLocation}>
              <option value="">Tất cả địa điểm</option>
              {#each locations as location}
                <option value={location}>{location}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="price-min">Giá từ</label>
            <input
              type="number"
              id="price-min"
              placeholder="VNĐ"
              bind:value={minPrice}
              min="0"
              step="100000"
            />
          </div>

          <div class="form-group">
            <label for="price-max">Đến</label>
            <input
              type="number"
              id="price-max"
              placeholder="VNĐ"
              bind:value={maxPrice}
              min="0"
              step="100000"
            />
          </div>

          <div class="form-group">
            <label for="stars">Xếp hạng sao</label>
            <select id="stars" bind:value={selectedStars}>
              <option value="">Tất cả xếp hạng</option>
              <option value="5">5 sao</option>
              <option value="4">4 sao</option>
              <option value="3">3 sao</option>
              <option value="2">2 sao</option>
              <option value="1">1 sao</option>
            </select>
          </div>

          <button type="button" class="reset-btn" on:click={resetFilters}>
            <i class="fas fa-sync-alt"></i> Đặt lại
          </button>
        </form>
      </section>

      <!-- Hotels List Section - Right Side -->
      <section class="hotels-list-section">
        <h2>Danh sách Khách sạn</h2>

        {#if isLoading}
          <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Đang tải danh sách khách sạn...</p>
          </div>
        {:else if error}
          <div class="error-message">
            <i class="fas fa-exclamation-circle"></i>
            <p>{error}</p>
            <button on:click={fetchHotels}>Thử lại</button>
          </div>
        {:else if filteredHotels.length === 0}
          <div class="no-results">
            <i class="fas fa-search"></i>
            <p>Không tìm thấy khách sạn nào phù hợp với tiêu chí tìm kiếm.</p>
            <button on:click={resetFilters}>Đặt lại bộ lọc</button>
          </div>
        {:else}
          <div class="hotels-count">
            <span>Hiển thị {filteredHotels.length} khách sạn</span>
          </div>
          <div class="hotels-grid">
            {#each filteredHotels as hotel, index (hotel.ma_khach_san)}
              <div
                class="hotel-card"
                data-aos="fade-up"
                data-aos-delay={index * 100}
                role="button"
                tabindex="0"
                on:click={() => viewHotelDetails(hotel.ma_khach_san)}
                on:keydown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    viewHotelDetails(hotel.ma_khach_san);
                  }
                }}
                aria-label="Xem chi tiết khách sạn {hotel.ten_khach_san}"
              >
                <div class="hotel-image">
                  <img
                    src={formatImageUrl(hotel.hinh_anh)}
                    alt={hotel.ten_khach_san}
                  />
                  <div class="hotel-rating">
                    <span>{@html formatStars(hotel.so_sao)}</span>
                  </div>
                </div>
                <div class="hotel-info">
                  <h3 class="hotel-title">{hotel.ten_khach_san}</h3>
                  <p class="hotel-location">
                    <i class="fas fa-map-marker-alt"></i> {hotel.dia_diem}
                  </p>
                  <p class="hotel-address">{hotel.dia_chi}</p>
                  {#if hotel.danh_gia_trung_binh}
                    <div class="hotel-review">
                      <span class="review-score">{hotel.danh_gia_trung_binh.toFixed(1)}</span>
                      <span class="review-text">Rất tốt</span>
                    </div>
                  {/if}
                  <div class="hotel-price">
                    <span class="price-label">Giá mỗi đêm từ</span>
                    <span class="price-value">{formatPrice(hotel.gia)}</span>
                  </div>
                  <button class="view-details-btn">
                    Xem chi tiết <i class="fas fa-arrow-right"></i>
                  </button>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </section>
    </div>
  </div>
</div>

<CTA />
<Footer />

<style>
  .hero-section {
    position: relative;
    height: 75vh;
    background-image: url('/images/hotel-banner.jpg');
    background-size: cover;
    background-position: center;
    margin-top: 84px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    background-attachment: fixed;
    overflow: hidden;
  }

  .hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(41, 128, 185, 0.7) 0%, rgba(44, 62, 80, 0.8) 100%);
    z-index: 1;
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, transparent 0%, rgba(0, 0, 0, 0.4) 100%);
    z-index: 2;
  }

  .hero-content {
    position: relative;
    z-index: 3;
    max-width: 900px;
    padding: 0 20px;
    animation: fadeInUp 1s ease-out;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .hero-content h1 {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    font-weight: 700;
    letter-spacing: 1px;
  }

  .hero-content p {
    font-size: 1.4rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }

  .hotels-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 20px;
  }

  .hotels-layout {
    display: grid;
    grid-template-columns: 1.2fr 2.8fr;
    gap: 30px;
  }

  .hotels-count {
    background: #f8fafc;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
    color: #4a5568;
    border-left: 4px solid #4a90e2;
  }

  .filter-section {
    background: #ffffff;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 100px;
    height: fit-content;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    width: 100%;
    background-image: linear-gradient(135deg, #f8f9fa, #ffffff);
    overflow: hidden;
    border: 1px solid rgba(226, 232, 240, 0.8);
  }

  .filter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #1abc9c, #3498db, #9b59b6);
    opacity: 0.8;
  }

  .filter-section::after {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(26, 188, 156, 0.05) 0%, rgba(26, 188, 156, 0) 70%);
    border-radius: 50%;
    z-index: 0;
  }

  .filter-section:hover {
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }

  .filter-section h2 {
    margin-top: 0;
    margin-bottom: 2.2rem;
    font-size: 1.9rem;
    color: #2c3e50;
    position: relative;
    padding-bottom: 15px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
    letter-spacing: 0.5px;
    z-index: 1;
  }

  .filter-section h2::before {
    content: '\f0b0';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: #1abc9c;
    font-size: 1.4rem;
    background: rgba(26, 188, 156, 0.1);
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .filter-section:hover h2::before {
    background: rgba(26, 188, 156, 0.2);
    transform: rotate(-10deg);
  }

  .filter-section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, #1abc9c, #3498db);
    border-radius: 3px;
    transition: width 0.3s ease;
  }

  .filter-section:hover h2::after {
    width: 120px;
  }

  .filter-form {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    align-items: end;
    width: 100%;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.2rem;
    position: relative;
    z-index: 1;
  }

  .form-group label {
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.05rem;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    letter-spacing: 0.3px;
  }

  .form-group:hover label {
    color: #1abc9c;
    transform: translateX(3px);
  }

  .form-group label::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #1abc9c;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 0 0 2px rgba(26, 188, 156, 0.2);
  }

  .form-group:hover label::before {
    transform: scale(1.3);
    background-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
  }

  .search-input {
    position: relative;
    margin-bottom: 1rem;
    overflow: hidden;
  }
  
  .search-input::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #1abc9c, #3498db, #9b59b6);
    background-size: 400% 400%;
    z-index: 0;
    border-radius: 16px;
    animation: gradientBorder 6s ease infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .search-input:hover::before,
  .search-input:focus-within::before {
    opacity: 1;
  }

  @keyframes gradientBorder {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* .search-input i {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #1abc9c;
    font-size: 1.3rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 2;
    text-shadow: 0 2px 10px rgba(26, 188, 156, 0.3);
  }

  .search-input:hover i {
    color: #3498db;
    transform: translateY(-50%) rotate(-10deg);
  }

  .search-input:focus-within i {
    left: 20px;
    color: #9b59b6;
    transform: translateY(-50%) scale(1.1);
  } */

  .search-input input {
    padding: 0 30px 0 60px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    height: 55px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
    width: 100%;
    position: relative;
    z-index: 1;
    letter-spacing: 0.5px;
    font-weight: 500;
  }

  .search-input input:focus {
    border-color: transparent;
    box-shadow: 0 15px 30px rgba(26, 188, 156, 0.15);
    background: white;
    outline: none;
    transform: translateY(-2px);
  }

  .search-input input::placeholder {
    color: #a0aec0;
    font-weight: 400;
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
  }

  .search-input input:focus::placeholder {
    opacity: 0.6;
    transform: translateX(10px);
    color: #1abc9c;
  }

  .form-group input,
  .form-group select {
    padding: 1rem 1.2rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1.05rem;
    width: 100%;
    box-sizing: border-box;
    transition: all 0.3s ease;
    background-color: #f8fafc;
    color: #2d3748;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.03);
    height: 55px;
    position: relative;
    z-index: 1;
    letter-spacing: 0.3px;
  }

  .form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%231abc9c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    padding-right: 2.5rem;
    cursor: pointer;
  }

  .form-group input:hover,
  .form-group select:hover {
    border-color: #1abc9c;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    transform: translateY(-3px);
    background-color: #f0fdf9;
  }

  .form-group input:focus,
  .form-group select:focus {
    outline: none;
    border-color: #1abc9c;
    box-shadow: 0 0 0 3px rgba(26, 188, 156, 0.2);
    background-color: #fff;
    transform: translateY(-3px);
  }

  .form-group input::placeholder {
    color: #a0aec0;
    transition: all 0.3s ease;
  }

  .form-group input:focus::placeholder {
    opacity: 0.7;
    transform: translateX(5px);
  }

  .reset-btn {
    background: linear-gradient(135deg, #1abc9c 0%, #3498db 100%);
    color: white;
    border: none;
    padding: 1.2rem 1.5rem;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 2.5rem;
    font-size: 1.1rem;
    box-shadow: 0 15px 30px rgba(26, 188, 156, 0.25);
    position: relative;
    overflow: hidden;
    z-index: 1;
    letter-spacing: 0.5px;
  }

  .reset-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 100%);
    transition: all 0.6s ease;
    z-index: -1;
  }

  .reset-btn::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.4);
    z-index: -1;
  }

  .reset-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(26, 188, 156, 0.4);
    background: linear-gradient(135deg, #16a085 0%, #2980b9 100%);
  }

  .reset-btn:hover::before {
    left: 100%;
  }

  .reset-btn:active {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(26, 188, 156, 0.4);
  }

  .reset-btn i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.2);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }

  .reset-btn:hover i {
    transform: rotate(360deg);
    color: white;
    background: rgba(255, 255, 255, 0.3);
  }

  .hotels-list-section {
    margin-top: 2rem;
  }

  .hotels-list-section h2 {
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: #333;
  }

  .hotels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .hotel-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .hotel-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  }

  .hotel-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(135deg, #4a90e2 0%, #63b3ed 100%);
    z-index: 1;
    transition: height 0.3s ease;
    opacity: 0.8;
  }

  .hotel-card:hover::before {
    height: 5px;
  }

  .hotel-image {
    position: relative;
    height: 220px;
    overflow: hidden;
  }

  .hotel-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s ease;
  }

  .hotel-card:hover .hotel-image img {
    transform: scale(1.1);
  }

  .hotel-rating {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 8px 15px;
    border-radius: 30px;
    font-size: 0.9rem;
    z-index: 2;
    backdrop-filter: blur(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: translateY(0);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .hotel-card:hover .hotel-rating {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    background: rgba(0, 0, 0, 0.7);
  }



  .hotel-info {
    padding: 1.8rem;
    position: relative;
  }

  .hotel-title {
    margin-top: 0;
    margin-bottom: 0.8rem;
    font-size: 1.4rem;
    color: #2d3748;
    font-weight: 600;
    line-height: 1.3;
    transition: color 0.3s;
    height: 3.6rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
  }

  .hotel-card:hover .hotel-title {
    color: #4a90e2;
  }

  .hotel-location {
    color: #4a5568;
    margin-bottom: 0.7rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
  }

  .hotel-location i {
    color: #4a90e2;
  }

  .hotel-address {
    color: #718096;
    font-size: 0.95rem;
    margin-bottom: 1.2rem;
    line-height: 1.5;
  }

  .hotel-review {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 1.2rem;
  }

  .review-score {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    color: white;
    padding: 4px 10px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
  }

  .review-text {
    color: #4a5568;
    font-size: 0.95rem;
    font-weight: 500;
  }

  .hotel-price {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
    position: relative;
    padding-top: 0.8rem;
  }

  .hotel-price::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: #e2e8f0;
  }

  .price-label {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 0.3rem;
  }

  .price-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: #38a169;
    letter-spacing: -0.5px;
  }

  .view-details-btn {
    background: linear-gradient(135deg, #4a90e2 0%, #3182ce 100%);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.25);
    position: relative;
    overflow: hidden;
    z-index: 1;
  }

  .view-details-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
    transition: all 0.6s ease;
    z-index: -1;
  }

  .view-details-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(74, 144, 226, 0.35);
  }

  .view-details-btn:hover::before {
    left: 100%;
  }

  .view-details-btn i {
    transition: transform 0.3s ease;
  }

  .view-details-btn:hover i {
    transform: translateX(4px);
  }

  .loading-spinner,
  .error-message,
  .no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4a90e2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-message i,
  .no-results i {
    font-size: 3rem;
    color: #ff6b6b;
    margin-bottom: 1rem;
  }

  .error-message p,
  .no-results p {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 1rem;
  }

  .error-message button,
  .no-results button {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    margin-top: 1rem;
    cursor: pointer;
    font-weight: 600;
  }

  @media (max-width: 1024px) {
    .hotels-layout {
      grid-template-columns: 1fr 2fr;
    }
  }

  @media (max-width: 768px) {
    .hero-content h1 {
      font-size: 2rem;
    }

    .hero-content p {
      font-size: 1rem;
    }

    .hotels-layout {
      grid-template-columns: 1fr;
    }

    .hotels-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }

    .filter-form {
      grid-template-columns: 1fr;
    }

    .filter-section {
      position: relative;
      top: 0;
      margin-bottom: 2rem;
    }
  }

  @media (max-width: 480px) {
    .hotels-grid {
      grid-template-columns: 1fr;
    }

    .hotel-image {
      height: 180px;
    }

    .hotel-title {
      font-size: 1.2rem;
      height: 3.1rem;
    }
  }
</style>