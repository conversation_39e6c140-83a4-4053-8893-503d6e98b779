<script>
  import { createEventDispatcher } from 'svelte';
  import { onMount } from 'svelte';

  const dispatch = createEventDispatcher();

  export let accountId; 

  let account = {
    ma_nguoi_dung: '',
    ho_ten: '',
    email: '',
    mat_khau: '', 
    so_dien_thoai: '',
    vai_tro: 'khach_hang'
  };

  let isLoading = false;
  let hasError = false;
  let errorMessage = '';

  onMount(async () => {
    console.log('EditAccountForm mounted with accountId:', accountId);
    if (accountId) {
      await fetchAccountDetails();
    } else {
      console.error('No accountId provided on mount');
    }
  });

  $: {
    if (accountId) {
      console.log('accountId changed to:', accountId);
      setTimeout(() => {
        fetchAccountDetails();
      }, 0);
    }
  }

  async function fetchAccountDetails() {
    console.log('fetchAccountDetails called with accountId:', accountId);

    if (!accountId) {
      console.error('Invalid accountId:', accountId);
      hasError = true;
      errorMessage = 'ID tài khoản không hợp lệ';
      alert(errorMessage);
      return;
    }

    isLoading = true;
    hasError = false;
    errorMessage = '';

    try {
      const url = `http://localhost:5000/api/account/users`;
      console.log('Fetching from URL:', url);

      const response = await fetch(url);
      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error('Không thể lấy thông tin tài khoản');
      }

      const data = await response.json();
      console.log('Received data:', data);

      let foundAccount = null;
      if (data && data.users && Array.isArray(data.users)) {
        foundAccount = data.users.find(user => user.ma_nguoi_dung == accountId);
        console.log('Found account data:', foundAccount);

        if (!foundAccount) {
          throw new Error('Không tìm thấy tài khoản với ID đã cho');
        }

        console.log('Extracted account data:', foundAccount);
      } else {
        console.error('Invalid data structure:', data);
        throw new Error('Cấu trúc dữ liệu không hợp lệ');
      }
      console.log('Updating account with data:', foundAccount);
      account = {
        ma_nguoi_dung: foundAccount.ma_nguoi_dung || '',
        ho_ten: foundAccount.ho_ten || '',
        email: foundAccount.email || '',
        mat_khau: '', 
        so_dien_thoai: foundAccount.so_dien_thoai || '',
        vai_tro: foundAccount.vai_tro || 'khach_hang'
      };

      console.log('Updated account:', account);
      isLoading = false;
    } catch (error) {
      console.error('Error fetching account details:', error);
      isLoading = false;
      hasError = true;
      errorMessage = 'Có lỗi khi lấy thông tin tài khoản: ' + error.message;

      alert(errorMessage);
    }
  }

  async function handleSubmit() {
    console.log('handleSubmit called with accountId:', accountId);
    console.log('Data to submit:', account);
    isLoading = true;
    hasError = false;
    errorMessage = '';

    try {
      const url = `http://localhost:5000/api/account/${accountId}`;
      console.log('Sending PUT request to:', url);
      const dataToSend = { ...account };
      const requestData = {
        ho_ten: dataToSend.ho_ten,
        email: dataToSend.email,
        so_dien_thoai: dataToSend.so_dien_thoai,
        vai_tro: dataToSend.vai_tro
      };

      if (dataToSend.mat_khau && dataToSend.mat_khau.trim() !== '') {
        requestData.mat_khau = dataToSend.mat_khau;
        console.log('Including password in request');
      } else {
        console.log('Password not included in request');
      }

      console.log('Data after formatting:', requestData);
      console.log('Sending data to server:', JSON.stringify(requestData));

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      console.log('PUT response status:', response.status);

      if (!response.ok) {
        let errorMessage = 'Không thể cập nhật thông tin tài khoản';

        try {
          const errorData = await response.json();
          console.error('Error response data:', errorData);

          if (errorData && errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData && errorData.error) {
            errorMessage = errorData.error;
          }
          console.error('Error details:', {
            status: response.status,
            statusText: response.statusText,
            errorData: errorData
          });
        } catch (parseError) {
          console.error('Error parsing error response:', parseError);
          try {
            const errorText = await response.text();
            console.error('Error response text:', errorText);

            if (errorText) {
              errorMessage = errorText;
            }
          } catch (textError) {
            console.error('Error getting error text:', textError);
          }
        }
        const detailedError = `Lỗi ${response.status}: ${errorMessage}`;
        console.error('Detailed error:', detailedError);

        throw new Error(detailedError);
      }

      isLoading = false;

      console.log('Dispatching accountUpdated event');
      dispatch('accountUpdated');

      alert('Cập nhật thông tin tài khoản thành công!');
    } catch (error) {
      console.error('Error updating account:', error);

      isLoading = false;
      hasError = true;
      errorMessage = 'Có lỗi khi cập nhật thông tin tài khoản: ' + error.message;

      alert(errorMessage);
    }
  }
</script>

<div class="edit-account-form">
  <h2>Sửa Thông Tin Tài Khoản</h2>

  {#if isLoading}
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Đang tải thông tin tài khoản...</p>
    </div>
  {:else if hasError}
    <div class="error-container">
      <p class="error-message">{errorMessage}</p>
      <button class="btn-retry" on:click={fetchAccountDetails}>Thử lại</button>
    </div>
  {:else}
    <form on:submit|preventDefault={handleSubmit}>
      <div class="form-grid">
        <div class="form-group">
          <label for="hoTen">Họ tên</label>
          <input
            id="hoTen"
            type="text"
            bind:value={account.ho_ten}
            placeholder="Nhập họ tên"
            required
          >
        </div>

        <div class="form-group">
          <label for="email">Email</label>
          <input
            id="email"
            type="email"
            bind:value={account.email}
            placeholder="Nhập email"
            required
          >
        </div>

        <div class="form-group">
          <label for="matKhau">Mật khẩu</label>
          <input
            id="matKhau"
            type="password"
            bind:value={account.mat_khau}
            placeholder="Nhập mật khẩu mới (để trống nếu không thay đổi)"
          >
        </div>

        <div class="form-group">
          <label for="soDienThoai">Số điện thoại</label>
          <input
            id="soDienThoai"
            type="tel"
            bind:value={account.so_dien_thoai}
            placeholder="Nhập số điện thoại"
            required
          >
        </div>

        <div class="form-group">
          <label for="vaiTro">Vai trò</label>
          <select
            id="vaiTro"
            bind:value={account.vai_tro}
            required
          >
            <option value="khach_hang">Khách hàng</option>
            <option value="admin">Quản trị viên</option>
          </select>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn-cancel" on:click={() => {
          console.log('Cancel button clicked');
          dispatch('cancel');
        }}>Hủy</button>
        <button type="submit" class="btn-save">Lưu Thay Đổi</button>
      </div>
    </form>
  {/if}
</div>

<style>
  .edit-account-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  /* Loading styles */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Error styles */
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
  }

  .error-message {
    color: #e53e3e;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  .btn-retry {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .btn-retry:hover {
    background-color: #45a049;
  }

  h2 {
    color: #2c3e50;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    font-weight: 600;
    text-align: center;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    color: #4a5568;
    font-weight: 500;
    font-size: 0.95rem;
  }

  input,
  select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: white;
  }

  input:hover,
  select:hover {
    border-color: #cbd5e0;
  }

  input:focus,
  select:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .btn-save,
  .btn-cancel {
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-save {
    background: #4CAF50;
    color: white;
    border: none;
  }

  .btn-save:hover {
    background: #45a049;
    transform: translateY(-1px);
  }

  .btn-cancel {
    background: white;
    border: 2px solid #e2e8f0;
    color: #4a5568;
  }

  .btn-cancel:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
  }

  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
