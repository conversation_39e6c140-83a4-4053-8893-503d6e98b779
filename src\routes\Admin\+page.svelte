<script lang="ts">
  import { writable } from 'svelte/store';
  import TourManagement from '../../components/admin/TourManagement.svelte';
  import AccountManagement from '../../components/admin/AccountManagement.svelte';
  import NhanVienManagement from '../../components/admin/NhanVienManagement.svelte';
  import DashboardManager from '../../components/admin/DashboardManager.svelte';
  import HotelManagement from '../../components/admin/HotelManagement.svelte';
  import BookingManagement from '../../components/admin/BookingManagement.svelte';
  import HotelBookingManager from '../../components/admin/HotelBookingManager.svelte';
  import PaymentManagement from '../../components/admin/PaymentManagement.svelte';
  import TourSuggestionManagement from '../../components/admin/TourSuggestionManagement.svelte';
  import ItineraryManagement from '../../components/admin/LichTrinhManagement.svelte';
  import NCCManagement from '../../components/admin/NCCManagement.svelte';

  const activeMenu = writable('dashboard');

  const menuItems = [
    { id: 'reports', label: 'Báo cáo & Thống kê', icon: 'fas fa-chart-line' },
    // { id: 'dashboard', label: 'Bảng điều khiển', icon: 'fas fa-tachometer-alt' },
    { id: 'tours', label: 'Quản lý Tour', icon: 'fas fa-map-marked-alt' },
    { id: 'itineraries', label: 'Quản lý Lịch trình', icon: 'fas fa-calendar-alt' },
    { id: 'tourSuggestions', label: 'Quản lý Tour gợi ý', icon: 'fas fa-lightbulb' },
    { id: 'hotels', label: 'Quản lý Khách sạn', icon: 'fas fa-hotel' },
    { id: 'suppliers', label: 'Quản lý Nhà cung cấp', icon: 'fas fa-truck' },
    { id: 'accounts', label: 'Quản lý Tài khoản', icon: 'fas fa-users' },
    { id: 'staff', label: 'Quản lý Nhân viên', icon: 'fas fa-user-tie' },
    { id: 'bookings', label: 'Quản lý Đặt tour', icon: 'fas fa-calendar-check' },
    { id: 'hotelBookings', label: 'Quản lý Đặt khách sạn', icon: 'fas fa-concierge-bell' },
    { id: 'payments', label: 'Quản lý Thanh toán', icon: 'fas fa-credit-card' },
    // { id: 'settings', label: 'Cài đặt', icon: 'fas fa-cog' }
  ];

  function handleMenuClick(menuId: string) {
    activeMenu.set(menuId);
  }
  function handleLogout() {
    window.location.href = '/'; 
  }
</script>
<div class="admin-container">
  <div class="sidebar">
    <div class="logo">
      <i class="fas fa-mountain logo-icon"></i>
      <h2>Tour Admin</h2>
    </div>
    <nav>
      {#each menuItems as item}
        <button
          class="menu-item"
          class:active={$activeMenu === item.id}
          on:click={() => handleMenuClick(item.id)}
        >
          <i class={item.icon}></i>
          <span>{item.label}</span>
        </button>
      {/each}
    </nav>
    <div class="user-section">
      <div class="user-info">
        <i class="fas fa-user-circle"></i>
        <span>Admin</span>
      </div>
      <button class="logout-btn" on:click={handleLogout} aria-label="Đăng xuất">
        <i class="fas fa-sign-out-alt"></i>
      </button>
    </div>
  </div>

  <div class="content">
    {#if $activeMenu === 'dashboard'}
      <DashboardManager />
    {:else if $activeMenu === 'tours'}
      <TourManagement />
    {:else if $activeMenu === 'itineraries'}
      <div class="content-header">
        <h2><i class="fas fa-calendar-alt"></i> Quản lý Lịch trình</h2>
      </div>
      <ItineraryManagement />
    {:else if $activeMenu === 'tourSuggestions'}
      <div class="content-header">
        <h2><i class="fas fa-lightbulb"></i> Quản lý Tour gợi ý</h2>
      </div>
      <TourSuggestionManagement />
    {:else if $activeMenu === 'hotels'}
      <div class="content-header">
        <h2><i class="fas fa-hotel"></i> Quản lý Khách sạn</h2>
      </div>
      <HotelManagement />
    {:else if $activeMenu === 'accounts'}
      <AccountManagement />
    {:else if $activeMenu === 'staff'}
      <div class="content-header">
        <h2><i class="fas fa-user-tie"></i> Quản lý Nhân viên</h2>
      </div>
      <NhanVienManagement />
    {:else if $activeMenu === 'bookings'}
      <div class="content-header">
        <h2><i class="fas fa-calendar-check"></i> Quản lý Đặt tour</h2>
      </div>
      <BookingManagement />
    {:else if $activeMenu === 'hotelBookings'}
      <div class="content-header">
        <h2><i class="fas fa-concierge-bell"></i> Quản lý Đặt khách sạn</h2>
      </div>
      <HotelBookingManager />
    {:else if $activeMenu === 'payments'}
      <div class="content-header">
        <h2><i class="fas fa-credit-card"></i> Quản lý Thanh toán</h2>
      </div>
      <PaymentManagement />
    {:else if $activeMenu === 'suppliers'}
      <div class="content-header">
        <h2><i class="fas fa-truck"></i> Quản lý Nhà cung cấp</h2>
      </div>
      <NCCManagement />
    {:else if $activeMenu === 'reports'}
      <DashboardManager />
    {:else if $activeMenu === 'settings'}
      <div class="content-header">
        <h2><i class="fas fa-cog"></i> Cài đặt</h2>
      </div>
    {/if}
  </div>
</div>

<style>
  .admin-container {
    display: flex;
    min-height: 100vh;
    width: 100%;
    background-color: #f8f9fa;
    position: relative;
  }

  .sidebar {
    width: 280px;
    min-width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #1a237e 0%, #303f9f 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
    position: fixed; 
    left: 0;
    top: 0;
    overflow-y: auto; 
    z-index: 100;
  }

  .logo {
    padding: 1rem 0;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: center;
  }

  .logo-icon {
    font-size: 2rem;
    color: #fff;
  }

  .logo h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    background: linear-gradient(120deg, #fff, #e3f2fd);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex-grow: 1;
    overflow-y: auto;
    max-height: calc(100vh - 200px); 
    padding-right: 5px; 
    margin-right: -5px; 
  }

  nav::-webkit-scrollbar {
    width: 5px;
  }

  nav::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  nav::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  nav::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.9rem 1.25rem;
    margin-bottom: 0.25rem;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    text-align: left;
    width: 100%;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
  }

  .menu-item i {
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
    transition: transform 0.3s ease;
  }

  .menu-item span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
  }

  .menu-item:hover i {
    transform: scale(1.1);
  }

  .menu-item.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 500;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .menu-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: #fff;
    border-radius: 0 2px 2px 0;
  }

  .user-section {
    border-top: 2px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    margin-top: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .user-info i {
    font-size: 1.5rem;
  }

  .logout-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }

  .content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    margin-left: 280px; 
    width: calc(100% - 280px); 
    min-height: 100vh;
  }

  .content-header {
    background: white;
    padding: 1.5rem 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
  }

  .content-header h2 {
    margin: 0;
    color: #1a237e;
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.75rem;
  }

  .content-header h2 i {
    color: #303f9f;
  }


  .content::-webkit-scrollbar {
    width: 8px;
  }

  .content::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .content::-webkit-scrollbar-thumb {
    background: #c5cae9;
    border-radius: 4px;
  }

  .content::-webkit-scrollbar-thumb:hover {
    background: #9fa8da;
  }

  * {
    transition: background-color 0.3s, transform 0.3s, box-shadow 0.3s;
  }

  @media (max-width: 1024px) {
    .sidebar {
      width: 240px;
      min-width: 240px;
    }

    .content {
      margin-left: 240px;
      width: calc(100% - 240px);
    }
  }

  @media (max-width: 768px) {
    .sidebar {
      width: 70px;
      min-width: 70px;
      padding: 1.5rem 0.5rem;
    }

    .content {
      margin-left: 70px;
      width: calc(100% - 70px);
    }

    .logo h2, .menu-item span, .user-info span {
      display: none;
    }

    .menu-item {
      justify-content: center;
      padding: 0.9rem 0;
    }

    .menu-item i {
      font-size: 1.3rem;
      margin: 0;
    }

    .user-info {
      justify-content: center;
    }
  }
</style>