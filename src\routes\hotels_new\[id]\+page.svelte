<script lang="ts">
    import { onMount } from 'svelte';
    import { page } from '$app/stores'; 
    import { user, hotelBookings } from '../../../stores/userStore';
    import { goto } from '$app/navigation';
    import Navbar from '../../../components/Navbar.svelte'; 
    import Footer from '../../../components/Footer.svelte';
    import CTA from '../../../components/CTA.svelte'; 

    let hotel = null;
    let isLoading = true;
    let error = null;
    let hotelId = $page.params.id; 
    let showBookingSuccess = false;

    let checkInDate = '';
    let checkOutDate = '';
    let guests = 2;
    let rooms = 1;

    let ratings = [];
    let totalRatings = 0;
    let averageRating = 0;
    let userRating = null;
    let isLoadingRatings = false;
    let ratingError = null;
    let ratingValue = 5; 
    let ratingComment = '';
    let ratingStatus = 'idle'; 
    let ratingFormError = '';
    let showRatingForm = false;
    let showDeleteConfirm = false;
    let ratingToDelete = null;

    type RoomType = 'deluxe' | 'superior';
    let selectedRoom: RoomType | null = null;
    let roomPrices: Record<RoomType, number> = {
      deluxe: 0,
      superior: 0
    };
    let showRoomPriceModal = false;
    let editingRoomPrice = 0;
    let editingRoomType: RoomType = 'deluxe';

    $: minCheckOutDate = checkInDate ? new Date(new Date(checkInDate).getTime() + 86400000).toISOString().split('T')[0] : '';

    $: totalNights = calculateNights(checkInDate, checkOutDate);

    $: totalPrice = totalNights > 0 ?
        (selectedRoom ? roomPrices[selectedRoom] : (hotel && hotel.gia ? hotel.gia : 0)) * totalNights * rooms : 0;

    function calculateNights(checkIn: string, checkOut: string): number {
      if (!checkIn || !checkOut) return 0;
      const start = new Date(checkIn);
      const end = new Date(checkOut);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    }

    function formatDate(dateString: string): string {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN', { weekday: 'short', day: 'numeric', month: 'long', year: 'numeric' });
    }

    function formatStars(stars: number): string {
      if (!stars || stars < 1) return '';
      return '<i class="fas fa-star star-icon"></i>'.repeat(Math.min(stars, 5));
    }

    function formatImageUrl(imageUrl: string | undefined): string {
      if (!imageUrl) return '/images/vidu.jpg'; 
      if (imageUrl.startsWith('http')) {
        return imageUrl;
      }
      if (imageUrl.startsWith('/uploads')) {
        return `http://localhost:5000${imageUrl}`;
      }
      return `/images/${imageUrl}`;
    }

    function handleBooking() {
      if (!$user) {
        goto('/login');
        return;
      }

      if (!checkInDate || !checkOutDate) {
        alert('Vui lòng chọn ngày nhận phòng và trả phòng');
        return;
      }

      const booking = {
        id: Date.now(),
        hotelId: hotelId,
        userId: $user.id,
        hotelDetails: hotel,
        checkInDate: checkInDate,
        checkOutDate: checkOutDate,
        guests: guests,
        rooms: rooms,
        totalPrice: totalPrice,
        bookingDate: new Date().toISOString(),
        status: 'pending'
      };

      hotelBookings.update(existingBookings => [...existingBookings, booking]);
      showBookingSuccess = true;

      setTimeout(() => {
        showBookingSuccess = false;
        goto('/Profile');
      }, 2000);
    }

    async function fetchHotelRatings() {
      isLoadingRatings = true;
      ratingError = null;
      try {
        const response = await fetch(`http://localhost:5000/api/hotel-ratings/hotel/${hotelId}`);

        if (!response.ok) {
          if (response.status === 404) {
            ratings = [];
            averageRating = 0;
            totalRatings = 0;
            return;
          }
          throw new Error(`Lỗi ${response.status}: Không thể tải đánh giá.`);
        }

        const data = await response.json();
        ratings = data.ratings || [];
        totalRatings = ratings.length;

        if (totalRatings > 0) {
          const sum = ratings.reduce((acc, rating) => acc + rating.xep_hang, 0);
          averageRating = sum / totalRatings;
        } else {
          averageRating = 0;
        }

        if ($user) {
          userRating = ratings.find(rating => rating.ma_nguoi_dung === $user.ma_nguoi_dung);

          if (userRating) {
            ratingValue = userRating.xep_hang;
            ratingComment = userRating.binh_luan || '';
          }
        }
      } catch (err) {
        console.error('Lỗi khi tải đánh giá:', err);
        ratingError = err.message;
      } finally {
        isLoadingRatings = false;
      }
    }

    async function submitRating() {
      if (!$user) {
        goto('/login'); 
        return;
      }

      if (ratingValue < 1 || ratingValue > 5) {
        ratingFormError = 'Xếp hạng phải từ 1 đến 5 sao.';
        ratingStatus = 'error';
        return;
      }

      ratingStatus = 'loading';
      ratingFormError = '';

      try {
        const ratingData = {
          ma_nguoi_dung: $user.ma_nguoi_dung,
          ma_khach_san: parseInt(hotelId),
          xep_hang: ratingValue,
          binh_luan: ratingComment || null
        };

        let url = 'http://localhost:5000/api/hotel-ratings';
        let method = 'POST';

        if (userRating) {
          url = `http://localhost:5000/api/hotel-ratings/${userRating.ma_danh_gia}`;
          method = 'PUT';
        }

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(ratingData)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Không thể gửi đánh giá. Vui lòng thử lại.');
        }

        await response.json(); 

        await fetchHotelRatings();

        ratingStatus = 'success';
        showRatingForm = false;

        alert(userRating ? 'Cập nhật đánh giá thành công!' : 'Gửi đánh giá thành công!');

      } catch (err) {
        console.error('Lỗi khi gửi đánh giá:', err);
        ratingStatus = 'error';
        ratingFormError = err.message || 'Có lỗi xảy ra khi gửi đánh giá. Vui lòng thử lại sau.';
      }
    }

    function confirmDeleteRating(ratingId: number): void {
      ratingToDelete = ratingId;
      showDeleteConfirm = true;
    }

    async function deleteRating() {
      if (!ratingToDelete) return;

      try {
        const response = await fetch(`http://localhost:5000/api/hotel-ratings/${ratingToDelete}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Không thể xóa đánh giá. Vui lòng thử lại.');
        }

        await fetchHotelRatings();

        showDeleteConfirm = false;
        ratingToDelete = null;

        alert('Xóa đánh giá thành công!');
      } catch (err) {
        console.error('Lỗi khi xóa đánh giá:', err);
        alert(`Lỗi khi xóa đánh giá: ${err.message}`);
      }
    }

    function cancelDeleteRating() {
      showDeleteConfirm = false;
      ratingToDelete = null;
    }

    function toggleRatingForm() {
      if (!$user) {
        goto('/login');
        return;
      }
      showRatingForm = !showRatingForm;
    }

    function selectRoom(roomType: RoomType): void {
      selectedRoom = roomType;
      const bookingSection = document.getElementById('booking-section');
      if (bookingSection) {
        bookingSection.scrollIntoView({ behavior: 'smooth' });
      }
    }

    function openRoomPriceModal(roomType: RoomType): void {
      editingRoomType = roomType;
      editingRoomPrice = roomPrices[roomType] || 0;
      showRoomPriceModal = true;
    }

    function saveRoomPrice(): void {
      if (editingRoomPrice <= 0) {
        alert('Giá phòng phải lớn hơn 0');
        return;
      }

      roomPrices[editingRoomType] = Number(editingRoomPrice);
      showRoomPriceModal = false;

      if (selectedRoom === editingRoomType) {
      }
    }

    function cancelRoomPriceEdit(): void {
      showRoomPriceModal = false;
    }

    onMount(async () => {
      isLoading = true;
      error = null;
      try {
        const response = await fetch(`http://localhost:5000/api/khachsan/${hotelId}`);
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Không tìm thấy thông tin khách sạn.');
          }
          throw new Error(`Lỗi ${response.status}: Không thể tải chi tiết khách sạn.`);
        }
        const data = await response.json();
        hotel = data.khachsan || data; 
        if (!hotel) {
          throw new Error('Không tìm thấy thông tin khách sạn.');
        }

        roomPrices = {
          deluxe: hotel.gia || 0,
          superior: hotel.gia ? hotel.gia * 1.2 : 0
        };

        isLoading = false;

        await fetchHotelRatings();
      } catch (err) {
        console.error('Lỗi khi tải chi tiết khách sạn:', err);
        error = err.message;
        isLoading = false;
      }
    });
  </script>

  <svelte:head>
    <title>{hotel ? hotel.ten_khach_san : 'Chi tiết khách sạn'} | Travel Agency</title>
    <meta name="description" content="Thông tin chi tiết và đặt phòng khách sạn" />
    <style>
      .star-icon {
        color: #ffcc00 !important;
        margin: 0 1px !important;
        font-size: 1.1rem !important;
        filter: drop-shadow(0 0 2px rgba(255, 204, 0, 0.5)) !important;
        transition: all 0.3s ease !important;
      }

      .stars:hover .star-icon {
        transform: scale(1.1) !important;
        animation: starTwinkle 1.5s infinite alternate !important;
      }

      @keyframes starTwinkle {
        0% {
          opacity: 0.8;
          transform: scale(1);
          filter: drop-shadow(0 0 2px rgba(255, 204, 0, 0.5));
        }
        100% {
          opacity: 1;
          transform: scale(1.2);
          filter: drop-shadow(0 0 5px rgba(255, 204, 0, 0.8));
        }
      }
    </style>
  </svelte:head>

  <Navbar />

  {#if showBookingSuccess}
    <div class="booking-success-message">
      <div class="success-content">
        <i class="fas fa-check-circle"></i>
        <h3>Đặt phòng thành công!</h3>
        <p>Thông tin đặt phòng đã được lưu vào tài khoản của bạn và đang chờ xác nhận.</p>
      </div>
    </div>
  {/if}

  <div class="hotel-detail-page-container">
    {#if isLoading}
      <div class="loading-spinner-container">
        <div class="spinner"></div>
        <p>Đang tải chi tiết khách sạn...</p>
      </div>
    {:else if error}
      <div class="error-message-container">
        <p>Có lỗi xảy ra: {error}</p>
        <a href="/hotels" class="back-button">Quay lại danh sách</a>
      </div>
    {:else if hotel}
      <div class="hero-banner" style="background-image: url({formatImageUrl(hotel.hinh_anh)})">
        <div class="hero-overlay">
          <div class="hero-content">
            <h1>{hotel.ten_khach_san}</h1>
            <div class="hero-rating">
              <span class="stars">{@html formatStars(hotel.so_sao)}</span>
              {#if averageRating > 0}
                <div class="hero-review-badge">
                  <span class="review-score">{averageRating.toFixed(1)}</span>
                  <span class="review-text">
                    {#if averageRating >= 4.5}
                      Tuyệt vời
                    {:else if averageRating >= 4}
                      Rất tốt
                    {:else if averageRating >= 3}
                      Tốt
                    {:else if averageRating >= 2}
                      Trung bình
                    {:else}
                      Cần cải thiện
                    {/if}
                  </span>
                </div>
              {/if}
            </div>
            <div class="hero-location">
              <i class="fas fa-map-marker-alt"></i> {hotel.dia_diem} - {hotel.dia_chi}
            </div>
          </div>
        </div>
      </div>

      <div class="breadcrumb">
        <a href="/">Trang chủ</a> / <a href="/hotels">Khách sạn</a> / <span>{hotel.ten_khach_san}</span>
      </div>

      <!-- Image Gallery (Ví dụ đơn giản) -->
      <!-- <div class="image-gallery">
        <div class="main-image">
          <img src={formatImageUrl(hotel.hinh_anh)} alt="Ảnh chính {hotel.ten_khach_san}" />
        </div>
        {#if hotel.hinh_anh_chi_tiet && hotel.hinh_anh_chi_tiet.length > 0}
          <div class="thumbnail-images">
            {#each hotel.hinh_anh_chi_tiet as imgUrl}
              <img src={formatImageUrl(imgUrl)} alt="Ảnh chi tiết {hotel.ten_khach_san}" class="thumbnail" />
            {/each}
          </div>
        {/if}
      </div> -->

      <!-- Hotel Details Content -->
      <div class="hotel-details-content">
        <div class="main-content">
          <!-- Description -->
          {#if hotel.mo_ta}
            <div class="description-section section">
              <h2>Mô tả khách sạn</h2>
              <p>{@html hotel.mo_ta}</p> 
            </div>
          {/if}

          {#if hotel.tien_nghi && hotel.tien_nghi.length > 0}
            <div class="amenities-section section">
              <h2>Tiện nghi nổi bật</h2>
              <ul class="amenities-list">
                {#each hotel.tien_nghi as amenity}
                  <li><i class="fas fa-check-circle"></i> {amenity}</li>
                {/each}
              </ul>
            </div>
          {/if}

          <div class="rooms-section section">
            <h2>Phòng có sẵn</h2>
            <div class="room-list">
              <div class="room-card">
                <div class="room-image">
                  <img src={formatImageUrl(hotel.hinh_anh)} alt="Phòng Deluxe" />
                  <div class="room-badge">Phổ biến nhất</div>
                </div>
                <div class="room-details">
                  <div class="room-info">
                    <h3>Phòng Deluxe</h3>
                    <div class="room-features">
                      <span><i class="fas fa-user-friends"></i> 2 người</span>
                      <span><i class="fas fa-bed"></i> 1 giường đôi</span>
                      <span><i class="fas fa-bath"></i> Phòng tắm riêng</span>
                    </div>
                    <div class="room-amenities">
                      <span><i class="fas fa-wifi"></i> WiFi miễn phí</span>
                      <span><i class="fas fa-snowflake"></i> Điều hòa</span>
                      <span><i class="fas fa-tv"></i> TV màn hình phẳng</span>
                    </div>
                  </div>
                  <div class="room-booking">
                    <div class="room-price">
                      <span class="price-value">{Number(roomPrices?.deluxe || 0).toLocaleString('vi-VN')} VNĐ</span>
                      <span class="price-period">/ đêm</span>
                      <button
                        class="edit-price-btn"
                        on:click={() => openRoomPriceModal('deluxe')}
                        aria-label="Chỉnh sửa giá phòng Deluxe"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                    <button
                      class="select-room-btn {selectedRoom === 'deluxe' ? 'selected' : ''}"
                      on:click={() => selectRoom('deluxe')}
                    >
                      {selectedRoom === 'deluxe' ? 'Đã chọn' : 'Chọn phòng'}
                    </button>
                  </div>
                </div>
              </div>

              <div class="room-card">
                <div class="room-image">
                  <img src={formatImageUrl(hotel.hinh_anh)} alt="Phòng Superior" />
                </div>
                <div class="room-details">
                  <div class="room-info">
                    <h3>Phòng Superior</h3>
                    <div class="room-features">
                      <span><i class="fas fa-user-friends"></i> 2 người</span>
                      <span><i class="fas fa-bed"></i> 2 giường đơn</span>
                      <span><i class="fas fa-bath"></i> Phòng tắm riêng</span>
                    </div>
                    <div class="room-amenities">
                      <span><i class="fas fa-wifi"></i> WiFi miễn phí</span>
                      <span><i class="fas fa-snowflake"></i> Điều hòa</span>
                      <span><i class="fas fa-tv"></i> TV màn hình phẳng</span>
                    </div>
                  </div>
                  <div class="room-booking">
                    <div class="room-price">
                      <span class="price-value">{Number(roomPrices?.superior || 0).toLocaleString('vi-VN')} VNĐ</span>
                      <span class="price-period">/ đêm</span>
                      <button
                        class="edit-price-btn"
                        on:click={() => openRoomPriceModal('superior')}
                        aria-label="Chỉnh sửa giá phòng Superior"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                    <button
                      class="select-room-btn {selectedRoom === 'superior' ? 'selected' : ''}"
                      on:click={() => selectRoom('superior')}
                    >
                      {selectedRoom === 'superior' ? 'Đã chọn' : 'Chọn phòng'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="reviews-section section">
            <h2>Đánh giá từ khách hàng</h2>

            {#if isLoadingRatings}
              <div class="loading-spinner-container">
                <div class="spinner"></div>
                <p>Đang tải đánh giá...</p>
              </div>
            {:else if ratingError}
              <div class="error-message">
                <p>Có lỗi xảy ra khi tải đánh giá: {ratingError}</p>
              </div>
            {:else}
              <div class="reviews-summary">
                <div class="reviews-score">
                  <div class="big-score">{averageRating ? averageRating.toFixed(1) : '0.0'}</div>
                  <div class="score-label">
                    <div class="score-text">
                      {#if averageRating >= 4.5}
                        Tuyệt vời
                      {:else if averageRating >= 4}
                        Rất tốt
                      {:else if averageRating >= 3}
                        Tốt
                      {:else if averageRating >= 2}
                        Trung bình
                      {:else}
                        Cần cải thiện
                      {/if}
                    </div>
                    <div class="score-count">Dựa trên {totalRatings} đánh giá</div>
                  </div>
                </div>

                <div class="reviews-actions">
                  {#if $user}
                    <button class="write-review-btn" on:click={toggleRatingForm}>
                      {userRating ? 'Chỉnh sửa đánh giá của bạn' : 'Viết đánh giá'}
                    </button>
                  {:else}
                    <button class="write-review-btn" on:click={() => goto('/login')}>
                      Đăng nhập để đánh giá
                    </button>
                  {/if}
                </div>
              </div>

              {#if showRatingForm}
                <div class="rating-form-container">
                  <h3>{userRating ? 'Chỉnh sửa đánh giá của bạn' : 'Viết đánh giá mới'}</h3>

                  {#if ratingFormError}
                    <div class="form-error">
                      <i class="fas fa-exclamation-circle"></i> {ratingFormError}
                    </div>
                  {/if}

                  <div class="rating-form">
                    <div class="form-group">
                      <label for="star-rating-input">Xếp hạng của bạn</label>
                      <div class="star-rating-input" id="star-rating-input">
                        {#each Array(5) as _, i}
                          <button
                            type="button"
                            class="star-btn {i < ratingValue ? 'active' : ''}"
                            on:click={() => ratingValue = i + 1}
                            aria-label="Đánh giá {i + 1} sao"
                          >
                            <i class="fas fa-star"></i>
                          </button>
                        {/each}
                        <span class="rating-value-display">{ratingValue}/5</span>
                      </div>
                    </div>

                    <div class="form-group">
                      <label for="rating-comment">Nhận xét của bạn (không bắt buộc)</label>
                      <textarea
                        id="rating-comment"
                        bind:value={ratingComment}
                        placeholder="Chia sẻ trải nghiệm của bạn về khách sạn này..."
                        rows="4"
                      ></textarea>
                    </div>

                    <div class="form-actions">
                      <button
                        type="button"
                        class="cancel-btn"
                        on:click={() => showRatingForm = false}
                      >
                        Hủy
                      </button>
                      <button
                        type="button"
                        class="submit-btn {ratingStatus === 'loading' ? 'loading' : ''}"
                        on:click={submitRating}
                        disabled={ratingStatus === 'loading'}
                      >
                        {#if ratingStatus === 'loading'}
                          <i class="fas fa-spinner fa-spin"></i> Đang gửi...
                        {:else}
                          {userRating ? 'Cập nhật đánh giá' : 'Gửi đánh giá'}
                        {/if}
                      </button>
                    </div>
                  </div>
                </div>
              {/if}

              {#if showDeleteConfirm}
                <div class="modal-overlay">
                  <div class="modal-content">
                    <h3>Xác nhận xóa</h3>
                    <p>Bạn có chắc chắn muốn xóa đánh giá này không?</p>
                    <div class="modal-actions">
                      <button class="cancel-btn" on:click={cancelDeleteRating}>Hủy</button>
                      <button class="delete-btn" on:click={deleteRating}>Xóa</button>
                    </div>
                  </div>
                </div>
              {/if}

              {#if ratings.length > 0}
                <div class="reviews-list">
                  {#each ratings as rating}
                    <div class="review-card">
                      <div class="reviewer-info">
                        <div class="reviewer-avatar">
                          <div class="avatar-placeholder">
                            {rating.ten_nguoi_dung ? rating.ten_nguoi_dung.substring(0, 2).toUpperCase() : 'NN'}
                          </div>
                        </div>
                        <div class="reviewer-details">
                          <div class="reviewer-name">{rating.ten_nguoi_dung || 'Người dùng ẩn danh'}</div>
                          <div class="review-date">Đã đánh giá vào {formatDate(rating.ngay_danh_gia)}</div>
                        </div>
                      </div>
                      <div class="review-content">
                        <div class="review-rating">
                          <span class="stars">{@html formatStars(rating.xep_hang)}</span>
                          <span class="review-score-badge">{rating.xep_hang.toFixed(1)}</span>

                          {#if $user && $user.ma_nguoi_dung === rating.ma_nguoi_dung}
                            <div class="review-actions">
                              <button
                                class="edit-review-btn"
                                on:click={() => {
                                  ratingValue = rating.xep_hang;
                                  ratingComment = rating.binh_luan || '';
                                  showRatingForm = true;
                                }}
                                aria-label="Chỉnh sửa đánh giá"
                              >
                                <i class="fas fa-edit"></i>
                              </button>
                              <button
                                class="delete-review-btn"
                                on:click={() => confirmDeleteRating(rating.ma_danh_gia)}
                                aria-label="Xóa đánh giá"
                              >
                                <i class="fas fa-trash-alt"></i>
                              </button>
                            </div>
                          {/if}
                        </div>
                        {#if rating.binh_luan}
                          <p class="review-text">{rating.binh_luan}</p>
                        {:else}
                          <p class="review-text no-comment">Người dùng không để lại bình luận.</p>
                        {/if}
                      </div>
                    </div>
                  {/each}
                </div>
              {:else}
                <div class="no-reviews">
                  <i class="fas fa-comment-slash"></i>
                  <p>Chưa có đánh giá nào cho khách sạn này.</p>
                  {#if $user}
                    <button class="be-first-btn" on:click={toggleRatingForm}>Hãy là người đầu tiên đánh giá!</button>
                  {/if}
                </div>
              {/if}
            {/if}
          </div>

          <div class="nearby-section section">
            <h2>Điểm tham quan gần đây</h2>
            <div class="nearby-places">
              <div class="nearby-place">
                <div class="nearby-icon">
                  <i class="fas fa-utensils"></i>
                </div>
                <div class="nearby-info">
                  <h4>Nhà hàng Hải Sản Biển Đông</h4>
                  <p>Cách 0.3 km - Hải sản tươi sống</p>
                </div>
              </div>

              <div class="nearby-place">
                <div class="nearby-icon">
                  <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="nearby-info">
                  <h4>Trung tâm mua sắm Vincom</h4>
                  <p>Cách 0.5 km - Mua sắm & Giải trí</p>
                </div>
              </div>

              <div class="nearby-place">
                <div class="nearby-icon">
                  <i class="fas fa-landmark"></i>
                </div>
                <div class="nearby-info">
                  <h4>Bảo tàng Lịch sử</h4>
                  <p>Cách 1.2 km - Văn hóa & Lịch sử</p>
                </div>
              </div>

              <div class="nearby-place">
                <div class="nearby-icon">
                  <i class="fas fa-tree"></i>
                </div>
                <div class="nearby-info">
                  <h4>Công viên Thống Nhất</h4>
                  <p>Cách 0.8 km - Giải trí & Thư giãn</p>
                </div>
              </div>
            </div>
          </div>

        </div>

        <div class="sidebar-content">
          <div id="booking-section" class="booking-section section">
            <h2>Đặt phòng {selectedRoom ? (selectedRoom === 'deluxe' ? 'Deluxe' : 'Superior') : ''}</h2>
            {#if hotel.gia}
              <div class="price-container">
                <div class="price-header">
                  <i class="fas fa-money-bill-wave"></i>
                  <span>Giá phòng</span>
                </div>
                <div class="price-display">
                  <div class="price-details">
                    <span class="price-label">Giá mỗi đêm</span>
                    <span class="price-value">
                      {Number(selectedRoom ? roomPrices[selectedRoom] : (hotel && hotel.gia ? hotel.gia : 0)).toLocaleString('vi-VN')} VNĐ
                    </span>
                    {#if selectedRoom}
                      <span class="room-type-badge">
                        {selectedRoom === 'deluxe' ? 'Phòng Deluxe' : 'Phòng Superior'}
                      </span>
                    {/if}
                  </div>
                  <div class="price-badge">Giá tốt nhất</div>
                </div>
                <div class="price-features">
                  <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Đã bao gồm thuế và phí</span>
                  </div>
                  <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Miễn phí hủy trước 24h</span>
                  </div>
                  <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Thanh toán tại khách sạn</span>
                  </div>
                </div>
              </div>

              <div class="booking-form">
                <div class="form-group">
                  <label for="check-in">Ngày nhận phòng</label>
                  <div class="input-with-icon">
                    <i class="fas fa-calendar-alt"></i>
                    <input
                      type="date"
                      id="check-in"
                      bind:value={checkInDate}
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </div>
                </div>

                <div class="form-group">
                  <label for="check-out">Ngày trả phòng</label>
                  <div class="input-with-icon">
                    <i class="fas fa-calendar-alt"></i>
                    <input
                      type="date"
                      id="check-out"
                      bind:value={checkOutDate}
                      min={minCheckOutDate || new Date(new Date().getTime() + 86400000).toISOString().split('T')[0]}
                      disabled={!checkInDate}
                    />
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group half">
                    <label for="guests">Số khách</label>
                    <div class="input-with-icon">
                      <i class="fas fa-user-friends"></i>
                      <select id="guests" bind:value={guests}>
                        <option value="1">1 người</option>
                        <option value="2">2 người</option>
                        <option value="3">3 người</option>
                        <option value="4">4 người</option>
                        <option value="5">5+ người</option>
                      </select>
                    </div>
                  </div>

                  <div class="form-group half">
                    <label for="rooms">Số phòng</label>
                    <div class="input-with-icon">
                      <i class="fas fa-door-open"></i>
                      <select id="rooms" bind:value={rooms}>
                        <option value="1">1 phòng</option>
                        <option value="2">2 phòng</option>
                        <option value="3">3 phòng</option>
                        <option value="4">4+ phòng</option>
                      </select>
                    </div>
                  </div>
                </div>

                {#if totalNights > 0}
                  <div class="booking-summary">
                    <div class="summary-row">
                      <span>Giá phòng x {rooms} phòng x {totalNights} đêm</span>
                      <span>{Number(totalPrice).toLocaleString('vi-VN')} VNĐ</span>
                    </div>
                    <div class="summary-row">
                      <span>Thuế và phí</span>
                      <span>Đã bao gồm</span>
                    </div>
                    <div class="summary-row total">
                      <span>Tổng cộng</span>
                      <span>{Number(totalPrice).toLocaleString('vi-VN')} VNĐ</span>
                    </div>
                  </div>
                {/if}
              </div>
            {:else}
              <div class="no-price-info">
                <i class="fas fa-info-circle"></i>
                <p>Thông tin giá hiện không khả dụng.</p>
              </div>
            {/if}
            <button class="book-now-btn" disabled={!checkInDate || !checkOutDate} on:click={handleBooking}>
              <i class="fas fa-calendar-check"></i>
              Đặt phòng ngay
            </button>
          </div>

          <!-- Map Section (Placeholder) -->
          <!-- <div class="map-section section">
            <h2>Vị trí trên bản đồ</h2>
            <div class="map-placeholder">Bản đồ sẽ hiển thị ở đây</div>
            <p><i class="fas fa-map-marker-alt"></i> {hotel.dia_chi}, {hotel.dia_diem}</p>
          </div> -->
        </div>
      </div>

    {:else}
      <!-- Trường hợp không có lỗi nhưng không tìm thấy hotel (ít xảy ra nếu API trả 404) -->
       <div class="error-message-container">
        <p>Không tìm thấy thông tin cho khách sạn này.</p>
        <a href="/hotels" class="back-button">Quay lại danh sách</a>
      </div>
    {/if}
  </div>

  <!-- Room Price Edit Modal -->
  {#if showRoomPriceModal}
    <div class="modal-overlay">
      <div class="modal-content">
        <h3>Chỉnh sửa giá phòng {editingRoomType === 'deluxe' ? 'Deluxe' : 'Superior'}</h3>
        <div class="form-group">
          <label for="room-price">Giá phòng (VNĐ)</label>
          <input
            type="number"
            id="room-price"
            bind:value={editingRoomPrice}
            min="1"
            step="10000"
            class="price-input"
          />
        </div>
        <div class="modal-actions">
          <button class="cancel-btn" on:click={cancelRoomPriceEdit}>Hủy</button>
          <button class="save-btn" on:click={saveRoomPrice}>Lưu thay đổi</button>
        </div>
      </div>
    </div>
  {/if}

  <CTA />
  <Footer />

  <style>
    .booking-success-message {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.7);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .success-content {
      background-color: white;
      padding: 30px;
      border-radius: 10px;
      text-align: center;
      max-width: 400px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      animation: fadeIn 0.5s ease;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .success-content i {
      font-size: 4rem;
      color: #4caf50;
      margin-bottom: 15px;
    }

    .success-content h3 {
      font-size: 1.5rem;
      margin-bottom: 10px;
      color: #333;
    }

    .success-content p {
      color: #666;
      margin-bottom: 0;
    }

    .hotel-detail-page-container {
      max-width: 1200px;
      margin: 30px auto;
      padding: 0 20px;
      font-family: 'Arial', sans-serif; /* Hoặc font bạn đang dùng */
    }

    /* Loading and Error States */
    .loading-spinner-container,
    .error-message-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 400px; /* Đảm bảo có chiều cao */
      text-align: center;
      color: #555;
    }

    .spinner {
      width: 50px;
      height: 50px;
      border: 5px solid #f3f3f3;
      border-top: 5px solid #4caf50;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error-message-container p {
      font-size: 1.1rem;
      margin-bottom: 15px;
    }

    .back-button {
      background: #4caf50;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      text-decoration: none;
      transition: background 0.3s ease;
    }

    .back-button:hover {
      background: #388e3c;
    }

    /* Breadcrumb */
    .breadcrumb {
      margin-bottom: 25px;
      font-size: 0.9rem;
      color: #666;
    }

    .breadcrumb a {
      color: #4caf50;
      text-decoration: none;
    }

    .breadcrumb a:hover {
      text-decoration: underline;
    }

    .breadcrumb span {
      color: #333;
      font-weight: bold;
    }

    /* Hero Banner */
    .hero-banner {
      height: 500px;
      background-size: cover;
      background-position: center;
      background-attachment: fixed; /* Parallax effect */
      position: relative;
      margin-bottom: 30px;
      border-radius: 12px;
      overflow: hidden;
    }

    .hero-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.7));
      display: flex;
      align-items: flex-end;
      padding: 40px;
    }

    .hero-content {
      color: white;
      max-width: 800px;
    }

    .hero-content h1 {
      font-size: 3rem;
      margin-bottom: 15px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      line-height: 1.2;
    }

    .hero-rating {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 10px;
    }

    .stars {
      color: #ffcc00; /* Màu vàng cho sao */
      font-size: 1.2rem;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }



    .hero-review-badge {
      display: flex;
      align-items: center;
      gap: 8px;
      background: rgba(255,255,255,0.2);
      padding: 5px 10px;
      border-radius: 20px;
      backdrop-filter: blur(5px);
    }

    .review-score {
      background: #4caf50;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: bold;
      font-size: 0.9rem;
    }

    .review-text {
      font-size: 0.9rem;
      color: white;
    }

    .hero-location {
      font-size: 1.1rem;
      display: flex;
      align-items: center;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .hero-location i {
      margin-right: 10px;
      color: #4caf50;
    }


    /* Image Gallery */
    /* .image-gallery {
      margin-bottom: 30px;
    }

    .main-image img {
      width: 100%;
      max-height: 500px;
      object-fit: cover;
      border-radius: 10px;
      display: block;
    }

    .thumbnail-images {
      display: flex;
      gap: 10px;
      margin-top: 15px;
      overflow-x: auto;
      padding-bottom: 10px;
    }

    .thumbnail {
      width: 100px;
      height: 70px;
      object-fit: cover;
      border-radius: 5px;
      cursor: pointer;
      border: 2px solid transparent;
      transition: border-color 0.3s ease;
    }

    .thumbnail:hover {
      border-color: #4caf50;
    } */

    /* Hotel Details Content Layout */
    .hotel-details-content {
      display: grid;
      grid-template-columns: 2fr 1fr; /* Chia layout 2 cột */
      gap: 30px;
    }

    .section {
      background: #fff;
      padding: 25px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      margin-bottom: 25px;
    }

    .section h2 {
      font-size: 1.4rem;
      color: #333;
      margin-top: 0;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }

    /* Main Content Sections */
    .description-section p {
      line-height: 1.7;
      color: #555;
    }

    .amenities-list {
      list-style: none;
      padding: 0;
      columns: 2; /* Hiển thị tiện nghi thành 2 cột */
      gap: 10px;
    }

    .amenities-list li {
      margin-bottom: 10px;
      color: #555;
      font-size: 0.95rem;
      display: flex;
      align-items: center;
    }

    .amenities-list i {
      color: #4caf50;
      margin-right: 8px;
      font-size: 1rem;
    }

    /* Room Selection Section */
    .room-list {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .room-card {
      display: flex;
      border: 1px solid #eee;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .room-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .room-image {
      width: 250px;
      position: relative;
    }

    .room-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .room-badge {
      position: absolute;
      top: 10px;
      left: 0;
      background: #ff5722;
      color: white;
      padding: 5px 10px;
      font-size: 0.8rem;
      font-weight: 600;
      border-radius: 0 4px 4px 0;
    }

    .room-details {
      flex: 1;
      display: flex;
      padding: 20px;
    }

    .room-info {
      flex: 1;
    }

    .room-info h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 1.3rem;
      color: #333;
    }

    .room-features, .room-amenities {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 15px;
    }

    .room-features span, .room-amenities span {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: #555;
    }

    .room-features i, .room-amenities i {
      margin-right: 5px;
      color: #4caf50;
    }

    .room-booking {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-end;
      padding-left: 20px;
      border-left: 1px solid #eee;
      min-width: 180px;
    }

    .room-price {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      margin-bottom: 15px;
    }

    .room-price .price-value {
      font-size: 1.4rem;
      font-weight: bold;
      color: #ff5722;
    }

    .room-price .price-period {
      font-size: 0.8rem;
      color: #666;
    }

    .select-room-btn {
      background: #4caf50;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-weight: 600;
      transition: background 0.3s ease;
    }

    .select-room-btn:hover {
      background: #388e3c;
    }

    .select-room-btn.selected {
      background: #2e7d32;
      position: relative;
    }

    .select-room-btn.selected::before {
      content: '\f00c';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      margin-right: 5px;
    }

    .edit-price-btn {
      background: none;
      border: none;
      color: #2196f3;
      cursor: pointer;
      padding: 5px;
      border-radius: 50%;
      transition: all 0.3s ease;
      margin-top: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .edit-price-btn:hover {
      background: rgba(33, 150, 243, 0.1);
      transform: scale(1.1);
    }

    .room-type-badge {
      display: block;
      font-size: 0.85rem;
      color: #666;
      margin-top: 5px;
    }

    .price-input {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      font-size: 1.1rem;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .price-input:focus {
      outline: none;
      border-color: #4caf50;
      box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    }

    .save-btn {
      background: linear-gradient(to right, #4caf50, #66bb6a);
      color: white;
      border: none;
      padding: 10px 25px;
      border-radius: 50px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 140px;
    }

    .save-btn:hover {
      background: linear-gradient(to right, #388e3c, #4caf50);
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(76, 175, 80, 0.4);
    }

    .save-btn:active {
      transform: translateY(0);
      box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
    }

    /* Reviews Section */
    .reviews-summary {
      display: flex;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }

    .reviews-score {
      display: flex;
      align-items: center;
      padding-right: 30px;
      margin-right: 30px;
      border-right: 1px solid #eee;
    }

    .big-score {
      font-size: 3rem;
      font-weight: bold;
      color: #4caf50;
      margin-right: 15px;
    }

    .score-label {
      display: flex;
      flex-direction: column;
    }

    .score-text {
      font-size: 1.2rem;
      font-weight: 600;
      color: #333;
    }

    .score-count {
      font-size: 0.9rem;
      color: #666;
    }

    .reviews-actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    .write-review-btn {
      background: linear-gradient(to right, #4caf50, #66bb6a);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 50px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba(76, 175, 80, 0.2);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .write-review-btn::before {
      content: '\f304';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
    }

    .write-review-btn:hover {
      background: linear-gradient(to right, #388e3c, #4caf50);
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(76, 175, 80, 0.3);
    }

    .write-review-btn:active {
      transform: translateY(0);
      box-shadow: 0 4px 8px rgba(76, 175, 80, 0.2);
    }

    .rating-form-container {
      background: #f9f9f9;
      padding: 25px;
      border-radius: 12px;
      margin: 25px 0;
      border: 1px solid #e0e0e0;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .rating-form-container h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 1.3rem;
      color: #333;
      padding-bottom: 12px;
      border-bottom: 1px solid #eee;
    }

    .rating-form {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #444;
      font-size: 0.95rem;
    }

    .form-group textarea {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      font-size: 0.95rem;
      resize: vertical;
      min-height: 100px;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .form-group textarea:focus {
      outline: none;
      border-color: #4caf50;
      box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    }

    .form-error {
      background: #ffebee;
      color: #c62828;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
    }

    .form-error i {
      margin-right: 8px;
    }

    .star-rating-input {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 10px;
      margin-bottom: 5px;
      padding: 12px 15px;
      background: #f5f5f5;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
    }

    .star-btn {
      background: none;
      border: none;
      font-size: 1.8rem;
      color: #d1d1d1;
      cursor: pointer;
      padding: 0;
      transition: all 0.3s ease;
      position: relative;
      margin-right: 2px;
    }

    .star-btn:hover {
      transform: scale(1.15);
    }

    .star-btn:hover::after {
      content: '';
      position: absolute;
      top: -5px;
      left: -5px;
      right: -5px;
      bottom: -5px;
      background: rgba(255, 204, 0, 0.1);
      border-radius: 50%;
      z-index: -1;
    }

    .star-btn.active {
      color: #ffcc00;
      text-shadow: 0 0 5px rgba(255, 204, 0, 0.5);
    }

    .rating-value-display {
      margin-left: 15px;
      font-weight: 600;
      color: #333;
      background: #fff;
      padding: 5px 12px;
      border-radius: 20px;
      border: 1px solid #e0e0e0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 15px;
      margin-top: 25px;
    }

    .cancel-btn {
      background: #f5f5f5;
      color: #555;
      border: none;
      padding: 10px 20px;
      border-radius: 50px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .cancel-btn:hover {
      background: #e0e0e0;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .cancel-btn:active {
      transform: translateY(0);
      box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    }

    .submit-btn {
      background: linear-gradient(to right, #4caf50, #66bb6a);
      color: white;
      border: none;
      padding: 10px 25px;
      border-radius: 50px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 140px;
    }

    .submit-btn:hover {
      background: linear-gradient(to right, #388e3c, #4caf50);
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(76, 175, 80, 0.4);
    }

    .submit-btn:active {
      transform: translateY(0);
      box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
    }

    .submit-btn.loading {
      opacity: 0.8;
      cursor: not-allowed;
      background: linear-gradient(to right, #78909c, #90a4ae);
      box-shadow: 0 4px 10px rgba(120, 144, 156, 0.3);
    }

    .submit-btn i {
      margin-right: 8px;
    }

    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      backdrop-filter: blur(3px);
      animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    .modal-content {
      background: white;
      padding: 30px;
      border-radius: 12px;
      max-width: 450px;
      width: 100%;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      animation: slideUp 0.3s ease;
      transform: translateY(0);
    }

    @keyframes slideUp {
      from { transform: translateY(30px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }

    .modal-content h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 1.5rem;
      color: #333;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .modal-content h3::before {
      content: '\f057';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      color: #f44336;
    }

    .modal-content p {
      margin-bottom: 25px;
      color: #555;
      font-size: 1.05rem;
      line-height: 1.6;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 15px;
    }

    .delete-btn {
      background: linear-gradient(to right, #f44336, #e57373);
      color: white;
      border: none;
      padding: 10px 25px;
      border-radius: 50px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba(244, 67, 54, 0.3);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .delete-btn::before {
      content: '\f2ed';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
    }

    .delete-btn:hover {
      background: linear-gradient(to right, #d32f2f, #f44336);
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(244, 67, 54, 0.4);
    }

    .delete-btn:active {
      transform: translateY(0);
      box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
    }

    .reviews-list {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .review-card {
      display: flex;
      padding: 25px;
      background: #f9f9f9;
      border-radius: 12px;
      margin-bottom: 20px;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: 1px solid #eee;
    }

    .review-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }

    .reviewer-info {
      margin-right: 25px;
      min-width: 150px;
    }

    .reviewer-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      margin-bottom: 12px;
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    }

    .avatar-placeholder {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #4caf50, #66bb6a);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 1.2rem;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .reviewer-name {
      font-weight: 600;
      color: #333;
      margin-bottom: 6px;
      font-size: 1.05rem;
    }

    .review-date {
      font-size: 0.85rem;
      color: #777;
      display: flex;
      align-items: center;
    }

    .review-date::before {
      content: '\f017';
      font-family: 'Font Awesome 5 Free';
      font-weight: 400;
      margin-right: 5px;
      font-size: 0.8rem;
      opacity: 0.7;
    }

    .review-content {
      flex: 1;
      position: relative;
    }

    .review-rating {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }

    .review-score-badge {
      background: linear-gradient(to right, #4caf50, #66bb6a);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.85rem;
      font-weight: 600;
      margin-left: 10px;
      box-shadow: 0 2px 5px rgba(76, 175, 80, 0.2);
    }

    .review-text {
      line-height: 1.7;
      color: #555;
      background: white;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #4caf50;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    }

    .review-actions {
      margin-left: auto;
      display: flex;
      gap: 8px;
    }

    .edit-review-btn, .delete-review-btn {
      background: white;
      border: none;
      cursor: pointer;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .edit-review-btn {
      color: #2196f3;
    }

    .delete-review-btn {
      color: #f44336;
    }

    .edit-review-btn:hover {
      background: #e3f2fd;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
    }

    .delete-review-btn:hover {
      background: #ffebee;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(244, 67, 54, 0.2);
    }

    .edit-review-btn:active, .delete-review-btn:active {
      transform: translateY(0);
    }

    .no-reviews {
      text-align: center;
      padding: 40px 0;
      color: #666;
      background: white;
      border-radius: 12px;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
      border: 1px dashed #ddd;
      margin: 20px 0;
    }

    .no-reviews i {
      font-size: 4rem;
      color: #e0e0e0;
      margin-bottom: 20px;
      display: block;
    }

    .no-reviews p {
      margin-bottom: 20px;
      font-size: 1.1rem;
    }

    .be-first-btn {
      background: linear-gradient(to right, #4caf50, #66bb6a);
      color: white;
      border: none;
      padding: 12px 25px;
      border-radius: 50px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba(76, 175, 80, 0.2);
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .be-first-btn::before {
      content: '\f304';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
    }

    .be-first-btn:hover {
      background: linear-gradient(to right, #388e3c, #4caf50);
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(76, 175, 80, 0.3);
    }

    .review-text.no-comment {
      color: #999;
      font-style: italic;
    }

    .error-message {
      background: #ffebee;
      color: #c62828;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
    }

    /* Nearby Section */
    .nearby-places {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }

    .nearby-place {
      display: flex;
      align-items: center;
      padding: 15px;
      background: #f9f9f9;
      border-radius: 8px;
      transition: transform 0.3s ease;
    }

    .nearby-place:hover {
      transform: translateY(-3px);
    }

    .nearby-icon {
      width: 40px;
      height: 40px;
      background: #4caf50;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      font-size: 1.2rem;
    }

    .nearby-info h4 {
      margin: 0 0 5px 0;
      color: #333;
      font-size: 1rem;
    }

    .nearby-info p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }

    /* Sidebar Content */
    .sidebar-content .section {
       position: sticky; /* Giữ sidebar khi cuộn */
       top: 20px; /* Khoảng cách từ top khi sticky */
    }

    .booking-section p {
      color: #666;
      font-size: 0.9rem;
      margin-bottom: 15px;
    }

    .price-container {
      margin-bottom: 20px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      border: 1px solid #eee;
    }

    .price-header {
      background: #4caf50;
      color: white;
      padding: 12px 15px;
      display: flex;
      align-items: center;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .price-header i {
      margin-right: 10px;
    }

    .price-display {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      background-color: #f9f9f9;
      border-bottom: 1px solid #eee;
    }

    .price-details {
      display: flex;
      flex-direction: column;
    }

    .price-label {
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 5px;
    }

    .price-value {
      font-size: 1.5rem;
      font-weight: bold;
      color: #ff5722;
    }

    .price-badge {
      background: #ff9800;
      color: white;
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .price-features {
      padding: 15px;
      background: white;
    }

    .feature-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 0.9rem;
      color: #555;
    }

    .feature-item:last-child {
      margin-bottom: 0;
    }

    .feature-item i {
      color: #4caf50;
      margin-right: 8px;
      font-size: 0.9rem;
    }

    .no-price-info {
      display: flex;
      align-items: center;
      padding: 15px;
      background: #f5f5f5;
      border-radius: 6px;
      margin-bottom: 20px;
    }

    .no-price-info i {
      color: #ff9800;
      font-size: 1.2rem;
      margin-right: 10px;
    }

    .no-price-info p {
      margin: 0;
      color: #555;
    }

    .book-now-btn {
      background: linear-gradient(to right, #ff5722, #ff7043);
      color: white;
      border: none;
      padding: 14px 25px;
      border-radius: 50px;
      cursor: pointer;
      font-size: 1.1rem;
      font-weight: bold;
      width: 100%;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .book-now-btn:hover {
      background: linear-gradient(to right, #e64a19, #ff5722);
      transform: translateY(-2px);
      box-shadow: 0 6px 18px rgba(255, 87, 34, 0.4);
    }

    .book-now-btn:active {
      transform: translateY(1px);
      box-shadow: 0 2px 10px rgba(255, 87, 34, 0.3);
    }

    .book-now-btn:disabled {
      background: linear-gradient(to right, #b0b0b0, #cccccc);
      cursor: not-allowed;
      box-shadow: none;
    }

    .book-now-btn:disabled:hover {
      transform: none;
      box-shadow: none;
      background: linear-gradient(to right, #b0b0b0, #cccccc);
    }

    /* Booking Form */
    .booking-form {
      margin: 20px 0;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-size: 0.9rem;
      color: #555;
      font-weight: 500;
    }

    .input-with-icon {
      position: relative;
    }

    .input-with-icon i {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #4caf50;
    }

    .input-with-icon input,
    .input-with-icon select {
      width: 100%;
      padding: 10px 10px 10px 35px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 0.95rem;
      transition: border-color 0.3s ease;
    }

    .input-with-icon input:focus,
    .input-with-icon select:focus {
      outline: none;
      border-color: #4caf50;
      box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
    }

    .input-with-icon input:disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;
    }

    .form-row {
      display: flex;
      gap: 15px;
    }

    .form-group.half {
      flex: 1;
    }

    .booking-summary {
      margin-top: 20px;
      padding: 15px;
      background: #f9f9f9;
      border-radius: 8px;
      border: 1px solid #eee;
    }

    .summary-row {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      font-size: 0.95rem;
      color: #555;
    }

    .summary-row:not(:last-child) {
      border-bottom: 1px dashed #ddd;
    }

    .summary-row.total {
      font-weight: bold;
      color: #333;
      font-size: 1.1rem;
      padding-top: 12px;
    }

    /* .map-placeholder {
      height: 200px;
      background: #f0f0f0;
      border-radius: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      margin-bottom: 15px;
    }

    .map-section p {
      font-size: 0.9rem;
      color: #555;
      display: flex;
      align-items: center;
    }
     .map-section i {
       margin-right: 8px;
       color: #4caf50;
     } */

    /* Responsive adjustments */
    @media (max-width: 900px) {
      .hotel-details-content {
        grid-template-columns: 1fr; /* Chuyển thành 1 cột */
      }
      .sidebar-content .section {
        position: static; /* Bỏ sticky trên mobile */
      }
      .amenities-list {
        columns: 1; /* Chỉ 1 cột tiện nghi */
      }
      .room-card {
        flex-direction: column;
      }
      .room-image {
        width: 100%;
        height: 200px;
      }
      .room-details {
        flex-direction: column;
      }
      .room-booking {
        border-left: none;
        border-top: 1px solid #eee;
        padding-left: 0;
        padding-top: 15px;
        margin-top: 15px;
        align-items: flex-start;
      }
      .room-price {
        align-items: flex-start;
      }
      .reviews-summary {
        flex-direction: column;
      }
      .reviews-score {
        border-right: none;
        border-bottom: 1px solid #eee;
        padding-right: 0;
        margin-right: 0;
        padding-bottom: 20px;
        margin-bottom: 20px;
        width: 100%;
      }
      .review-card {
        flex-direction: column;
      }
      .reviewer-info {
        margin-right: 0;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
      }
      .reviewer-avatar {
        margin-bottom: 0;
        margin-right: 15px;
      }
      .nearby-places {
        grid-template-columns: 1fr;
      }
      .form-row {
        flex-direction: column;
        gap: 15px;
      }
      .booking-summary {
        padding: 10px;
      }
      .summary-row {
        font-size: 0.9rem;
      }
      .summary-row.total {
        font-size: 1rem;
      }
    }

    @media (max-width: 600px) {
      .hero-content h1 {
        font-size: 2rem;
      }
      .hero-rating {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
      }
      .hero-overlay {
        padding: 20px;
      }
      /* .main-image img {
        max-height: 300px;
      } */
      .section {
        padding: 20px;
      }
      .section h2 {
        font-size: 1.2rem;
      }
      .hero-banner {
        height: 350px;
        background-attachment: scroll;
      }
    }
  </style>
