<script>
  import { createEventDispatcher } from 'svelte';
  const dispatch = createEventDispatcher();

  let newAccount = {
    ho_ten: '',
    email: '',
    mat_khau: '',
    so_dien_thoai: '',
    vai_tro: 'khach_hang' 
  };

  function resetForm() {
    newAccount = {
      ho_ten: '',
      email: '',
      mat_khau: '',
      so_dien_thoai: '',
      vai_tro: 'khach_hang'
    };
  }

  async function handleSubmit() {
    try {
      if (!newAccount.ho_ten) {
        throw new Error('Họ tên không được để trống');
      }
      if (!newAccount.email) {
        throw new Error('Email không được để trống');
      }
      if (!newAccount.mat_khau) {
        throw new Error('Mật khẩu không được để trống');
      }
      if (!newAccount.so_dien_thoai) {
        throw new Error('<PERSON><PERSON> điện thoại không được để trống');
      }

      console.log('Submitting new account:', newAccount);

      const response = await fetch('http://localhost:5000/api/account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newAccount)
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        let errorMessage = 'Không thể thêm tài khoản';

        try {
          const errorData = await response.json();
          console.error('Error response data:', errorData);

          if (errorData && errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData && errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (parseError) {
          console.error('Error parsing error response:', parseError);
        }

        throw new Error(errorMessage);
      }

      dispatch('accountAdded');

      resetForm();

      alert('Thêm tài khoản thành công!');
    } catch (error) {
      console.error('Error adding account:', error);
      alert(error.message);
    }
  }
</script>

<div class="add-account-form">
  <h2>Thêm Tài Khoản Mới</h2>
  <form on:submit|preventDefault={handleSubmit}>
    <div class="form-grid">
      <div class="form-group">
        <label for="hoTen">Họ tên</label>
        <input
          id="hoTen"
          type="text"
          bind:value={newAccount.ho_ten}
          placeholder="Nhập họ tên"
          required
        >
      </div>

      <div class="form-group">
        <label for="email">Email</label>
        <input
          id="email"
          type="email"
          bind:value={newAccount.email}
          placeholder="Nhập email"
          required
        >
      </div>

      <div class="form-group">
        <label for="matKhau">Mật khẩu</label>
        <input
          id="matKhau"
          type="password"
          bind:value={newAccount.mat_khau}
          placeholder="Nhập mật khẩu"
          required
        >
      </div>

      <div class="form-group">
        <label for="soDienThoai">Số điện thoại</label>
        <input
          id="soDienThoai"
          type="tel"
          bind:value={newAccount.so_dien_thoai}
          placeholder="Nhập số điện thoại"
          required
        >
      </div>

      <div class="form-group">
        <label for="vaiTro">Vai trò</label>
        <select
          id="vaiTro"
          bind:value={newAccount.vai_tro}
          required
        >
          <option value="khach_hang">Khách hàng</option>
          <option value="admin">Quản trị viên</option>
        </select>
      </div>
    </div>

    <div class="form-actions">
      <button type="button" class="btn-cancel" on:click={resetForm}>Hủy</button>
      <button type="submit" class="btn-save">Lưu Tài Khoản</button>
    </div>
  </form>
</div>

<style>
  .add-account-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  h2 {
    color: #2c3e50;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    font-weight: 600;
    text-align: center;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    color: #4a5568;
    font-weight: 500;
    font-size: 0.95rem;
  }

  input,
  select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: white;
  }

  input:hover,
  select:hover {
    border-color: #cbd5e0;
  }

  input:focus,
  select:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .btn-save,
  .btn-cancel {
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-save {
    background: #4CAF50;
    color: white;
    border: none;
  }

  .btn-save:hover {
    background: #45a049;
    transform: translateY(-1px);
  }

  .btn-cancel {
    background: white;
    border: 2px solid #e2e8f0;
    color: #4a5568;
  }

  .btn-cancel:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
  }

  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
