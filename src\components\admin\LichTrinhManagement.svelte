<script lang="ts">
  import { onMount } from 'svelte';

  interface Itinerary {
    ma_lich_trinh: number;
    ma_tour: number;
    ngay: string;
    hoat_dong: string;
    ten_tour?: string;
  }

  interface Tour {
    ma_tour: number;
    ten_tour: string;
  }

  let itineraries: Itinerary[] = [];
  let tours: Tour[] = [];
  let currentPage = 1;
  let itemsPerPage = 10;
  let searchQuery = '';
  let isLoading = true;
  let fetchError: string | null = null;
  let showAddForm = false;
  let showEditForm = false;
  let selectedItineraryId: number | null = null;

  let formData = {
    ma_tour: '',
    ngay: '',
    hoat_dong: ''
  };

  $: filteredItineraries = itineraries.filter(itinerary => {
    const searchLower = searchQuery.toLowerCase();
    return (
      (itinerary.hoat_dong && itinerary.hoat_dong.toLowerCase().includes(searchLower)) ||
      (itinerary.ten_tour && itinerary.ten_tour.toLowerCase().includes(searchLower)) ||
      (itinerary.ngay && itinerary.ngay.includes(searchLower))
    );
  });

  $: paginatedItineraries = filteredItineraries.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  $: totalPages = Math.ceil(filteredItineraries.length / itemsPerPage);

  async function fetchItineraries() {
    isLoading = true;
    fetchError = null;

    try {
      const response = await fetch('http://localhost:5000/api/lichtrinh');

      if (!response.ok) {
        throw new Error(`Lỗi ${response.status}: Không thể tải dữ liệu lịch trình.`);
      }

      const data = await response.json();
      itineraries = data.lichtrinh || [];

    } catch (err: any) {
      console.error('Lỗi khi tải dữ liệu lịch trình:', err);
      fetchError = err.message;
    } finally {
      isLoading = false;
    }
  }

  async function fetchTours() {
    try {
      const response = await fetch('http://localhost:5000/api/tours');

      if (!response.ok) {
        throw new Error(`Lỗi ${response.status}: Không thể tải danh sách tour.`);
      }

      const data = await response.json();
      tours = data || [];

    } catch (err: any) {
      console.error('Lỗi khi tải danh sách tour:', err);
    }
  }

  async function addItinerary() {
    try {
      if (!formData.ma_tour || !formData.ngay || !formData.hoat_dong) {
        alert('Vui lòng nhập đầy đủ thông tin bắt buộc');
        return;
      }

      const response = await fetch('http://localhost:5000/api/lichtrinh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ma_tour: parseInt(formData.ma_tour as string),
          ngay: formData.ngay,
          hoat_dong: formData.hoat_dong
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Không thể thêm lịch trình');
      }

      resetForm();
      showAddForm = false;
      await fetchItineraries();
      alert('Thêm lịch trình thành công!');
    } catch (err: any) {
      console.error('Lỗi khi thêm lịch trình:', err);
      alert(`Lỗi: ${err.message}`);
    }
  }

  async function updateItinerary() {
    try {
      if (!selectedItineraryId || !formData.ma_tour || !formData.ngay || !formData.hoat_dong) {
        alert('Vui lòng nhập đầy đủ thông tin bắt buộc');
        return;
      }

      const response = await fetch(`http://localhost:5000/api/lichtrinh/${selectedItineraryId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ma_tour: parseInt(formData.ma_tour as string),
          ngay: formData.ngay,
          hoat_dong: formData.hoat_dong
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Không thể cập nhật lịch trình');
      }

      resetForm();
      showEditForm = false;
      selectedItineraryId = null;
      await fetchItineraries();
      alert('Cập nhật lịch trình thành công!');
    } catch (err: any) {
      console.error('Lỗi khi cập nhật lịch trình:', err);
      alert(`Lỗi: ${err.message}`);
    }
  }

  async function deleteItinerary(id: number) {
    if (!confirm('Bạn có chắc chắn muốn xóa lịch trình này không?')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/lichtrinh/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Không thể xóa lịch trình');
      }

      await fetchItineraries();
      alert('Xóa lịch trình thành công!');
    } catch (err: any) {
      console.error('Lỗi khi xóa lịch trình:', err);
      alert(`Lỗi: ${err.message}`);
    }
  }

  function editItinerary(itinerary: Itinerary) {
    selectedItineraryId = itinerary.ma_lich_trinh;
    formData = {
      ma_tour: itinerary.ma_tour.toString(),
      ngay: itinerary.ngay,
      hoat_dong: itinerary.hoat_dong
    };
    showEditForm = true;
  }

  function resetForm() {
    formData = {
      ma_tour: '',
      ngay: '',
      hoat_dong: ''
    };
  }

  function formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  }

  function getTourName(tourId: number): string {
    const tour = tours.find(t => t.ma_tour === tourId);
    return tour ? tour.ten_tour : `Tour ${tourId}`;
  }

  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
    }
  }

  onMount(async () => {
    await Promise.all([fetchItineraries(), fetchTours()]);
  });
</script>

<style>
  .itinerary-management {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .search-bar {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
  }

  .search-input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }

  .add-button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .add-button:hover {
    background-color: #45a049;
  }

  .table-container {
    overflow-x: auto;
    margin-bottom: 20px;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
  }

  th {
    background-color: #f8f9fa;
    font-weight: 600;
  }

  tr:hover {
    background-color: #f5f5f5;
  }

  .action-buttons {
    display: flex;
    gap: 5px;
  }

  .edit-button, .delete-button {
    padding: 6px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
  }

  .edit-button {
    background-color: #2196F3;
    color: white;
  }

  .edit-button:hover {
    background-color: #0b7dda;
  }

  .delete-button {
    background-color: #f44336;
    color: white;
  }

  .delete-button:hover {
    background-color: #da190b;
  }

  .pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 20px;
  }

  .page-button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background-color: white;
    cursor: pointer;
    border-radius: 4px;
  }

  .page-button.active {
    background-color: #4285f4;
    color: white;
    border-color: #4285f4;
  }

  .page-button:hover:not(.active) {
    background-color: #f5f5f5;
  }

  .form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .form-container {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 100%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  }

  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .form-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #777;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
  }

  .form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }

  .form-control:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
  }

  textarea.form-control {
    min-height: 100px;
    resize: vertical;
  }

  .submit-button {
    background-color: #4285f4;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    width: 100%;
    margin-top: 10px;
  }

  .submit-button:hover {
    background-color: #3367d6;
  }

  .loading-message, .error-message {
    text-align: center;
    padding: 20px;
  }

  .error-message {
    color: #f44336;
  }

  .no-data-message {
    text-align: center;
    padding: 20px;
    color: #777;
  }
</style>

<div class="itinerary-management">
  <div class="header">
    <h2>Quản lý Lịch trình</h2>
    <button class="add-button" on:click={() => { resetForm(); showAddForm = true; }}>
      <i class="fas fa-plus"></i> Thêm lịch trình
    </button>
  </div>

  <div class="search-bar">
    <input
      type="text"
      class="search-input"
      placeholder="Tìm kiếm lịch trình..."
      bind:value={searchQuery}
    />
  </div>

  {#if isLoading}
    <div class="loading-message">
      <p>Đang tải dữ liệu lịch trình...</p>
    </div>
  {:else if fetchError}
    <div class="error-message">
      <p>Lỗi: {fetchError}</p>
      <button on:click={fetchItineraries} class="retry-button">Thử lại</button>
    </div>
  {:else if paginatedItineraries.length === 0}
    <div class="no-data-message">
      <p>Không có dữ liệu lịch trình.</p>
    </div>
  {:else}
    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Tour</th>
            <th>Ngày</th>
            <th>Hoạt động</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {#each paginatedItineraries as itinerary}
            <tr>
              <td>{itinerary.ma_lich_trinh}</td>
              <td>{itinerary.ten_tour || getTourName(itinerary.ma_tour)}</td>
              <td>{formatDate(itinerary.ngay)}</td>
              <td>
                {#if itinerary.hoat_dong && itinerary.hoat_dong.length > 100}
                  {itinerary.hoat_dong.substring(0, 100)}...
                {:else}
                  {itinerary.hoat_dong}
                {/if}
              </td>
              <td>
                <div class="action-buttons">
                  <button class="edit-button" on:click={() => editItinerary(itinerary)}>
                    <i class="fas fa-edit"></i> Sửa
                  </button>
                  <button class="delete-button" on:click={() => deleteItinerary(itinerary.ma_lich_trinh)}>
                    <i class="fas fa-trash"></i> Xóa
                  </button>
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>

    {#if totalPages > 1}
      <div class="pagination">
        <button
          class="page-button"
          on:click={() => goToPage(1)}
          disabled={currentPage === 1}
          aria-label="Trang đầu tiên"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button
          class="page-button"
          on:click={() => goToPage(currentPage - 1)}
          disabled={currentPage === 1}
          aria-label="Trang trước"
        >
          <i class="fas fa-angle-left"></i>
        </button>

        {#each Array(totalPages) as _, i}
          {#if i + 1 === currentPage || i + 1 === 1 || i + 1 === totalPages || (i + 1 >= currentPage - 1 && i + 1 <= currentPage + 1)}
            <button
              class="page-button {i + 1 === currentPage ? 'active' : ''}"
              on:click={() => goToPage(i + 1)}
            >
              {i + 1}
            </button>
          {:else if i + 1 === currentPage - 2 || i + 1 === currentPage + 2}
            <span class="page-button">...</span>
          {/if}
        {/each}

        <button
          class="page-button"
          on:click={() => goToPage(currentPage + 1)}
          disabled={currentPage === totalPages}
          aria-label="Trang tiếp theo"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button
          class="page-button"
          on:click={() => goToPage(totalPages)}
          disabled={currentPage === totalPages}
          aria-label="Trang cuối cùng"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
    {/if}
  {/if}


  {#if showAddForm}
    <div class="form-overlay">
      <div class="form-container">
        <div class="form-header">
          <h3 class="form-title">Thêm lịch trình mới</h3>
          <button class="close-button" on:click={() => showAddForm = false}>&times;</button>
        </div>
        <form on:submit|preventDefault={addItinerary}>
          <div class="form-group">
            <label for="tour">Tour</label>
            <select id="tour" class="form-control" bind:value={formData.ma_tour} required>
              <option value="">-- Chọn tour --</option>
              {#each tours as tour}
                <option value={tour.ma_tour}>{tour.ten_tour}</option>
              {/each}
            </select>
          </div>
          <div class="form-group">
            <label for="date">Ngày</label>
            <input
              type="date"
              id="date"
              class="form-control"
              bind:value={formData.ngay}
              required
            />
          </div>
          <div class="form-group">
            <label for="activity">Hoạt động</label>
            <textarea
              id="activity"
              class="form-control"
              bind:value={formData.hoat_dong}
              placeholder="Mô tả chi tiết hoạt động trong ngày..."
              required
            ></textarea>
          </div>
          <button type="submit" class="submit-button">Thêm lịch trình</button>
        </form>
      </div>
    </div>
  {/if}


  {#if showEditForm}
    <div class="form-overlay">
      <div class="form-container">
        <div class="form-header">
          <h3 class="form-title">Chỉnh sửa lịch trình</h3>
          <button class="close-button" on:click={() => showEditForm = false}>&times;</button>
        </div>
        <form on:submit|preventDefault={updateItinerary}>
          <div class="form-group">
            <label for="edit-tour">Tour</label>
            <select id="edit-tour" class="form-control" bind:value={formData.ma_tour} required>
              <option value="">-- Chọn tour --</option>
              {#each tours as tour}
                <option value={tour.ma_tour}>{tour.ten_tour}</option>
              {/each}
            </select>
          </div>
          <div class="form-group">
            <label for="edit-date">Ngày</label>
            <input
              type="date"
              id="edit-date"
              class="form-control"
              bind:value={formData.ngay}
              required
            />
          </div>
          <div class="form-group">
            <label for="edit-activity">Hoạt động</label>
            <textarea
              id="edit-activity"
              class="form-control"
              bind:value={formData.hoat_dong}
              placeholder="Mô tả chi tiết hoạt động trong ngày..."
              required
            ></textarea>
          </div>
          <button type="submit" class="submit-button">Cập nhật lịch trình</button>
        </form>
      </div>
    </div>
  {/if}
</div>
