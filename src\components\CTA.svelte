<script>
    import { onMount, onDestroy } from 'svelte';
    import AOS from 'aos';
    import 'aos/dist/aos.css';
  
    onMount(() => {
      AOS.init({
        duration: 1000,
        easing: 'ease-in-out',
        once: false,
      });
  
      onDestroy(() => {
        AOS.refresh();
      });
  
      window.addEventListener('scroll', () => {
        AOS.refresh();
      });
    });
  </script>
  
  <section class="cta">
    <div class="container">
      <h2 data-aos="fade-down">Đặt ngay chuyến đi của bạn</h2>
      <p data-aos="fade-up">Khám phá những điểm đến tuyệt vời với giá ưu đãi!</p>
      <a href="/" class="btn-cta" data-aos="zoom-in">Đặt ngay</a>
    </div>
  </section>
  
  <style>
    .cta {
      background: linear-gradient(135deg, #fff, #e0f2ff);
      color: #111;
      padding: 80px 20px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }
  
    .cta h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }
  
    .cta p {
      font-size: 1.25rem;
      margin-bottom: 2rem;
    }
  
    .btn-cta {
      display: inline-block;
      padding: 14px 32px;
      font-size: 1.1rem;
      font-weight: 600;
      background: #ffc107;
      color: #000;
      border-radius: 8px;
      text-decoration: none;
      transition: all 0.3s ease;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }
  
    .btn-cta:hover {
      background-color: #ffb300;
      transform: scale(1.05);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25);
    }
  
    /* Keyframes nếu cần dùng animation thủ công */
    @keyframes fadeInDown {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: scale(0.95);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }
  </style>
  