<script>
  import { createEventDispatcher } from 'svelte';
  const dispatch = createEventDispatcher();

  let newStaff = {
    ho_ten: '',
    email: '',
    mat_khau: '',
    so_dien_thoai: '',
    chuc_vu: '',
    ngay_sinh: '',
    dia_chi: ''
  };

  function resetForm() {
    newStaff = {
      ho_ten: '',
      email: '',
      mat_khau: '',
      so_dien_thoai: '',
      chuc_vu: '',
      ngay_sinh: '',
      dia_chi: ''
    };
  }

  async function handleSubmit() {
    try {
      const response = await fetch('http://localhost:5000/api/nhanvien', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newStaff)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Không thể thêm nhân viên');
      }

      dispatch('staffAdded');

      resetForm();

      alert('Thêm nhân viên thành công!');
    } catch (error) {
      console.error('Error adding staff:', error);
      alert(error.message);
    }
  }
</script>

<div class="add-staff-form">
  <h2>Thêm Nhân viên Mới</h2>
  <form on:submit|preventDefault={handleSubmit}>
    <div class="form-grid">
      <div class="form-group">
        <label for="hoTen">Họ tên</label>
        <input
          id="hoTen"
          type="text"
          bind:value={newStaff.ho_ten}
          placeholder="Nhập họ tên"
          required
        >
      </div>

      <div class="form-group">
        <label for="email">Email</label>
        <input
          id="email"
          type="email"
          bind:value={newStaff.email}
          placeholder="Nhập email"
          required
        >
      </div>

      <div class="form-group">
        <label for="matKhau">Mật khẩu</label>
        <input
          id="matKhau"
          type="password"
          bind:value={newStaff.mat_khau}
          placeholder="Nhập mật khẩu"
          required
        >
      </div>

      <div class="form-group">
        <label for="soDienThoai">Số điện thoại</label>
        <input
          id="soDienThoai"
          type="tel"
          bind:value={newStaff.so_dien_thoai}
          placeholder="Nhập số điện thoại"
          required
        >
      </div>

      <div class="form-group">
        <label for="chucVu">Chức vụ</label>
        <input
          id="chucVu"
          type="text"
          bind:value={newStaff.chuc_vu}
          placeholder="Nhập chức vụ"
          required
        >
      </div>

      <div class="form-group">
        <label for="ngaySinh">Ngày sinh</label>
        <input
          id="ngaySinh"
          type="date"
          bind:value={newStaff.ngay_sinh}
          required
        >
      </div>

      <div class="form-group full-width">
        <label for="diaChi">Địa chỉ</label>
        <textarea
          id="diaChi"
          bind:value={newStaff.dia_chi}
          placeholder="Nhập địa chỉ"
          required
          rows="3"
        ></textarea>
      </div>
    </div>

    <div class="form-actions">
      <button type="button" class="btn-cancel" on:click={resetForm}>Hủy</button>
      <button type="submit" class="btn-save">Lưu Nhân viên</button>
    </div>
  </form>
</div>

<style>
  .add-staff-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  h2 {
    color: #2c3e50;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    font-weight: 600;
    text-align: center;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group.full-width {
    grid-column: 1 / -1;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    color: #4a5568;
    font-weight: 500;
    font-size: 0.95rem;
  }

  input,
  textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: white;
  }

  input:hover,
  textarea:hover {
    border-color: #cbd5e0;
  }

  input:focus,
  textarea:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
  }

  textarea {
    resize: vertical;
    min-height: 120px;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .btn-save,
  .btn-cancel {
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-save {
    background: #4CAF50;
    color: white;
    border: none;
  }

  .btn-save:hover {
    background: #45a049;
    transform: translateY(-1px);
  }

  .btn-cancel {
    background: white;
    border: 2px solid #e2e8f0;
    color: #4a5568;
  }

  .btn-cancel:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
  }

  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
    }
  }
</style>