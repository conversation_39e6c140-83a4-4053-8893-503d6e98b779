<script lang="ts">
  import { onMount } from 'svelte';
  import AddTourForm from './AddTourForm.svelte';
  import EditTourForm from './EditTourForm.svelte';

  interface Tour {
    ma_tour: number;
    ten_tour: string;
    dia_diem: string;
    loai_tour: 'trong_nuoc' | 'nuoc_ngoai' | null;
    gia: number | string; 
    ngay_bat_dau: string;
    ngay_ket_thuc: string;
    so_cho_trong: number | string; 
    mo_ta?: string; 
    hinh_anh?: string; 
  }

  let tours: Tour[] = []; 
  let currentPage = 1;
  let itemsPerPage = 10;
  let searchQuery = '';
  let showAddTourPage = false;
  let showEditTourPage = false;
  let selectedTourId: number | null = null; 
  let isLoading = true; 
  let fetchError: string | null = null; 

  $: filteredTours = tours.filter(tour => {
    const searchLower = searchQuery.toLowerCase();
    return (
      (tour.ten_tour && tour.ten_tour.toLowerCase().includes(searchLower)) ||
      (tour.dia_diem && tour.dia_diem.toLowerCase().includes(searchLower)) ||
      (tour.mo_ta && tour.mo_ta.toLowerCase().includes(searchLower))
    );
  });

  $: startIndex = (currentPage - 1) * itemsPerPage;
  $: endIndex = startIndex + itemsPerPage;
  $: displayedTours = filteredTours.slice(startIndex, endIndex);
  $: totalPages = Math.ceil(filteredTours.length / itemsPerPage);

  async function fetchTours() {
    isLoading = true;
    fetchError = null;
    try {
      const response = await fetch('http://localhost:5000/api/tours'); 
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(errorData.message || 'Không thể tải danh sách tour');
      }
      tours = await response.json();

      console.log('Tours loaded:', tours.length);
      tours.forEach(tour => {
        if (tour.hinh_anh) {
          if (tour.hinh_anh.startsWith('data:image')) {
            const shortBase64 = tour.hinh_anh.substring(0, 30) + '...' + tour.hinh_anh.substring(tour.hinh_anh.length - 10);
            console.log(`Tour ID ${tour.ma_tour} image: Base64 data (${tour.hinh_anh.length} chars) - ${shortBase64}`);
          } else {
            console.log(`Tour ID ${tour.ma_tour} image path:`, tour.hinh_anh);

            if (!tour.hinh_anh.includes('/') && !tour.hinh_anh.includes('\\') && !tour.hinh_anh.startsWith('http')) {
              console.log(`  - Likely a filename only, will try to load from /images/${tour.hinh_anh}`);
            }
            else if (tour.hinh_anh.startsWith('/')) {
              console.log(`  - Backend path, will try to load from http://localhost:5000${tour.hinh_anh}`);
            }
            else if (!tour.hinh_anh.startsWith('http')) {
              console.log(`  - Relative path, will try to load from http://localhost:5000/${tour.hinh_anh}`);
            }
          }
        }
      });
    } catch (error: any) { 
      console.error('Error fetching tours:', error);
      fetchError = 'Có lỗi khi tải danh sách tour: ' + error.message;
    } finally {
      isLoading = false;
    }
  }

  async function handleDelete(id: number) { 
    if (confirm('Bạn có chắc muốn xóa tour này?')) {
      try {
        const response = await fetch(`http://localhost:5000/api/tours/${id}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: response.statusText }));
          throw new Error(errorData.message || 'Không thể xóa tour');
        }

        tours = tours.filter(tour => tour.ma_tour !== id);
        alert('Xóa tour thành công!'); 
      } catch (error: any) {
        console.error('Error deleting tour:', error);
        alert('Có lỗi khi xóa tour: ' + error.message);
      }
    }
  }

  function toggleAddTourPage() {
    showAddTourPage = !showAddTourPage;
    showEditTourPage = false;
    selectedTourId = null; 
  }

  function showEditTour(id: number) { 
    selectedTourId = id;
    showEditTourPage = true;
    showAddTourPage = false;
  }

  function closeEditTourPage() {
    showEditTourPage = false;
    selectedTourId = null;
  }

  function handleTourAdded() {
    fetchTours(); 
    showAddTourPage = false; 
  }

  function handleTourUpdated() {
    fetchTours(); 
    closeEditTourPage();
  }

  function formatDate(dateString: string | null | undefined): string {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Ngày không hợp lệ';
      return date.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (e) {
      console.error("Error formatting date:", dateString, e);
      return 'Ngày lỗi';
    }
  }

  function formatPrice(price: number | string | null | undefined): string {
    const numPrice = Number(price);
    if (price === null || price === undefined || isNaN(numPrice)) return 'N/A';
    return numPrice.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' });
  }

  function getTourType(type: Tour['loai_tour']): string {
    switch (type) {
      case 'trong_nuoc':
        return 'Trong nước';
      case 'nuoc_ngoai':
        return 'Nước ngoài';
      default:
        return 'Không xác định';
    }
  }

  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
    }
  }

  function getFilenameFromPath(path: string): string {
      if (!path) return '';
      const parts = path.split(/[\/\\]/);
      return parts[parts.length - 1];
  }


  onMount(fetchTours);
</script>

<div class="tour-management">
  <div class="content-header">
    <h2><i class="fas fa-map-marked-alt"></i> Quản lý Tour</h2>
  </div>

  {#if isLoading}
    <div class="loading-indicator">Đang tải dữ liệu tour...</div>
  {:else if fetchError}
    <div class="error-message">{fetchError}</div>
  {:else if !showAddTourPage && !showEditTourPage}
    <div class="actions">
      <button class="add-button" on:click={toggleAddTourPage}>
        <i class="fas fa-plus"></i> Thêm Tour Mới
      </button>
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="search"
          placeholder="Tìm kiếm theo tên, địa điểm, mô tả..."
          bind:value={searchQuery}
          aria-label="Tìm kiếm tour"
        >
      </div>
    </div>

    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Mã</th>
            <th>Hình ảnh</th> 
            <th>Tên Tour</th>
            <th>Địa điểm</th>
            <th>Loại Tour</th>
            <th>Giá</th>
            <th>Bắt đầu</th>
            <th>Kết thúc</th>
            <th>Chỗ trống</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {#each displayedTours as tour (tour.ma_tour)} 
            <tr>
              <td>{tour.ma_tour}</td>
              <td class="image-cell">
                {#if tour.hinh_anh}
                  <img
                    src={
                      tour.hinh_anh.startsWith('data:image')
                        ? tour.hinh_anh
                        : tour.hinh_anh.startsWith('http')
                          ? tour.hinh_anh
                          : `/images/${getFilenameFromPath(tour.hinh_anh)}`
                    }
                    alt="Hình ảnh {tour.ten_tour}"
                    class="tour-thumbnail"
                    loading="lazy"
                    on:error={(e: Event) => {
                      console.error(`Image failed to load: ${tour.hinh_anh}`);
                      const imgElement = e.target as HTMLImageElement;

                      if (tour.hinh_anh.startsWith('data:image')) {
                        console.log('Base64 image failed to load, showing error message');
                        imgElement.style.display = 'none';
                        if (imgElement.nextElementSibling instanceof HTMLElement) {
                          imgElement.nextElementSibling.style.display = 'flex';
                        }
                      }
                      else if (!tour.hinh_anh.startsWith('http')) {
                        const backendUrl = `http://localhost:5000${tour.hinh_anh.startsWith('/') ? '' : '/'}${tour.hinh_anh}`;
                        console.log('Trying backend URL:', backendUrl);

                        imgElement.onerror = () => {
                          console.error(`Backend URL also failed: ${backendUrl}`);
                          imgElement.style.display = 'none';
                          if (imgElement.nextElementSibling instanceof HTMLElement) {
                            imgElement.nextElementSibling.style.display = 'flex';
                          }
                        };

                        imgElement.src = backendUrl;
                      } else {
                        imgElement.style.display = 'none';
                        if (imgElement.nextElementSibling instanceof HTMLElement) {
                          imgElement.nextElementSibling.style.display = 'flex';
                        }
                      }
                    }}
                  />
                  <div class="no-image" style="display: none;">Ảnh lỗi</div>
                {:else}
                  <div class="no-image">Không có ảnh</div>
                {/if}
              </td>
              <td class="tour-name">{tour.ten_tour}</td>
              <td>{tour.dia_diem}</td>
              <td>
                <span class="tour-type-badge {tour.loai_tour || 'unknown'}">
                  {getTourType(tour.loai_tour)}
                </span>
              </td>
              <td class="price">{formatPrice(tour.gia)}</td>
              <td>{formatDate(tour.ngay_bat_dau)}</td>
              <td>{formatDate(tour.ngay_ket_thuc)}</td>
              <td class="seats">{tour.so_cho_trong}</td>
              <td class="actions-cell">
                <button
                  class="action-btn edit"
                  aria-label="Sửa tour {tour.ten_tour}"
                  title="Sửa tour"
                  on:click={() => showEditTour(tour.ma_tour)}
                >
                  <i class="fas fa-edit" aria-hidden="true"></i>
                </button>
                <button
                  class="action-btn delete"
                  aria-label="Xóa tour {tour.ten_tour}"
                  title="Xóa tour"
                  on:click={() => handleDelete(tour.ma_tour)}
                >
                  <i class="fas fa-trash" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          {:else}
             <tr>
                <td colspan="10" class="no-data"> <!-- Adjusted colspan -->
                    {#if tours.length === 0}
                        Chưa có tour nào được tạo.
                    {:else}
                        Không tìm thấy tour nào phù hợp.
                    {/if}
                </td>
            </tr>
          {/each} <!-- End of #each -->
        </tbody>
      </table>
    </div>

    {#if totalPages > 1}
      <div class="pagination">
        <button on:click={() => goToPage(currentPage - 1)} disabled={currentPage === 1}>
          <i class="fas fa-chevron-left"></i> Trước
        </button>
        {#each Array(totalPages) as _, index}
          {@const pageNum = index + 1}
          <button
            class:active={currentPage === pageNum}
            on:click={() => goToPage(pageNum)}
            aria-label="Đi đến trang {pageNum}"
            aria-current={currentPage === pageNum ? 'page' : undefined}
          >
            {pageNum}
          </button>
        {/each}
        <button
          on:click={() => goToPage(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Sau <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    {/if}

  {:else if showAddTourPage}
    <!-- Add Tour View -->
    <div class="add-tour-container">
      <div class="view-header">
        <button class="back-button" on:click={toggleAddTourPage}>
          <i class="fas fa-arrow-left"></i> Quay lại danh sách
        </button>
      </div>
      <AddTourForm on:tourAdded={handleTourAdded} />
    </div>
  {:else if showEditTourPage && selectedTourId !== null}
    <!-- Edit Tour View -->
    <div class="edit-tour-container">
      <div class="view-header">
        <button class="back-button" on:click={closeEditTourPage}>
          <i class="fas fa-arrow-left"></i> Quay lại danh sách
        </button>
      </div>
      <EditTourForm
        tourId={selectedTourId}
        on:tourUpdated={handleTourUpdated}
        on:cancel={closeEditTourPage}
      />
    </div>
  {/if}
</div>

<style>
  /* General Styles */
  .tour-management {
    padding: 25px;
    background-color: #f8f9fa; /* Lighter background */
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* Common UI font */
  }

  .content-header h2 {
    font-size: 26px;
    color: #343a40; /* Darker heading */
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6; /* Slightly darker border */
  }

  .actions {
    display: flex;
    justify-content: space-between;
    align-items: center; /* Align items vertically */
    margin-bottom: 25px;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
    gap: 15px; /* Add gap between items */
  }

  .add-button {
    background-color: #007bff; /* Primary blue */
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: 5px;
    cursor: pointer;
    display: inline-flex; /* Use inline-flex */
    align-items: center;
    gap: 8px;
    font-weight: 500;
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
  }

  .add-button:hover {
    background-color: #0056b3;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
  }

  .search-box {
    position: relative;
    min-width: 250px; /* Minimum width */
    flex-grow: 1; /* Allow search box to grow */
    max-width: 400px; /* Max width */
  }

  .search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d; /* Muted icon color */
    pointer-events: none; /* Prevent icon from blocking input */
  }

  .search-box input[type="search"] {
    width: 100%;
    padding: 10px 12px 10px 40px; /* Adjust padding for icon */
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }
   .search-box input[type="search"]:focus {
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      outline: none;
   }

  /* Table Styles */
  .table-container {
    overflow-x: auto; /* Enable horizontal scroll on small screens */
    margin-bottom: 25px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }

  table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
    font-size: 14px; /* Slightly smaller base font */
  }

  th, td {
    padding: 14px 16px; /* Increased padding */
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
    white-space: nowrap; /* Prevent text wrapping initially */
  }

  th {
    background-color: #e9ecef; /* Lighter header background */
    font-weight: 600;
    color: #495057; /* Header text color */
    white-space: nowrap; /* Keep headers on one line */
  }

  /* Allow wrapping for specific columns */
  td.tour-name {
      white-space: normal;
      min-width: 150px; /* Ensure name column has some width */
  }


  tr:last-child td {
      border-bottom: none; /* Remove border from last row */
  }

  tr:hover {
    background-color: #f8f9fa; /* Subtle hover effect */
  }

  /* Image Column Styles (Re-added) */
  .image-cell {
    width: 100px; /* Fixed width for consistency */
    padding: 8px 10px; /* Adjust padding */
    text-align: center;
  }

  .tour-thumbnail {
    width: 80px; /* Thumbnail width */
    height: 60px; /* Thumbnail height */
    object-fit: cover; /* Crop image nicely */
    border-radius: 4px;
    border: 1px solid #e9ecef; /* Light border */
    vertical-align: middle; /* Align with text */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    background-color: #f8f9fa; /* Background for loading state */
  }

  .tour-thumbnail:hover {
    transform: scale(1.1); /* Slight zoom on hover */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .no-image {
    width: 80px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
    color: #6c757d;
    font-size: 11px;
    border-radius: 4px;
    margin: 0 auto; /* Center the placeholder */
    text-align: center;
    line-height: 1.2;
  }


  /* Other Cell Styles */
   .price {
      font-weight: 500;
      color: #28a745; /* Green for price */
      min-width: 120px;
   }
   .seats {
       text-align: center;
   }

  .tour-type-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 12px; /* Pill shape */
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize; /* Capitalize first letter */
  }

  .tour-type-badge.trong_nuoc {
    background-color: rgba(0, 123, 255, 0.1); /* Light blue */
    color: #0056b3;
  }

  .tour-type-badge.nuoc_ngoai {
    background-color: rgba(255, 193, 7, 0.1); /* Light yellow */
    color: #b38600;
  }

  .tour-type-badge.unknown {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
  }

  .actions-cell {
    display: flex;
    gap: 10px; /* Increased gap */
    align-items: center;
    justify-content: center; /* Center buttons */
    min-width: 100px; /* Ensure space for buttons */
  }

  .action-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px; /* Slightly larger icons */
    padding: 6px;
    border-radius: 50%; /* Circular buttons */
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease, color 0.2s ease;
    margin-top: 20px;
  }

  .action-btn.edit {
    color: #007bff; /* Primary blue */
  }
  .action-btn.edit:hover {
    background-color: rgba(0, 123, 255, 0.1);
  }

  .action-btn.delete {
    color: #dc3545; /* Danger red */
  }
   .action-btn.delete:hover {
    background-color: rgba(220, 53, 69, 0.1);
  }

  /* Pagination Styles */
  .pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 25px;
    flex-wrap: wrap; /* Allow wrapping */
  }

  .pagination button {
    padding: 8px 14px;
    border: 1px solid #dee2e6;
    background-color: white;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.2s ease;
    display: inline-flex; /* Align icon and text */
    align-items: center;
    gap: 5px;
    font-size: 14px;
  }

  .pagination button.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
    font-weight: 600;
  }

  .pagination button:hover:not(.active):not(:disabled) {
    background-color: #e9ecef;
    border-color: #ced4da;
  }

  .pagination button:disabled {
    color: #adb5bd; /* Lighter text for disabled */
    cursor: not-allowed;
    background-color: #f8f9fa;
  }

  /* No Data / Loading / Error Styles */
  .no-data, .loading-indicator, .error-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-size: 16px;
  }
  .error-message {
      color: #721c24;
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 5px;
  }

  /* Back Button and View Headers */
  .view-header {
      margin-bottom: 20px;
  }

  .back-button {
    background-color: #6c757d; /* Secondary gray */
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: 5px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    transition: background-color 0.2s ease;
  }

  .back-button:hover {
    background-color: #5a6268;
  }

  .add-tour-container, .edit-tour-container {
      padding: 20px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      margin-top: 20px; /* Add some space when shown */
  }

</style>
