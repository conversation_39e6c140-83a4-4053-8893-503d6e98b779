<script>
    import { onMount, onDestroy } from 'svelte';
    import AOS from 'aos';
    import 'aos/dist/aos.css';
  
    onMount(() => {
      AOS.init({
        duration: 1000,  
        easing: 'ease-in-out',  
        once: false,  
      });
  
      onDestroy(() => {
        AOS.refresh();  
      });
  
      window.addEventListener('scroll', () => {
        AOS.refresh();
      });
    });
  </script>
  
  <section class="container my-5">
    <h2 class="text-center">T<PERSON>h năng nổi bật</h2>
    <div class="row">
      <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
        <div class="feature-box text-center p-4 border rounded">
          <i class="fas fa-map-marked-alt fa-3x"></i>
          <h5>Chinh Phục Cảnh Quan Việt Nam</h5>
          <p>Khám phá những cảnh đẹp hùng vĩ của Vi<PERSON>t Nam.</p>
          <img src="images/Đà Nẵng - Hội An 4N3Đ.jpg" alt="Cảnh Quan Việt Nam" class="feature-image" />
        </div>
      </div>
      <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
        <div class="feature-box text-center p-4 border rounded">
          <i class="fas fa-globe-asia fa-3x"></i>
          <h5>Trải Nghiệm Đặc Sắc</h5>
          <p>Tham gia các lễ hội và hoạt động văn hóa.</p>
          <img src="images/Miền Tây 2N1Đ.jpg" alt="Trải Nghiệm Đặc Sắc" class="feature-image" />
        </div>
      </div>
      <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
        <div class="feature-box text-center p-4 border rounded">
          <i class="fas fa-umbrella-beach fa-3x"></i>
          <h5>Khám Phá Biển Đảo</h5>
          <p>Chiêm ngưỡng vẻ đẹp của những bãi biển xanh ngát.</p>
          <img src="images/Maldives Thiên Đường Biển - 7N6Đ.jpg" alt="Khám Phá Biển Đảo" class="feature-image" />
        </div>
      </div>
    </div>
  </section>
  
  <style>
    .feature-image {
      width: 100%;
      height: 200px; 
      object-fit: cover; 
      border-radius: 8px;
      margin-top: 1.25rem;
    }
  
    .feature-box {
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      padding: 20px;
      transition: transform 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-height: 400px; 
    }
  
    .feature-box:hover {
      transform: translateY(-10px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    }
  
    .feature-box h5 {
      font-size: 1.25rem;
      color: #333;
      margin-top: 1rem;
      margin-bottom: 0.75rem;
    }
  
    .feature-box p {
      font-size: 1rem;
      color: #666;
    }
  </style>
