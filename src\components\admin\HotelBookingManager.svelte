<script lang="ts">
  import { onMount } from 'svelte';
  import { hotelBookings, user } from '../../stores/userStore';

  interface HotelBooking {
    id: number;
    hotelId: string | number;
    userId: string | number;
    hotelDetails: {
      ma_khach_san: number;
      ten_khach_san: string;
      dia_diem: string;
      dia_chi?: string;
      so_sao?: number;
      gia?: number;
      hinh_anh?: string;
    };
    checkInDate: string;
    checkOutDate: string;
    guests: number;
    rooms: number;
    totalPrice: number;
    bookingDate: string;
    status: string;
    ten_khach_hang?: string;
    email?: string;
    so_dien_thoai?: string;
  }

  let allBookings: HotelBooking[] = [];
  let currentPage = 1;
  let itemsPerPage = 10;
  let searchQuery = '';
  let statusFilter = 'all';
  let isLoading = true;
  let error = '';

  function sortBookings(bookingsToSort: HotelBooking[]): HotelBooking[] {
    return [...bookingsToSort].sort((a, b) => {
      return new Date(b.bookingDate).getTime() - new Date(a.bookingDate).getTime();
    });
  }

  function getAdjustedBookingId(bookings: HotelBooking[], currentBooking: HotelBooking): number {
    if (bookings.length === 0) return 1;

    const minBookingId = Math.min(...bookings.map(b => b.id));

    return currentBooking.id - minBookingId + 1;
  }

  function formatDate(dateString: string): string {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  }

  function formatPrice(price: number): string {
    if (price === undefined || price === null) return '0 ₫';

    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  }

  $: filteredBookings = allBookings.filter(booking => {
    if (statusFilter !== 'all' && booking.status !== statusFilter) {
      return false;
    }

    const searchLower = searchQuery.toLowerCase();
    return (
      (booking.hotelDetails?.ten_khach_san?.toLowerCase().includes(searchLower) || '') ||
      (booking.ten_khach_hang?.toLowerCase().includes(searchLower) || '') ||
      (booking.email?.toLowerCase().includes(searchLower) || '') ||
      (booking.hotelDetails?.dia_diem?.toLowerCase().includes(searchLower) || '')
    );
  });

  $: totalPages = Math.ceil(filteredBookings.length / itemsPerPage);
  $: paginatedBookings = filteredBookings.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  function changePage(newPage: number) {
    if (newPage >= 1 && newPage <= totalPages) {
      currentPage = newPage;
    }
  }

  async function loadBookings() {
    isLoading = true;
    error = '';

    try {
      let storeBookings = $hotelBookings;
      console.log('Bookings from store:', storeBookings);

      if (storeBookings.length > 0) {
        allBookings = storeBookings.map((booking: HotelBooking) => ({
          ...booking,
          ten_khach_hang: booking.ten_khach_hang || ($user ? $user.ho_ten : '') || '',
          email: booking.email || ($user ? $user.email : '') || '',
          so_dien_thoai: booking.so_dien_thoai || ($user ? $user.so_dien_thoai : '') || ''
        }));

        allBookings = sortBookings(allBookings);
      } else {
        allBookings = [];
      }
    } catch (err) {
      console.error('Error loading hotel bookings:', err);
      error = err.message || 'Đã xảy ra lỗi khi tải danh sách đặt phòng khách sạn';
    } finally {
      isLoading = false;
    }
  }

  async function deleteBooking(booking: HotelBooking) {
    if (confirm('Bạn có chắc chắn muốn xóa đặt phòng này không?')) {
      try {
        console.log(`Xóa đặt phòng ID ${booking.id}`);

        error = '';
        isLoading = true;

        hotelBookings.update(currentBookings =>
          currentBookings.filter((b: HotelBooking) => b.id !== booking.id)
        );

        await loadBookings();

        const event = new CustomEvent('bookingStatusChanged');
        document.dispatchEvent(event);

      } catch (err) {
        console.error('Lỗi khi xóa đặt phòng:', err);
        error = err.message || 'Đã xảy ra lỗi khi xóa đặt phòng';
      } finally {
        isLoading = false;
      }
    }
  }

  async function updateBookingStatus(booking: HotelBooking, newStatus: string) {
    try {
      console.log(`Cập nhật trạng thái cho đặt phòng ID ${booking.id} thành ${newStatus}`);

      error = '';
      isLoading = true;

      hotelBookings.update(currentBookings =>
        currentBookings.map((b: HotelBooking) => {
          if (b.id === booking.id) {
            return { ...b, status: newStatus };
          }
          return b;
        })
      );

      await loadBookings();

      const event = new CustomEvent('bookingStatusChanged');
      document.dispatchEvent(event);

    } catch (err) {
      console.error('Lỗi khi cập nhật trạng thái đặt phòng:', err);
      error = err.message || 'Đã xảy ra lỗi khi cập nhật trạng thái đặt phòng';
    } finally {
      isLoading = false;
    }
  }

  async function undoConfirmation(booking: HotelBooking) {
    if (confirm('Bạn có chắc chắn muốn hoàn tác xác nhận cho đặt phòng này không?')) {
      await updateBookingStatus(booking, 'pending');
    }
  }

  async function undoCompletion(booking: HotelBooking) {
    if (confirm('Bạn có chắc chắn muốn hoàn tác trạng thái hoàn thành cho đặt phòng này không?')) {
      await updateBookingStatus(booking, 'confirmed');
    }
  }

  onMount(() => {
    loadBookings();

    const handleBookingStatusChanged = () => {
      console.log('HotelBookingManager: Detected booking status change, refreshing data...');
      loadBookings();
    };

    document.addEventListener('bookingStatusChanged', handleBookingStatusChanged);

    return () => {
      document.removeEventListener('bookingStatusChanged', handleBookingStatusChanged);
    };
  });
</script>

<div class="booking-management">
  <div class="actions">
    <div class="filters">
      <div class="status-filter">
        <label for="status-select">Lọc theo trạng thái:</label>
        <select id="status-select" bind:value={statusFilter}>
          <option value="all">Tất cả</option>
          <option value="pending">Chờ xác nhận</option>
          <option value="awaiting_payment">Chờ thanh toán</option>
          <option value="confirmed">Đã xác nhận</option>
          <option value="cancelled">Đã hủy</option>
          <option value="completed">Hoàn thành</option>
        </select>
      </div>
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="search"
          placeholder="Tìm kiếm theo tên khách sạn, khách hàng, email..."
          bind:value={searchQuery}
          aria-label="Tìm kiếm đặt phòng khách sạn"
        >
      </div>
    </div>
    <button on:click={loadBookings} class="refresh-button" aria-label="Làm mới dữ liệu">
      <i class="fas fa-sync-alt"></i> Làm mới
    </button>
  </div>

  {#if isLoading}
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Đang tải danh sách đặt phòng khách sạn...</p>
    </div>
  {:else if error}
    <div class="error-message">
      <i class="fas fa-exclamation-circle"></i>
      <p>{error}</p>
      <button on:click={loadBookings} class="retry-button">
        <i class="fas fa-sync-alt"></i> Thử lại
      </button>
    </div>
  {:else if allBookings.length === 0}
    <div class="empty-state">
      <i class="fas fa-hotel"></i>
      <h3>Không tìm thấy đặt phòng khách sạn</h3>
      <p>Chưa có đặt phòng khách sạn nào trong hệ thống.</p>
    </div>
  {:else if filteredBookings.length === 0}
    <div class="empty-state">
      <i class="fas fa-search"></i>
      <h3>Không tìm thấy kết quả phù hợp</h3>
      <p>Không có đặt phòng nào phù hợp với tiêu chí tìm kiếm của bạn.</p>
      <button on:click={() => { searchQuery = ''; statusFilter = 'all'; }} class="clear-filters-button">
        Xóa bộ lọc
      </button>
    </div>
  {:else}
    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Hình ảnh</th>
            <th>Khách sạn</th>
            <th>Tên khách hàng</th>
            <th>Email</th>
            <th>Số điện thoại</th>
            <th>Ngày nhận phòng</th>
            <th>Ngày trả phòng</th>
            <th>Số người</th>
            <th>Số phòng</th>
            <th>Tổng tiền</th>
            <th>Trạng thái</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {#each paginatedBookings as booking (booking.id)}
            <tr>
              <td>{getAdjustedBookingId(allBookings, booking)}</td>
              <td class="hotel-image-cell">
                {#if booking.hotelDetails?.hinh_anh}
                  <img
                    src={`/images/${booking.hotelDetails.hinh_anh}`}
                    alt={booking.hotelDetails.ten_khach_san || 'Khách sạn'}
                    on:error={(e) => {
                      (e.target as HTMLImageElement).src = '/images/default-tour.svg';
                    }}
                  />
                {:else}
                  <img src="/images/default-tour.svg" alt="Hình ảnh mặc định" />
                {/if}
              </td>
              <td>
                <div class="hotel-info">
                  <span class="hotel-name">{booking.hotelDetails?.ten_khach_san || 'N/A'}</span>
                  <span class="hotel-location">{booking.hotelDetails?.dia_diem || 'N/A'}</span>
                </div>
              </td>
              <td>{booking.ten_khach_hang || 'N/A'}</td>
              <td>{booking.email || 'N/A'}</td>
              <td>{booking.so_dien_thoai || 'N/A'}</td>
              <td>{formatDate(booking.checkInDate)}</td>
              <td>{formatDate(booking.checkOutDate)}</td>
              <td>{booking.guests}</td>
              <td>{booking.rooms}</td>
              <td>{formatPrice(booking.totalPrice)}</td>
              <td>
                <span class="status-badge {booking.status}">
                  {#if booking.status === 'pending'}
                    Chờ xác nhận
                  {:else if booking.status === 'awaiting_payment'}
                    Chờ thanh toán
                  {:else if booking.status === 'confirmed'}
                    Đã xác nhận/thanh toán
                  {:else if booking.status === 'cancelled'}
                    Đã hủy
                  {:else if booking.status === 'completed'}
                    Hoàn thành
                  {:else}
                    {booking.status}
                  {/if}
                </span>
              </td>
              <td>
                <div class="action-buttons">
                  {#if booking.status === 'pending'}
                    <button
                      on:click={() => updateBookingStatus(booking, 'confirmed')}
                      class="action-button confirm"
                      title="Xác nhận đặt phòng"
                      aria-label="Xác nhận đặt phòng"
                    >
                      <i class="fas fa-check"></i>
                    </button>
                    <button
                      on:click={() => updateBookingStatus(booking, 'cancelled')}
                      class="action-button cancel"
                      title="Hủy đặt phòng"
                      aria-label="Hủy đặt phòng"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  {:else if booking.status === 'confirmed'}
                    <button
                      on:click={() => updateBookingStatus(booking, 'completed')}
                      class="action-button complete"
                      title="Đánh dấu hoàn thành"
                      aria-label="Đánh dấu hoàn thành"
                    >
                      <i class="fas fa-check-double"></i>
                    </button>
                    <button
                      on:click={() => undoConfirmation(booking)}
                      class="action-button undo"
                      title="Hoàn tác xác nhận"
                      aria-label="Hoàn tác xác nhận"
                    >
                      <i class="fas fa-undo"></i>
                    </button>
                  {:else if booking.status === 'completed'}
                    <button
                      on:click={() => undoCompletion(booking)}
                      class="action-button undo"
                      title="Hoàn tác hoàn thành"
                      aria-label="Hoàn tác hoàn thành"
                    >
                      <i class="fas fa-undo"></i>
                    </button>
                  {/if}
                  <button
                    on:click={() => deleteBooking(booking)}
                    class="action-button delete"
                    title="Xóa đặt phòng"
                    aria-label="Xóa đặt phòng"
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {#if totalPages > 1}
      <div class="pagination">
        <button
          class="pagination-btn"
          disabled={currentPage === 1}
          on:click={() => changePage(1)}
          aria-label="Trang đầu"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button
          class="pagination-btn"
          disabled={currentPage === 1}
          on:click={() => changePage(currentPage - 1)}
          aria-label="Trang trước"
        >
          <i class="fas fa-angle-left"></i>
        </button>

        {#each Array(totalPages) as _, i}
          {#if i + 1 === currentPage || i + 1 === currentPage - 1 || i + 1 === currentPage + 1 || i + 1 === 1 || i + 1 === totalPages}
            <button
              class="pagination-btn"
              class:active={currentPage === i + 1}
              on:click={() => changePage(i + 1)}
            >
              {i + 1}
            </button>
          {:else if i + 1 === currentPage - 2 || i + 1 === currentPage + 2}
            <span class="pagination-ellipsis">...</span>
          {/if}
        {/each}

        <button
          class="pagination-btn"
          disabled={currentPage === totalPages}
          on:click={() => changePage(currentPage + 1)}
          aria-label="Trang sau"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button
          class="pagination-btn"
          disabled={currentPage === totalPages}
          on:click={() => changePage(totalPages)}
          aria-label="Trang cuối"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
    {/if}
  {/if}
</div>

<style>
  .booking-management {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .filters {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
  }

  .status-filter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .status-filter label {
    font-weight: 500;
    color: #555;
  }

  .status-filter select {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: white;
    font-size: 0.95rem;
    min-width: 150px;
  }

  .search-box {
    position: relative;
    flex-grow: 1;
    max-width: 400px;
  }

  .search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #777;
  }

  .search-box input {
    width: 100%;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.95rem;
  }

  .refresh-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: #f0f0f0;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.95rem;
    transition: background-color 0.2s;
  }

  .refresh-button:hover {
    background-color: #e0e0e0;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    gap: 1rem;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    background-color: #fff5f5;
    border-radius: 8px;
    color: #e53e3e;
    gap: 1rem;
  }

  .error-message i {
    font-size: 2rem;
  }

  .retry-button {
    padding: 0.5rem 1rem;
    background-color: #e53e3e;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.2s;
  }

  .retry-button:hover {
    background-color: #c53030;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    gap: 1rem;
    text-align: center;
  }

  .empty-state i {
    font-size: 3rem;
    color: #a0aec0;
    margin-bottom: 1rem;
  }

  .empty-state h3 {
    font-size: 1.5rem;
    color: #4a5568;
    margin: 0;
  }

  .empty-state p {
    color: #718096;
    margin-bottom: 1rem;
  }

  .clear-filters-button {
    padding: 0.5rem 1rem;
    background-color: #4299e1;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .clear-filters-button:hover {
    background-color: #3182ce;
  }

  .table-container {
    overflow-x: auto;
    margin-bottom: 1.5rem;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
  }

  th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
  }

  th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  tr:last-child td {
    border-bottom: none;
  }

  tr:hover td {
    background-color: #f9f9f9;
  }

  .hotel-image-cell {
    width: 80px;
    padding: 0.5rem;
  }

  .hotel-image-cell img {
    width: 70px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
  }

  /* Hotel column styling */
  table th:nth-child(3),
  table td:nth-child(3) {
    min-width: 250px;
    width: 250px;
    max-width: 300px;
  }

  .hotel-info {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .hotel-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 240px;
  }

  .hotel-location {
    font-size: 0.85rem;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 240px;
  }

  .status-badge {
    display: inline-block;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    min-width: 100px;
  }

  .status-badge.pending {
    background-color: #fef3c7;
    color: #92400e;
  }

  .status-badge.awaiting_payment {
    background-color: #e6f7ff;
    color: #0c5460;
  }

  .status-badge.confirmed {
    background-color: #def7ec;
    color: #046c4e;
  }

  .status-badge.cancelled {
    background-color: #fee2e2;
    color: #b91c1c;
  }

  .status-badge.completed {
    background-color: #e0e7ff;
    color: #3730a3;
  }

  .action-buttons {
    display: flex;
    gap: 0.5rem;
  }

  .action-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .action-button.confirm {
    background-color: #def7ec;
    color: #046c4e;
  }

  .action-button.confirm:hover {
    background-color: #bcf0da;
  }

  .action-button.cancel {
    background-color: #fee2e2;
    color: #b91c1c;
  }

  .action-button.cancel:hover {
    background-color: #fecaca;
  }

  .action-button.complete {
    background-color: #e0e7ff;
    color: #3730a3;
  }

  .action-button.complete:hover {
    background-color: #c7d2fe;
  }

  .action-button.undo {
    background-color: #fef3c7;
    color: #92400e;
  }

  .action-button.undo:hover {
    background-color: #fde68a;
  }

  .action-button.delete {
    background-color: #fee2e2;
    color: #b91c1c;
  }

  .action-button.delete:hover {
    background-color: #fecaca;
  }

  .pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
  }

  .pagination-btn {
    min-width: 36px;
    height: 36px;
    border: 1px solid #e2e8f0;
    background-color: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
  }

  .pagination-btn:hover:not(:disabled) {
    background-color: #edf2f7;
  }

  .pagination-btn.active {
    background-color: #4299e1;
    color: white;
    border-color: #4299e1;
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
  }
</style>
