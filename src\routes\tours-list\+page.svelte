<script>
  import Navbar from '../../components/Navbar.svelte';
  import Footer from '../../components/Footer.svelte';
  import ToursList from '../../components/ToursList.svelte';
</script>

<div class="tours-list-page">
  <Navbar />
  
  <div class="page-content">
    <div class="page-header">
      <h1><PERSON>h <PERSON>ch Tour Du Lịch</h1>
      <p>Khám phá các tour du lịch hấp dẫn của chúng tôi</p>
    </div>
    
    <ToursList />
  </div>
  
  <Footer />
</div>

<style>
  .tours-list-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
  
  .page-content {
    flex: 1;
    padding: 2rem 1rem;
  }
  
  .page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 3rem 1rem;
    background: linear-gradient(to right, #1a73e8, #6c5ce7);
    color: white;
    border-radius: 0 0 20px 20px;
  }
  
  .page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
  }
  
  .page-header p {
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto;
    opacity: 0.9;
  }
  
  @media (max-width: 768px) {
    .page-header {
      padding: 2rem 1rem;
    }
    
    .page-header h1 {
      font-size: 2rem;
    }
    
    .page-header p {
      font-size: 1rem;
    }
  }
</style>
