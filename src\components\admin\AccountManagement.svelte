<script lang="ts">
  import { onMount } from 'svelte';
  import AddAccountForm from './AddAccountForm.svelte';
  import EditAccountForm from './EditAccountForm.svelte';

  interface Account {
    ma_nguoi_dung: number;
    ho_ten: string;
    email: string;
    so_dien_thoai: string;
    vai_tro: string;
  }

  let accounts: Account[] = [];
  let searchQuery = '';
  let showAddAccountPage = false; 
  let showEditAccountPage = false; 
  let selectedAccountId: number | null = null; 

  async function fetchAccounts() {
    console.log('fetchAccounts called');
    try {
      const url = 'http://localhost:5000/api/account/users';
      console.log('Fetching from URL:', url);

      const response = await fetch(url);
      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error('Failed to fetch accounts');
      }

      const data = await response.json();
      console.log('Received data:', data);

      if (data && data.users && Array.isArray(data.users)) {
        accounts = data.users;
        console.log('Accounts data updated:', accounts);
      } else if (data && data.nguoi_dung && Array.isArray(data.nguoi_dung)) {
        accounts = data.nguoi_dung;
        console.log('Accounts data updated:', accounts);
      } else if (Array.isArray(data)) {
        accounts = data;
        console.log('Accounts data updated:', accounts);
      } else {
        console.error('Invalid data structure:', data);
        throw new Error('Dữ liệu không hợp lệ');
      }
    } catch (error) {
      console.error('Error fetching accounts:', error);
      alert('Có lỗi khi tải danh sách tài khoản: ' + error.message);
    }
  }

  onMount(() => {
    fetchAccounts();
  });

  function toggleAddAccountPage() {
    showAddAccountPage = !showAddAccountPage;
    showEditAccountPage = false;
  }
  function handleAccountAdded() {
    fetchAccounts();
    toggleAddAccountPage();
  }

  function showEditAccount(id: number): void {
    console.log('showEditAccount called with id:', id);

    if (!id) {
      console.error('Invalid id:', id);
      alert('ID tài khoản không hợp lệ');
      return;
    }
    selectedAccountId = id;
    showEditAccountPage = true;
    showAddAccountPage = false;
    console.log('showEditAccountPage:', showEditAccountPage);
    console.log('selectedAccountId:', selectedAccountId);
  }
  function closeEditAccountPage() {
    console.log('closeEditAccountPage called');
    showEditAccountPage = false;
    selectedAccountId = null;
  }
  function handleAccountUpdated() {
    console.log('handleAccountUpdated called');
    fetchAccounts();
    closeEditAccountPage();
  }
  async function handleDelete(id: number): Promise<void> {
    console.log('handleDelete called with id:', id);

    if (confirm('Bạn có chắc muốn xóa tài khoản này?')) {
      try {
        const url = `http://localhost:5000/api/account/${id}`;
        console.log('Sending DELETE request to:', url);

        const response = await fetch(url, {
          method: 'DELETE'
        });

        console.log('DELETE response status:', response.status);

        if (!response.ok) {
          let errorMessage = 'Không thể xóa tài khoản';

          try {
            const errorData = await response.json();
            console.error('Error response data:', errorData);

            if (errorData && errorData.message) {
              errorMessage = errorData.message;
            } else if (errorData && errorData.error) {
              errorMessage = errorData.error;
            }
          } catch (parseError) {
            console.error('Error parsing error response:', parseError);
          }

          throw new Error(errorMessage);
        }
        await fetchAccounts();
        alert('Xóa tài khoản thành công!');
      } catch (error: any) {
        console.error('Error deleting account:', error);
        alert('Có lỗi khi xóa tài khoản: ' + error.message);
      }
    }
  }
  function sortAccounts(accountsToSort: Account[]): Account[] {
    return [...accountsToSort].sort((a, b) => a.ma_nguoi_dung - b.ma_nguoi_dung);
  }
  function getAdjustedUserId(accounts: Account[], currentAccount: Account): number {
    if (accounts.length === 0) return 1;
    const minUserId = Math.min(...accounts.map(a => a.ma_nguoi_dung));
    return currentAccount.ma_nguoi_dung - minUserId + 1;
  }
  $: filteredAccounts = sortAccounts(accounts.filter(account => {
    const searchLower = searchQuery.toLowerCase();
    return (
      account.ho_ten?.toLowerCase().includes(searchLower) ||
      account.email?.toLowerCase().includes(searchLower) ||
      account.so_dien_thoai?.toLowerCase().includes(searchLower) ||
      (account.vai_tro === 'admin' && 'quản trị viên'.includes(searchLower)) ||
      (account.vai_tro === 'khach_hang' && 'khách hàng'.includes(searchLower))
    );
  }));
</script>

<div class="account-management">
  {#if !showAddAccountPage && !showEditAccountPage}
    <div class="actions">
      <button class="add-button" on:click={toggleAddAccountPage}>
        <i class="fas fa-plus"></i> Thêm Tài Khoản
      </button>
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="text"
          placeholder="Tìm kiếm tài khoản..."
          bind:value={searchQuery}
        >
      </div>
    </div>

    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Mã người dùng</th>
            <th>Họ tên</th>
            <th>Email</th>
            <th>Số điện thoại</th>
            <th>Vai trò</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {#each filteredAccounts as account}
            <tr>
              <td>{getAdjustedUserId(accounts, account)}</td>
              <td>{account.ho_ten}</td>
              <td>{account.email}</td>
              <td>{account.so_dien_thoai}</td>
              <td>
                <span class="role-badge {account.vai_tro}">
                  {account.vai_tro === 'admin' ? 'Quản trị viên' : 'Khách hàng'}
                </span>
              </td>
              <td class="actions-cell">
                <button
                  class="action-btn edit"
                  aria-label="Sửa thông tin"
                  on:click={() => showEditAccount(account.ma_nguoi_dung)}
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button
                  class="action-btn delete"
                  aria-label="Xóa tài khoản"
                  on:click={() => handleDelete(account.ma_nguoi_dung)}
                >
                  <i class="fas fa-trash"></i>
                </button>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {:else if showAddAccountPage}
    <div class="add-account-container">
      <div class="add-account-header">
        <button class="back-button" on:click={toggleAddAccountPage}>
          <i class="fas fa-arrow-left"></i> Quay lại
        </button>
      </div>
      <AddAccountForm on:accountAdded={handleAccountAdded} />
    </div>
  {:else if showEditAccountPage}
    <div class="edit-account-container">
      <div class="edit-account-header">
        <button class="back-button" on:click={closeEditAccountPage}>
          <i class="fas fa-arrow-left"></i> Quay lại
        </button>
      </div>
      <EditAccountForm
        accountId={selectedAccountId}
        on:accountUpdated={handleAccountUpdated}
        on:cancel={closeEditAccountPage}
      />
    </div>
  {/if}
</div>

<style>
  .account-management {
    padding: 1rem;
  }

  .actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
  }

  .add-button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .add-button:hover {
    background-color: #45a049;
  }

  .search-box {
    position: relative;
    width: 300px;
  }

  .search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
  }

  .search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    outline: none;
  }

  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
  }

  th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
  }

  .role-badge,
  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
  }

  .role-badge.admin {
    background-color: #e3f2fd;
    color: #1565c0;
  }

  .role-badge.khach_hang {
    background-color: #f3e5f5;
    color: #7b1fa2;
  }

  .status-badge.active {
    background-color: #e3fcef;
    color: #0f766e;
  }

  .actions-cell {
    display: flex;
    gap: 0.5rem;
  }

  .action-btn {
    border: none;
    background: none;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s;
  }

  .action-btn.edit {
    color: #2196F3;
  }

  .action-btn.lock {
    color: #ff9800;
  }

  .action-btn.delete {
    color: #f44336;
  }

  .action-btn:hover {
    background-color: #f5f5f5;
  }

  /* Add/Edit account page styles */
  .add-account-container,
  .edit-account-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .add-account-header,
  .edit-account-header {
    margin-bottom: 2rem;
  }

  .back-button {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    transition: color 0.3s;
  }

  .back-button:hover {
    color: #333;
  }
</style>