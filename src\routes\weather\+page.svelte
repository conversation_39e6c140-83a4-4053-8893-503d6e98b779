<!-- <script lang="ts">
  import { onMount } from 'svelte';
  import Navbar from '../../components/Navbar.svelte';
  import CTA from '../../components/CTA.svelte';
  import Footer from '../../components/Footer.svelte';

  interface WeatherData {
    ma_thoi_tiet: number;
    dia_diem: string;
    ngay: string;
    nhiet_do: number;
    tinh_trang: string;
    ngay_cap_nhat?: string;
  }

  let weatherData: WeatherData[] = [];
  let filteredWeatherData: WeatherData[] = [];
  let selectedDay: WeatherData | null = null;
  let locations: string[] = [];
  let selectedLocation: string = '';
  let isLoading: boolean = true;
  let error: string | null = null;
  let newLocation: string = '';

  function addNewLocation(): void {
    if (!newLocation.trim()) {
      alert('Vui lòng nhập tên địa điểm!');
      return;
    }

    if (locations.includes(newLocation)) {
      alert('Địa điểm này đã tồn tại!');
      return;
    }

    const newData = generateWeekWeatherData(newLocation);

    weatherData = [...weatherData, ...newData];

    locations = [...locations, newLocation].sort();

    selectedLocation = newLocation;
    filterByLocation(newLocation);

    newLocation = '';
  }

  function formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Ngày không hợp lệ';

    const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
    const dayOfWeek = days[date.getDay()];

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${dayOfWeek} - ${day}/${month}/${year}`;
  }

  function getImage(condition: string): string {
    const weatherMap: Record<string, string> = {
      'Nắng': 'Nắng',
      'Mưa': 'Mưa',
      'Nhiều mây': 'Nhiều mây',
      'Giông bão': 'Giông bão',
      'Âm u': 'Nhiều mây',
      'Mưa rào': 'Mưa',
      'Nắng nhẹ': 'Nắng'
    };

    const mappedCondition = weatherMap[condition] || 'Nhiều mây';
    return `/images/${mappedCondition}.jpg`;
  }

  function filterByLocation(location: string): void {
    selectedLocation = location;
    if (!location) {
      filteredWeatherData = [...weatherData];
    } else {
      filteredWeatherData = weatherData.filter(item => item.dia_diem === location);
    }

    if (filteredWeatherData.length > 0) {
      selectedDay = filteredWeatherData[0];
    } else {
      selectedDay = null;
    }
  }

  function generateWeekWeatherData(location: string): WeatherData[] {
    const today = new Date();
    const weatherConditions = ['Nắng', 'Mưa', 'Nhiều mây', 'Giông bão', 'Nắng nhẹ'];
    const result: WeatherData[] = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);

      const randomTemp = Math.floor(Math.random() * 15) + 20; 
      const randomCondition = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];

      result.push({
        ma_thoi_tiet: i + 1,
        dia_diem: location,
        ngay: date.toISOString().split('T')[0],
        nhiet_do: randomTemp,
        tinh_trang: randomCondition,
        ngay_cap_nhat: new Date().toISOString()
      });
    }

    return result;
  }

  function ensureFullWeekData(data: WeatherData[]): WeatherData[] {
    if (data.length === 0) {
      return generateWeekWeatherData('Hà Nội');
    }

    const locationMap = new Map<string, WeatherData[]>();
    data.forEach(item => {
      if (!locationMap.has(item.dia_diem)) {
        locationMap.set(item.dia_diem, []);
      }
      locationMap.get(item.dia_diem)?.push(item);
    });

    const result: WeatherData[] = [];
    locationMap.forEach((items, location) => {
      if (items.length < 7) {
        result.push(...generateWeekWeatherData(location));
      } else {
        result.push(...items);
      }
    });

    return result;
  }

  async function fetchWeatherData(): Promise<void> {
    isLoading = true;
    error = null;

    try {
      const response = await fetch('http://localhost:5000/api/thoitiet');

      if (!response.ok) {
        throw new Error(`Lỗi ${response.status}: Không thể tải dữ liệu thời tiết.`);
      }

      const data = await response.json();
      let apiData = data.thoitiet || [];
      weatherData = ensureFullWeekData(apiData);

      const uniqueLocations = new Set<string>();
      weatherData.forEach((item: WeatherData) => {
        if (item.dia_diem) {
          uniqueLocations.add(item.dia_diem);
        }
      });
      locations = Array.from(uniqueLocations).sort() as string[];

      filteredWeatherData = [...weatherData];
      if (weatherData.length > 0) {
        selectedDay = weatherData[0];
      }

    } catch (err: any) {
      console.error('Lỗi khi tải dữ liệu thời tiết:', err);
      error = err.message;

      weatherData = generateWeekWeatherData('Hà Nội');
      locations = ['Hà Nội'];
      filteredWeatherData = [...weatherData];
      selectedDay = weatherData[0];
    } finally {
      isLoading = false;
    }
  }
  onMount(fetchWeatherData);
</script>

<style>
  .weather-container {
    position: relative;
    max-width: 100%;
    min-height: 100vh;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 2rem;
    padding: 4rem 2rem 2rem;
    margin-top: 50px;
    flex-wrap: wrap;
    animation: fadeIn 0.8s ease-in;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .background-image {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: none;
    z-index: -1;
    transition: all 1.5s ease-in-out;
    animation: zoomInOut 20s ease-in-out infinite;
  }

  @keyframes zoomInOut {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  .weather-card {
    flex: 1 1 300px;
    max-width: 400px;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.85);
    border-radius: 15px;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    animation: slideIn 0.6s ease-out;
  }

  .weather-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .info h2 {
    font-size: 1.6rem;
    margin-bottom: 0.5rem;
    color: #444;
    font-weight: 600;

  }

  .info p {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 0.3rem;

  }

  .day-list {
    flex: 1 1 300px;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
  }

  .day-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.85);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: fadeInUp 0.5s ease-out backwards;
    border: none;
  }

  .day-item:nth-child(1) { animation-delay: 0.1s; }
  .day-item:nth-child(2) { animation-delay: 0.2s; }
  .day-item:nth-child(3) { animation-delay: 0.3s; }
  .day-item:nth-child(4) { animation-delay: 0.4s; }
  .day-item:nth-child(5) { animation-delay: 0.5s; }

  .day-item:hover {
    background-color: #4285f4;
    color: white;
    transform: scale(1.03) translateX(5px);
    box-shadow: 0 6px 15px rgba(66, 133, 244, 0.3);
  }

  .day-item.active {
    background-color: #4285f4;
    color: white;
    box-shadow: 0 5px 15px rgba(66, 133, 244, 0.4);
    transform: scale(1.02);
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .day-info {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
  }

  .day-summary {
    font-weight: bold;
    font-size: 1rem;
  }

  .day-details {
    font-size: 0.95rem;
  }

  .left-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    flex: 1 1 300px;
    max-width: 400px;
  }

  .location-filter {
    background: rgba(255, 255, 255, 0.85);
    padding: 1.2rem;
    border-radius: 15px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
  }

  .location-filter label {
    font-weight: 600;
    color: #444;
    font-size: 1.1rem;
  }

  .location-filter select {
    padding: 0.8rem;
    border-radius: 8px;
    border: 1px solid #ddd;
    background-color: white;
    font-size: 1rem;
    width: 100%;
    cursor: pointer;
    transition: border-color 0.3s ease;
  }

  .location-filter select:focus {
    border-color: #4285f4;
    outline: none;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
  }

  .add-location {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .add-location input {
    padding: 0.8rem;
    border-radius: 8px;
    border: 1px solid #ddd;
    background-color: white;
    font-size: 1rem;
    width: 100%;
    transition: border-color 0.3s ease;
  }

  .add-location input:focus {
    border-color: #4285f4;
    outline: none;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
  }

  .add-button {
    padding: 0.8rem;
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
    width: 100%;
  }

  .add-button:hover {
    background-color: #3367d6;
  }

  .loading-container,
  .error-container,
  .empty-container {
    background: rgba(255, 255, 255, 0.85);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
    text-align: center;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
  }

  .error-container {
    background: rgba(255, 220, 220, 0.9);
    color: #d32f2f;
  }

  .retry-button {
    margin-top: 1rem;
    padding: 0.6rem 1.2rem;
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
  }

  .retry-button:hover {
    background-color: #3367d6;
  }

  @media (max-width: 768px) {
    .weather-container {
      flex-direction: column;
      align-items: center;
      gap: 2rem;
    }

    .weather-card, .day-list, .left-column {
      width: 90%;
      max-width: 100%;
    }

    h1 {
      font-size: 1.8rem;
    }

    .info h2 {
      font-size: 1.4rem;
    }

    .info p {
      font-size: 1rem;
    }

    .day-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.4rem;
    }

    .day-summary {
      font-size: 1.1rem;
    }
  }
</style>
<Navbar />
<div class="weather-container">
  {#if isLoading}
    <div class="loading-container">
      <p>Đang tải dữ liệu thời tiết...</p>
    </div>
  {:else if error}
    <div class="error-container">
      <p>Lỗi: {error}</p>
      <button on:click={fetchWeatherData} class="retry-button">Thử lại</button>
    </div>
  {:else if !selectedDay}
    <div class="empty-container">
      <p>Không có dữ liệu thời tiết.</p>
    </div>
  {:else}
    <img class="background-image" src={getImage(selectedDay.tinh_trang)} alt="weather background" />

    <div class="left-column">
      <div class="location-filter">
        <label for="location-select">Chọn địa điểm:</label>
        <select
          id="location-select"
          bind:value={selectedLocation}
          on:change={() => filterByLocation(selectedLocation)}
        >
          <option value="">Tất cả địa điểm</option>
          {#each locations as location}
            <option value={location}>{location}</option>
          {/each}
        </select>

        <div class="add-location">
          <input
            type="text"
            id="new-location"
            placeholder="Nhập tên địa điểm mới"
            bind:value={newLocation}
          />
          <button on:click={addNewLocation} class="add-button">Thêm địa điểm</button>
        </div>
      </div>

      <div class="weather-card">
        <h1>🌤️ Dự Báo Thời Tiết</h1>

        <div class="info">
          <h2>{formatDate(selectedDay.ngay)} - {selectedDay.dia_diem}</h2>
          <p>🌡️ Nhiệt độ: {selectedDay.nhiet_do}°C</p>
          <p>🌦️ Thời tiết: {selectedDay.tinh_trang}</p>
          <p>📅 Cập nhật: {new Date(selectedDay.ngay_cap_nhat || selectedDay.ngay).toLocaleDateString('vi-VN')}</p>
        </div>
      </div>
    </div>

    <div class="day-list">
      {#each filteredWeatherData as day}
        <button
          type="button"
          class="day-item {day === selectedDay ? 'active' : ''}"
          on:click={() => selectedDay = day}
          on:keydown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              selectedDay = day;
            }
          }}
          aria-pressed={day === selectedDay}
          aria-label="Select weather for {formatDate(day.ngay)} in {day.dia_diem}"
        >
          <div class="day-info">
            <div class="day-summary">{formatDate(day.ngay)} - {day.dia_diem}</div>
            <div class="day-details">
              🌡️ {day.nhiet_do}°C | 🌦️ {day.tinh_trang}
            </div>
          </div>
        </button>
      {/each}
    </div>
  {/if}
</div>

<CTA />
<Footer /> -->
