<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';

  const dispatch = createEventDispatcher();

  export let hotelId: number;

  let hotel = {
    ma_khach_san: 0,
    ten_khach_san: '',
    dia_chi: '',
    so_dien_thoai: '',
    danh_gia_trung_binh: 0,
    so_sao: 3,
    mo_ta: '',
    hinh_anh: '',
    dia_diem: '',
    gia: 0
  };

  let isLoading = false;
  let isSubmitting = false;
  let hasError = false;
  let errorMessage = '';
  let successMessage = '';

  let imageFile: File | null = null;
  let imagePreview = '';
  let uploadProgress = 0;
  let originalImageUrl = '';

  onMount(async () => {
    if (hotelId) {
      await fetchHotelDetails();
    }
  });

  async function fetchHotelDetails() {
    isLoading = true;
    hasError = false;
    errorMessage = '';

    try {
      const response = await fetch(`http://localhost:5000/api/khachsan/${hotelId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Không thể tải thông tin khách sạn');
      }

      const data = await response.json();
      hotel = data.khachsan;

      if (hotel.hinh_anh) {
        if (!hotel.hinh_anh.includes('/')) {
          hotel.hinh_anh = `images/${hotel.hinh_anh}`;
        }
        originalImageUrl = hotel.hinh_anh;
        imagePreview = formatImageUrl(hotel.hinh_anh);
      }
    } catch (error: any) {
      console.error('Error fetching hotel details:', error);
      hasError = true;
      errorMessage = 'Có lỗi khi tải thông tin khách sạn: ' + error.message;
    } finally {
      isLoading = false;
    }
  }

  function formatImageUrl(imageUrl: string): string {
    if (!imageUrl) return '';

    if (imageUrl.startsWith('data:image')) {
      return imageUrl;
    } else if (imageUrl.startsWith('http')) {
      return imageUrl;
    } else {
      if (imageUrl.includes('/')) {
        return `/${imageUrl}`; 
      } else {
        return `/images/${imageUrl}`; 
      }
    }
  }

  function handleImageChange(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) {
      if (originalImageUrl) {
        imagePreview = formatImageUrl(originalImageUrl);
        imageFile = null;
      } else {
        imagePreview = '';
        imageFile = null;
      }
      return;
    }

    const file = input.files[0];
    imageFile = file;

    if (file.size > 5 * 1024 * 1024) {
      alert('Kích thước file quá lớn. Vui lòng chọn file nhỏ hơn 5MB.');
      if (originalImageUrl) {
        imagePreview = formatImageUrl(originalImageUrl);
      } else {
        imagePreview = '';
      }
      imageFile = null;
      input.value = '';
      return;
    }

    if (!file.type.startsWith('image/')) {
      alert('Vui lòng chọn file hình ảnh.');
      if (originalImageUrl) {
        imagePreview = formatImageUrl(originalImageUrl);
      } else {
        imagePreview = '';
      }
      imageFile = null;
      input.value = '';
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      imagePreview = e.target?.result as string || '';

      const fileName = file.name;
      hotel.hinh_anh = fileName;
    };
    reader.readAsDataURL(file);
  }

  async function handleSubmit() {
    hasError = false;
    errorMessage = '';
    successMessage = '';

    if (!hotel.ten_khach_san || !hotel.dia_chi || !hotel.dia_diem || !hotel.gia) {
      hasError = true;
      errorMessage = 'Vui lòng nhập đầy đủ thông tin bắt buộc (tên khách sạn, địa chỉ, địa điểm, giá).';
      return;
    }

    try {
      isSubmitting = true;

      if (imageFile) {
        if (!hotel.hinh_anh) {
          hotel.hinh_anh = imageFile.name;
        }
        console.log('Image file selected:', imageFile.name);
      }

      const response = await fetch(`http://localhost:5000/api/khachsan/${hotelId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(hotel)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Không thể cập nhật khách sạn');
      }

      successMessage = 'Cập nhật khách sạn thành công!';

      if (imageFile) {
        successMessage += ' Lưu ý: Hình ảnh đã được lưu tên, nhưng cần được thêm vào thư mục static/images thủ công.';
      }

      dispatch('hotelUpdated');
    } catch (error: any) {
      console.error('Error updating hotel:', error);
      hasError = true;
      errorMessage = 'Có lỗi khi cập nhật khách sạn: ' + error.message;
    } finally {
      isSubmitting = false;
    }
  }
</script>

<div class="edit-hotel-form">
  {#if isLoading}
    <div class="loading-indicator">
      <i class="fas fa-spinner fa-spin"></i> Đang tải thông tin khách sạn...
    </div>
  {:else if hasError && !successMessage}
    <div class="error-message">
      <i class="fas fa-exclamation-circle"></i> {errorMessage}
      <button class="retry-btn" on:click={fetchHotelDetails}>
        <i class="fas fa-sync-alt"></i> Thử lại
      </button>
    </div>
  {:else}
    <h3>Chỉnh sửa Khách sạn</h3>

    {#if hasError}
      <div class="error-message">
        <i class="fas fa-exclamation-circle"></i> {errorMessage}
      </div>
    {/if}

    {#if successMessage}
      <div class="success-message">
        <i class="fas fa-check-circle"></i> {successMessage}
      </div>
    {/if}

    <form on:submit|preventDefault={handleSubmit}>
      <div class="form-grid">
        <div class="form-group">
          <label for="ten_khach_san">Tên khách sạn <span class="required">*</span></label>
          <input
            type="text"
            id="ten_khach_san"
            bind:value={hotel.ten_khach_san}
            placeholder="Nhập tên khách sạn"
            required
          />
        </div>

        <div class="form-group">
          <label for="dia_diem">Địa điểm <span class="required">*</span></label>
          <input
            type="text"
            id="dia_diem"
            bind:value={hotel.dia_diem}
            placeholder="Nhập địa điểm (thành phố, tỉnh)"
            required
          />
        </div>

        <div class="form-group full-width">
          <label for="dia_chi">Địa chỉ <span class="required">*</span></label>
          <input
            type="text"
            id="dia_chi"
            bind:value={hotel.dia_chi}
            placeholder="Nhập địa chỉ chi tiết"
            required
          />
        </div>

        <div class="form-group">
          <label for="so_dien_thoai">Số điện thoại</label>
          <input
            type="tel"
            id="so_dien_thoai"
            bind:value={hotel.so_dien_thoai}
            placeholder="Nhập số điện thoại"
          />
        </div>

        <div class="form-group">
          <label for="so_sao">Số sao</label>
          <div class="star-rating-input">
            <select id="so_sao" bind:value={hotel.so_sao}>
              <option value="1">1 sao ★</option>
              <option value="2">2 sao ★★</option>
              <option value="3">3 sao ★★★</option>
              <option value="4">4 sao ★★★★</option>
              <option value="5">5 sao ★★★★★</option>
            </select>
            <div class="star-display">
              {'★'.repeat(hotel.so_sao)}{'☆'.repeat(5 - hotel.so_sao)}
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="danh_gia_trung_binh">Đánh giá trung bình</label>
          <div class="rating-input">
            <input
              type="number"
              id="danh_gia_trung_binh"
              bind:value={hotel.danh_gia_trung_binh}
              placeholder="Nhập đánh giá trung bình"
              min="0"
              max="5"
              step="0.1"
            />
            <div class="rating-display">
              {hotel.danh_gia_trung_binh.toFixed(1)} / 5
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="gia">Giá <span class="required">*</span></label>
          <input
            type="number"
            id="gia"
            bind:value={hotel.gia}
            placeholder="Nhập giá khách sạn"
            min="0"
            step="10000"
            required
          />
        </div>

        <div class="form-group full-width">
          <label for="mo_ta">Mô tả</label>
          <textarea
            id="mo_ta"
            bind:value={hotel.mo_ta}
            placeholder="Nhập mô tả chi tiết về khách sạn"
            rows="5"
          ></textarea>
        </div>

        <div class="form-group full-width">
          <label for="hotel-image">Hình ảnh</label>
          <div class="image-upload-container">
            <div class="image-upload-area" class:has-preview={imagePreview}>
              <input
                type="file"
                id="hotel-image"
                accept="image/*"
                on:change={handleImageChange}
              />
              {#if !imagePreview}
                <div class="upload-placeholder">
                  <i class="fas fa-cloud-upload-alt"></i>
                  <p>Kéo thả hoặc nhấp để chọn hình ảnh</p>
                  <p class="upload-note">Hỗ trợ: JPG, PNG, GIF (Tối đa 5MB)</p>
                </div>
              {:else}
                <div class="image-preview-container">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    class="image-preview"
                    on:error={(e) => {
                      console.error('Lỗi tải hình ảnh:', originalImageUrl);
                      // Try alternative path if the first one fails
                      const img = e.target as HTMLImageElement;
                      if (img.src.includes('/images/') && originalImageUrl) {
                        // If we tried /images/ path, try with API path
                        img.src = `http://localhost:5000${originalImageUrl.startsWith('/') ? '' : '/'}${originalImageUrl}`;
                      }
                    }}
                  />
                  <button
                    type="button"
                    class="remove-image-btn"
                    aria-label="Xóa hình ảnh"
                    on:click={() => {
                      if (originalImageUrl) {
                        // Nếu có hình ảnh gốc, khôi phục lại
                        imagePreview = formatImageUrl(originalImageUrl);
                        imageFile = null;
                      } else {
                        // Nếu không có hình ảnh gốc, xóa hoàn toàn
                        imagePreview = '';
                        imageFile = null;
                      }
                      const fileInput = document.getElementById('hotel-image') as HTMLInputElement;
                      if (fileInput) fileInput.value = '';
                    }}
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              {/if}
            </div>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="submit" class="submit-btn" disabled={isSubmitting}>
          {#if isSubmitting}
            <i class="fas fa-spinner fa-spin"></i> Đang xử lý...
          {:else}
            <i class="fas fa-save"></i> Lưu thay đổi
          {/if}
        </button>
        <button type="button" class="cancel-btn" on:click={() => dispatch('cancel')}>
          <i class="fas fa-times"></i> Hủy bỏ
        </button>
      </div>
    </form>
  {/if}
</div>

<style>
  .edit-hotel-form {
    background-color: #fff;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  h3 {
    font-size: 1.5rem;
    color: #343a40;
    margin-top: 0;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
  }

  .loading-indicator {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-size: 1.1rem;
  }

  .loading-indicator i {
    margin-right: 10px;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .full-width {
    grid-column: 1 / -1;
  }

  .form-group {
    margin-bottom: 20px;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
  }

  .required {
    color: #dc3545;
  }

  input, select, textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }

  input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  textarea {
    resize: vertical;
    min-height: 100px;
  }

  .star-rating-input, .rating-input {
    display: flex;
    align-items: center;
  }

  .star-display {
    margin-left: 10px;
    color: #ffc107;
    letter-spacing: 2px;
  }

  .rating-display {
    margin-left: 10px;
    color: #007bff;
    font-weight: 500;
  }

  .image-upload-container {
    margin-top: 5px;
  }

  .image-upload-area {
    position: relative;
    border: 2px dashed #ced4da;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .image-upload-area:hover {
    border-color: #80bdff;
    background-color: #f8f9fa;
  }

  .image-upload-area.has-preview {
    border-style: solid;
    padding: 0;
  }

  .image-upload-area input[type="file"] {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 10;
  }

  .upload-placeholder {
    pointer-events: none;
  }

  .upload-placeholder i {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 15px;
  }

  .upload-placeholder p {
    margin: 5px 0;
    color: #495057;
  }

  .upload-note {
    font-size: 0.8rem;
    color: #6c757d;
  }

  .image-preview-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .image-preview {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 6px;
  }

  .remove-image-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #dc3545;
    transition: all 0.2s ease;
  }

  .remove-image-btn:hover {
    background-color: #fff;
    color: #bd2130;
    transform: scale(1.1);
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
  }

  .submit-btn, .cancel-btn {
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .submit-btn {
    background-color: #007bff;
    color: white;
    border: none;
  }

  .submit-btn:hover:not(:disabled) {
    background-color: #0069d9;
  }

  .submit-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
  }

  .cancel-btn {
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
  }

  .cancel-btn:hover {
    background-color: #e9ecef;
  }

  .error-message, .success-message {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .success-message {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .retry-btn {
    margin-left: auto;
    background-color: transparent;
    border: 1px solid #721c24;
    color: #721c24;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
  }

  .retry-btn:hover {
    background-color: #f1b0b7;
  }
</style>
