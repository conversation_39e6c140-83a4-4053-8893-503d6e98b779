<script>
  import { createEventDispatcher, onMount } from 'svelte';

  const dispatch = createEventDispatcher();

  export let tourId; 
  let tour = {
    ma_tour: '',
    ten_tour: '',
    mo_ta: '',
    gia: '',
    ngay_bat_dau: '',
    ngay_ket_thuc: '',
    so_cho_trong: '',
    dia_diem: '',
    hinh_anh: '',
    loai_tour: 'trong_nuoc'
  };
  let isLoading = false;
  let hasError = false;
  let errorMessage = '';
  let imageFile = null;
  let imagePreview = '';

  onMount(async () => {
    console.log('EditTourForm mounted with tourId:', tourId);
    if (tourId) {
      await fetchTourDetails();
    } else {
      console.error('No tourId provided on mount');
    }
  });

  async function fetchTourDetails() {
    isLoading = true;
    hasError = false;
    errorMessage = '';

    try {
      const response = await fetch(`http://localhost:5000/api/tours/${tourId}`);

      if (!response.ok) {
        console.error(`Lỗi tải thông tin: ${response.status} ${response.statusText}`);
        try {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Không thể tải thông tin tour');
        } catch (jsonError) {
          const errorText = await response.text();
          console.error('Chi tiết lỗi:', errorText);
          throw new Error(`Không thể tải thông tin tour: ${response.status} ${response.statusText}`);
        }
      }

      const tourData = await response.json();
      tour = {
        ma_tour: tourData.ma_tour,
        ten_tour: tourData.ten_tour,
        mo_ta: tourData.mo_ta || '',
        gia: tourData.gia,
        ngay_bat_dau: formatDateForInput(tourData.ngay_bat_dau),
        ngay_ket_thuc: formatDateForInput(tourData.ngay_ket_thuc),
        so_cho_trong: tourData.so_cho_trong,
        dia_diem: tourData.dia_diem,
        hinh_anh: tourData.hinh_anh || '',
        loai_tour: tourData.loai_tour || 'trong_nuoc'
      };

      if (tour.hinh_anh) {
        console.log('Đường dẫn hình ảnh từ API:', tour.hinh_anh);

        if (tour.hinh_anh.startsWith('data:image')) {
          imagePreview = tour.hinh_anh;
          console.log('Sử dụng hình ảnh Base64');
        } else if (tour.hinh_anh.startsWith('http')) {
          imagePreview = tour.hinh_anh;
          console.log('Sử dụng URL đầy đủ');
        } else if (tour.hinh_anh.startsWith('/')) {
          imagePreview = `http://localhost:5000${tour.hinh_anh}`;
          console.log('Sử dụng đường dẫn tuyệt đối từ backend');
        } else {
          try {
            imagePreview = `/images/${tour.hinh_anh.split(/[\/\\]/).pop()}`;
            console.log('Thử tìm trong thư mục images:', imagePreview);
          } catch (error) {
            imagePreview = `http://localhost:5000/${tour.hinh_anh}`;
            console.log('Sử dụng đường dẫn tương đối từ backend');
          }
        }

        console.log('Đường dẫn hình ảnh đã xử lý:', imagePreview);
      }

      isLoading = false;
    } catch (error) {
      console.error('Error fetching tour details:', error);

      isLoading = false;
      hasError = true;
      errorMessage = 'Có lỗi khi lấy thông tin tour: ' + error.message;

      alert(errorMessage);
    }
  }

  function formatDateForInput(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  function handleImageChange(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.type.match('image.*')) {
      alert('Vui lòng chọn file hình ảnh');
      return;
    }

    if (file.size > 2 * 1024 * 1024) {
      alert('Kích thước ảnh quá lớn. Vui lòng chọn ảnh nhỏ hơn 2MB.');
      return;
    }

    imageFile = file;

    const reader = new FileReader();
    reader.onload = e => {
      if (typeof e.target.result === 'string') {
        imagePreview = e.target.result;
        tour.hinh_anh = e.target.result;
        console.log('Đã cập nhật hình ảnh thành Base64 data');
      }
    };
    reader.readAsDataURL(file);
  }

  async function handleSubmit() {
    isLoading = true;
    hasError = false;
    errorMessage = '';

    try {
      if (!tour.ten_tour) throw new Error('Tên tour không được để trống');
      if (!tour.gia) throw new Error('Giá tour không được để trống');
      if (!tour.ngay_bat_dau) throw new Error('Ngày bắt đầu không được để trống');
      if (!tour.ngay_ket_thuc) throw new Error('Ngày kết thúc không được để trống');
      if (!tour.so_cho_trong) throw new Error('Số chỗ trống không được để trống');
      if (!tour.dia_diem) throw new Error('Địa điểm không được để trống');

      const startDate = new Date(tour.ngay_bat_dau);
      const endDate = new Date(tour.ngay_ket_thuc);
      if (startDate > endDate) {
        throw new Error('Ngày bắt đầu phải trước ngày kết thúc');
      }

      let tourDataToSend = { ...tour };

      if (tourDataToSend.hinh_anh && tourDataToSend.hinh_anh.startsWith('data:image') && tourDataToSend.hinh_anh.length > 100000) {
        console.log('Ảnh quá lớn để gửi dưới dạng Base64, chuyển sang lưu tên file');

        if (imageFile) {
          tourDataToSend.hinh_anh = imageFile.name;
        }
      }

      console.log('Sending tour data with image information');

      const response = await fetch(`http://localhost:5000/api/tours/${tourId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(tourDataToSend)
      });

      if (!response.ok) {
        console.error(`Lỗi cập nhật: ${response.status} ${response.statusText}`);
        try {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Không thể cập nhật tour');
        } catch (jsonError) {
          const errorText = await response.text();
          console.error('Chi tiết lỗi:', errorText);
          throw new Error(`Không thể cập nhật tour: ${response.status} ${response.statusText}`);
        }
      }

      console.log('Cập nhật tour thành công!');

      dispatch('tourUpdated');

      alert('Cập nhật tour thành công!');
    } catch (error) {
      console.error('Error updating tour:', error);

      hasError = true;
      errorMessage = error.message;

      alert(errorMessage);
    } finally {
      isLoading = false;
    }
  }

  function handleCancel() {
    dispatch('cancel');
  }
</script>

<div class="edit-tour-form">
  <h2>Sửa Thông Tin Tour</h2>

  {#if isLoading}
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Đang tải thông tin tour...</p>
    </div>
  {:else if hasError}
    <div class="error-container">
      <p class="error-message">{errorMessage}</p>
      <button class="btn-retry" on:click={fetchTourDetails}>Thử lại</button>
    </div>
  {:else}
    <form on:submit|preventDefault={handleSubmit}>
      <div class="form-grid">
        <div class="form-group">
          <label for="tenTour">Tên tour <span class="required">*</span></label>
          <input
            id="tenTour"
            type="text"
            bind:value={tour.ten_tour}
            placeholder="Nhập tên tour"
            required
          >
        </div>

        <div class="form-group">
          <label for="diaDiem">Địa điểm <span class="required">*</span></label>
          <input
            id="diaDiem"
            type="text"
            bind:value={tour.dia_diem}
            placeholder="Nhập địa điểm"
            required
          >
        </div>

        <div class="form-group">
          <label for="gia">Giá (VNĐ) <span class="required">*</span></label>
          <input
            id="gia"
            type="number"
            bind:value={tour.gia}
            placeholder="Nhập giá tour"
            min="0"
            required
          >
        </div>

        <div class="form-group">
          <label for="soChoTrong">Số chỗ trống <span class="required">*</span></label>
          <input
            id="soChoTrong"
            type="number"
            bind:value={tour.so_cho_trong}
            placeholder="Nhập số chỗ trống"
            min="1"
            required
          >
        </div>

        <div class="form-group">
          <label for="ngayBatDau">Ngày bắt đầu <span class="required">*</span></label>
          <input
            id="ngayBatDau"
            type="date"
            bind:value={tour.ngay_bat_dau}
            required
          >
        </div>

        <div class="form-group">
          <label for="ngayKetThuc">Ngày kết thúc <span class="required">*</span></label>
          <input
            id="ngayKetThuc"
            type="date"
            bind:value={tour.ngay_ket_thuc}
            required
          >
        </div>

        <div class="form-group">
          <label for="loaiTour">Loại tour <span class="required">*</span></label>
          <select
            id="loaiTour"
            bind:value={tour.loai_tour}
            required
          >
            <option value="trong_nuoc">Tour trong nước</option>
            <option value="nuoc_ngoai">Tour nước ngoài</option>
          </select>
        </div>

        <div class="form-group image-upload">
          <label for="hinhAnh">Hình ảnh</label>
          <input
            id="hinhAnh"
            type="file"
            accept="image/*"
            on:change={handleImageChange}
          >
          {#if imagePreview}
            <div class="image-preview">
              <img
                src={imagePreview}
                alt="Preview"
                on:error={() => {
                  console.error(`Không thể tải hình ảnh: ${imagePreview}`);
                  alert('Không thể hiển thị hình ảnh. Vui lòng chọn hình ảnh mới.');
                }}
              />
            </div>
          {/if}
        </div>
      </div>

      <div class="form-group full-width">
        <label for="moTa">Mô tả</label>
        <textarea
          id="moTa"
          bind:value={tour.mo_ta}
          placeholder="Nhập mô tả chi tiết về tour"
          rows="5"
        ></textarea>
      </div>

      <div class="form-actions">
        <button type="button" class="btn-cancel" on:click={handleCancel}>Hủy</button>
        <button type="submit" class="btn-save" disabled={isLoading}>Lưu Thay Đổi</button>
      </div>
    </form>
  {/if}
</div>

<style>
  .edit-tour-form {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
  }

  h2 {
    margin-bottom: 20px;
    color: #333;
    font-size: 24px;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .full-width {
    grid-column: 1 / -1;
  }

  label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
  }

  .required {
    color: #f44336;
  }

  input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }

  textarea {
    resize: vertical;
  }

  input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }

  .btn-cancel, .btn-save, .btn-retry {
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
  }

  .btn-cancel {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    color: #333;
  }

  .btn-save {
    background-color: #4CAF50;
    border: none;
    color: white;
  }

  .btn-retry {
    background-color: #2196F3;
    border: none;
    color: white;
  }

  .btn-cancel:hover {
    background-color: #e0e0e0;
  }

  .btn-save:hover {
    background-color: #45a049;
  }

  .btn-retry:hover {
    background-color: #0b7dda;
  }

  .btn-save:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
  }

  .loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-container {
    text-align: center;
    padding: 30px;
  }

  .error-message {
    background-color: #ffebee;
    color: #f44336;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .image-upload {
    grid-column: 1 / -1;
  }

  .image-preview {
    margin-top: 10px;
    max-width: 300px;
  }

  .image-preview img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    border: 1px solid #ddd;
  }
</style>
