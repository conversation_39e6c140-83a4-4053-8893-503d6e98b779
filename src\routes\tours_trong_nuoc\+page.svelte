<script lang="ts">
  import { onMount } from 'svelte';
  import Navbar from '../../components/Navbar.svelte';
  import Footer from '../../components/Footer.svelte';
  import CTA from '../../components/CTA.svelte';

  interface Tour {
    ma_tour: number;
    ten_tour: string;
    mo_ta: string;
    gia: number;
    dia_diem: string;
    ngay_bat_dau: string;
    ngay_ket_thuc: string;
    so_cho_trong: number;
    hinh_anh: string;
    loai_tour: string;
  }

  let tours: Tour[] = [];
  let isLoading = true;
  let error: string | null = null;
  let searchQuery = '';
  let filteredTours: Tour[] = [];

  // Filter tours based on search query
  $: {
    if (searchQuery.trim() === '') {
      filteredTours = tours;
    } else {
      filteredTours = tours.filter(tour =>
        tour.ten_tour.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tour.dia_diem.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
  }

  function formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  }

  function formatImageUrl(imageName: string): string {
    if (!imageName) return '/images/default-tour.svg';
    return `/images/${imageName}`;
  }

  function calculateDays(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }

  function viewTourDetails(tourId: number) {
    window.location.href = `/tour/${tourId}`;
  }

  async function loadDomesticTours() {
    isLoading = true;
    error = null;

    try {
      const response = await fetch('http://localhost:5000/api/tours?loai_tour=trong_nuoc');

      if (!response.ok) {
        throw new Error(`Lỗi ${response.status}: Không thể tải danh sách tour trong nước.`);
      }

      const data = await response.json();
      tours = data.data || data || [];

      // Filter to ensure only domestic tours
      tours = tours.filter(tour => tour.loai_tour === 'trong_nuoc');

    } catch (err: any) {
      console.error('Lỗi khi tải tour trong nước:', err);
      error = err.message;
    } finally {
      isLoading = false;
    }
  }

  onMount(() => {
    loadDomesticTours();
  });
</script>

<svelte:head>
  <title>Tour Trong Nước - Travela</title>
  <meta name="description" content="Khám phá các tour du lịch trong nước hấp dẫn tại Việt Nam" />
</svelte:head>

<Navbar />

<div class="tours-page">
  <div class="page-header">
    <div class="header-content">
      <h1>Tour Du Lịch Trong Nước</h1>
      <p>Khám phá vẻ đẹp Việt Nam qua những hành trình đáng nhớ</p>
    </div>
  </div>

  <div class="container">
    <div class="search-section">
      <div class="search-box">
        <input
          type="text"
          placeholder="Tìm kiếm tour theo tên hoặc địa điểm..."
          bind:value={searchQuery}
        />
        <i class="fas fa-search"></i>
      </div>
    </div>

    {#if isLoading}
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <p>Đang tải danh sách tour trong nước...</p>
      </div>
    {:else if error}
      <div class="error-container">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Đã xảy ra lỗi</h3>
        <p>{error}</p>
        <button on:click={loadDomesticTours} class="retry-button">
          <i class="fas fa-sync-alt"></i> Thử lại
        </button>
      </div>
    {:else if filteredTours.length === 0}
      <div class="empty-container">
        <i class="fas fa-search"></i>
        <h3>Không tìm thấy tour nào</h3>
        <p>
          {searchQuery ?
            `Không có tour nào phù hợp với từ khóa "${searchQuery}"` :
            'Hiện tại chưa có tour trong nước nào.'
          }
        </p>
      </div>
    {:else}
      <div class="tours-grid">
        {#each filteredTours as tour (tour.ma_tour)}
          <div class="tour-card">
            <div class="tour-image">
              <img src={formatImageUrl(tour.hinh_anh)} alt={tour.ten_tour} />
              <div class="tour-type">Trong nước</div>
              {#if tour.so_cho_trong <= 5 && tour.so_cho_trong > 0}
                <div class="limited-seats">Chỉ còn {tour.so_cho_trong} chỗ</div>
              {/if}
            </div>

            <div class="tour-content">
              <h3>{tour.ten_tour}</h3>
              <div class="tour-info">
                <div class="info-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>{tour.dia_diem}</span>
                </div>
                <div class="info-item">
                  <i class="fas fa-calendar-alt"></i>
                  <span>{calculateDays(tour.ngay_bat_dau, tour.ngay_ket_thuc)} ngày</span>
                </div>
                <div class="info-item">
                  <i class="fas fa-users"></i>
                  <span>{tour.so_cho_trong} chỗ trống</span>
                </div>
              </div>

              <p class="tour-description">{tour.mo_ta}</p>

              <div class="tour-footer">
                <div class="tour-price">
                  <span class="price">{formatPrice(tour.gia)}</span>
                  <span class="price-per">/người</span>
                </div>

                <button
                  class="view-details-button"
                  on:click={() => viewTourDetails(tour.ma_tour)}
                  disabled={tour.so_cho_trong <= 0}
                >
                  {tour.so_cho_trong > 0 ? 'Xem chi tiết' : 'Hết chỗ'}
                </button>
              </div>
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</div>

<CTA />
<Footer />

<style>
  .tours-page {
    min-height: 100vh;
    background: #f8f9fa;
  }

  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0 60px;
    text-align: center;
  }

  .header-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .header-content p {
    font-size: 1.2rem;
    opacity: 0.9;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  .search-section {
    margin-bottom: 40px;
  }

  .search-box {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
  }

  .search-box input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 50px;
    font-size: 1rem;
    transition: border-color 0.3s;
  }

  .search-box input:focus {
    outline: none;
    border-color: #667eea;
  }

  .search-box i {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
  }

  .loading-container,
  .error-container,
  .empty-container {
    text-align: center;
    padding: 60px 20px;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .tours-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
  }

  .tour-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
  }

  .tour-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  }

  .tour-image {
    position: relative;
    height: 200px;
    overflow: hidden;
  }

  .tour-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .tour-type {
    position: absolute;
    top: 15px;
    left: 15px;
    background: #28a745;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
  }

  .limited-seats {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #dc3545;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
  }

  .tour-content {
    padding: 20px;
  }

  .tour-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }

  .tour-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #666;
  }

  .info-item i {
    color: #667eea;
    width: 16px;
  }

  .tour-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .tour-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .tour-price {
    display: flex;
    flex-direction: column;
  }

  .price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
  }

  .price-per {
    font-size: 0.8rem;
    color: #666;
  }

  .view-details-button {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s;
  }

  .view-details-button:hover:not(:disabled) {
    background: #5a6fd8;
  }

  .view-details-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
  }

  .retry-button {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 20px;
  }

  @media (max-width: 768px) {
    .header-content h1 {
      font-size: 2rem;
    }

    .tours-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
