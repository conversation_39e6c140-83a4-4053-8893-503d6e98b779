<script lang="ts">
    import { onMount } from 'svelte';

    interface UserInfo {
      ho_ten?: string;
      email?: string;
      so_dien_thoai?: string;
    }

    interface Suggestion {
      ma_goi_y: number;
      ma_nguoi_dung: number;
      ten_nguoi_dung?: string; 
      email?: string; 
      user_info?: UserInfo; 
      diem_den?: string;
      gia_tu?: number;
      gia_den?: number;
      ngay_bat_dau?: string;
      ngay_ket_thuc?: string;
      so_nguoi?: number;
      ghi_chu?: string;
      trang_thai: 'cho_xu_ly' | 'da_goi_y' | 'huy_bo' | string; 
      ngay_tao?: string; 
      isUpdating?: boolean; 
    }

    let isLoading = true;
    let error: string | null = null;
    let suggestions: Suggestion[] = [];
    let statusFilter = 'all';
    let searchQuery = '';
    let currentPage = 1;
    let itemsPerPage = 10;

    const API_BASE_URL = 'http://localhost:5000/api/tour-suggestions';
    const STATUS_PENDING = 'dang_xu_ly'; 
    const STATUS_CONFIRMED = 'da_goi_y';  
    const STATUS_CANCELLED = 'da_huy';    

    $: filteredSuggestions = suggestions.filter(suggestion => {
      const searchLower = searchQuery.toLowerCase().trim();
      const matchesSearch =
        !searchLower || 
        (suggestion.diem_den && suggestion.diem_den.toLowerCase().includes(searchLower)) ||
        (suggestion.ghi_chu && suggestion.ghi_chu.toLowerCase().includes(searchLower)) ||
        (suggestion.user_info?.ho_ten && suggestion.user_info.ho_ten.toLowerCase().includes(searchLower)) ||
        (suggestion.user_info?.email && suggestion.user_info.email.toLowerCase().includes(searchLower)) ||
        suggestion.ma_goi_y.toString().includes(searchLower) ||
        suggestion.ma_nguoi_dung.toString().includes(searchLower);


      const matchesStatus =
        statusFilter === 'all' ||
        suggestion.trang_thai === statusFilter;

      return matchesSearch && matchesStatus;
    });

    $: startIndex = (currentPage - 1) * itemsPerPage;
    $: endIndex = startIndex + itemsPerPage;
    $: displayedSuggestions = filteredSuggestions.slice(startIndex, endIndex);
    $: totalPages = Math.ceil(filteredSuggestions.length / itemsPerPage);

    function formatDate(dateString: string | undefined | null): string {
      if (!dateString) return 'N/A';
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return 'Ngày không hợp lệ';
        }
        return date.toLocaleDateString('vi-VN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return 'Lỗi định dạng ngày';
      }
    }

    function formatPrice(price: number | undefined | null): string {
      if (price === undefined || price === null) return 'N/A';
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(price);
    }

    function getStatusInfo(status: string): { text: string; class: string } {
      switch (status) {
        case 'cho_xu_ly': 
          return { text: 'Chờ xử lý', class: 'waiting' };
        case STATUS_PENDING: 
          return { text: 'Đang xử lý', class: 'processing' };
        case STATUS_CONFIRMED: 
          return { text: 'Đã gợi ý', class: 'completed' };
        case STATUS_CANCELLED: // 'da_huy'
          return { text: 'Đã hủy', class: 'cancelled' };
        case 'huy_bo': 
          return { text: 'Đã hủy', class: 'cancelled' };
        default:
          return { text: status || 'Không xác định', class: 'unknown' };
      }
    }
    async function loadSuggestions(): Promise<void> {
      isLoading = true;
      error = null;
      try {
        const response = await fetch(API_BASE_URL);

        if (!response.ok) {
           let errorDetail = response.statusText;
           try {
               const errorData = await response.json();
               errorDetail = errorData.message || errorData.error || JSON.stringify(errorData);
           } catch (jsonError) {}
          throw new Error(`Không thể tải danh sách gợi ý tour. Lý do: ${errorDetail} (HTTP ${response.status})`);
        }

        const data = await response.json();
        console.log('API response:', data); 

        if (Array.isArray(data)) {
          suggestions = data as Suggestion[];
        } else if (data && Array.isArray(data.suggestions)) {
          suggestions = data.suggestions as Suggestion[];
        } else if (data && typeof data === 'object') {
          const possibleSuggestions = Object.values(data).find(val => Array.isArray(val));
          if (possibleSuggestions && Array.isArray(possibleSuggestions)) {
            suggestions = possibleSuggestions as Suggestion[];
            console.log('Extracted suggestions from object:', suggestions);
          } else {
            console.error('Unexpected API response format:', data);
            throw new Error('Định dạng dữ liệu trả về từ API không đúng.');
          }
        } else {
          console.error('Unexpected API response format:', data);
          throw new Error('Định dạng dữ liệu trả về từ API không đúng.');
        }
        currentPage = 1;
      } catch (err: any) {
        console.error('Lỗi khi tải gợi ý tour:', err);
        error = err.message || 'Có lỗi xảy ra khi tải gợi ý tour';
        suggestions = []; 
      } finally {
        isLoading = false;
      }
    }

    async function updateSuggestionStatus(suggestionId: number, newStatus: string): Promise<void> {
      const suggestionIndex = suggestions.findIndex(s => s.ma_goi_y === suggestionId);
      if (suggestionIndex === -1) {
        console.error(`Suggestion with ID ${suggestionId} not found locally.`);
        alert('Không tìm thấy gợi ý để cập nhật.');
        return;
      }
      suggestions = suggestions.map(s =>
        s.ma_goi_y === suggestionId ? { ...s, isUpdating: true, trang_thai: newStatus } : s
      );

      try {
        const response = await fetch(`${API_BASE_URL}/${suggestionId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ trang_thai: newStatus }) 
        });

        if (!response.ok) {
          let errorDetail = response.statusText; 
          try {
            
            const errorData = await response.json();
            errorDetail = errorData.message || errorData.error || JSON.stringify(errorData); 
            console.error("Backend error details:", errorData); 
          } catch (jsonError) {
            console.warn("Could not parse error response as JSON.");
          }
          throw new Error(`Lý do: ${errorDetail} (HTTP ${response.status})`);
        }

        suggestions = suggestions.map(suggestion =>
          suggestion.ma_goi_y === suggestionId
            ? { ...suggestion, trang_thai: newStatus, isUpdating: false }
            : suggestion
        );

        const statusText = newStatus === STATUS_CONFIRMED ? 'đã xác nhận' :
                          newStatus === STATUS_PENDING ? 'chờ xử lý' :
                          newStatus === STATUS_CANCELLED ? 'đã hủy' : newStatus;

        const toast = document.createElement('div');
        toast.className = 'toast-notification success';
        toast.innerHTML = `<i class="fas fa-check-circle"></i> Cập nhật trạng thái thành ${statusText}`;
        document.body.appendChild(toast);

        setTimeout(() => {
          toast.classList.add('hide');
          setTimeout(() => toast.remove(), 300);
        }, 3000);

      } catch (err: any) {
        console.error('Lỗi khi cập nhật trạng thái:', err);
        alert(`Không thể cập nhật trạng thái gợi ý ID ${suggestionId}. ${err.message || 'Lỗi không xác định.'}`);

        const originalSuggestion = suggestions[suggestionIndex];
        if (originalSuggestion) {
          suggestions = suggestions.map(s =>
            s.ma_goi_y === suggestionId ? { ...originalSuggestion, isUpdating: false } : s
          );
        }
      } finally {
        suggestions = suggestions.map(s =>
          s.ma_goi_y === suggestionId ? { ...s, isUpdating: false } : s
        );
      }
    }

    async function deleteSuggestion(suggestionId: number): Promise<void> {
      if (confirm(`Bạn có chắc muốn xóa gợi ý tour có ID: ${suggestionId}? Thao tác này không thể hoàn tác.`)) {
        const suggestionToRemove = suggestions.find(s => s.ma_goi_y === suggestionId);
        if (!suggestionToRemove) return;

        const originalSuggestions = [...suggestions]; // Backup for rollback

        suggestions = suggestions.filter(suggestion => suggestion.ma_goi_y !== suggestionId);
        const originalCurrentPage = currentPage; // Backup current page

        const newTotalPages = Math.ceil(filteredSuggestions.length / itemsPerPage); // Recalculate based on filtered list *after* removal
        if (currentPage > newTotalPages && newTotalPages > 0) {
            currentPage = newTotalPages; // Go to the new last page
        } else if (currentPage > 1 && displayedSuggestions.length === 0) {
            currentPage--;
        }


        try {
          const response = await fetch(`${API_BASE_URL}/${suggestionId}`, {
            method: 'DELETE'
          });

          if (!response.ok) {
             let errorDetail = response.statusText;
             try {
               const errorData = await response.json();
               errorDetail = errorData.message || errorData.error || JSON.stringify(errorData);
               console.error("Backend error details:", errorData);
             } catch (jsonError) {
                console.warn("Could not parse error response as JSON.");
             }
             suggestions = originalSuggestions;
             currentPage = originalCurrentPage; // Restore original page
             throw new Error(`Lý do: ${errorDetail} (HTTP ${response.status})`);
          }

          const toast = document.createElement('div');
          toast.className = 'toast-notification success';
          toast.innerHTML = `<i class="fas fa-check-circle"></i> Xóa gợi ý tour thành công!`;
          document.body.appendChild(toast);

          setTimeout(() => {
            toast.classList.add('hide');
            setTimeout(() => toast.remove(), 300);
          }, 3000);

        } catch (err: any) {
          console.error('Lỗi khi xóa gợi ý tour:', err);
          suggestions = originalSuggestions;
          currentPage = originalCurrentPage;
          alert(`Không thể xóa gợi ý ID ${suggestionId}. ${err.message || 'Lỗi không xác định.'}`);
        }
      }
    }

    onMount(() => {
      loadSuggestions();
    });

    function handleStatusFilterChange() {
      currentPage = 1; 
    }

    function handleSearchInput() {
      currentPage = 1; 
    }

  </script>

  <div class="tour-suggestion-management">
    <div class="actions">
      <div class="filters">
        <div class="status-filter">
          <label for="status-select">Trạng thái:</label>
          <select id="status-select" bind:value={statusFilter} on:change={handleStatusFilterChange}>
            <option value="all">Tất cả</option>
            <option value="cho_xu_ly">Chờ xử lý</option>
            <option value={STATUS_PENDING}>Đang xử lý</option>
            <option value={STATUS_CONFIRMED}>Đã gợi ý</option>
            <option value={STATUS_CANCELLED}>Đã hủy</option>
          </select>
        </div>
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input
            type="search"
            placeholder="Tìm kiếm ID, người dùng, điểm đến, ghi chú..."
            bind:value={searchQuery}
            on:input={handleSearchInput}
            aria-label="Tìm kiếm gợi ý tour"
          >
        </div>
      </div>

    </div>

    {#if isLoading && suggestions.length === 0} <!-- Show initial loading -->
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <p>Đang tải dữ liệu...</p>
      </div>
    {:else if error}
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <p><strong>Đã xảy ra lỗi:</strong> {error}</p>
        <button on:click={loadSuggestions} class="retry-button" disabled={isLoading}>
          <i class="fas fa-sync-alt {isLoading ? 'fa-spin' : ''}"></i> Thử lại
        </button>
      </div>
    {:else if suggestions.length === 0}
       <div class="no-data">
        <i class="fas fa-info-circle"></i>
        <p>Không có dữ liệu gợi ý tour nào.</p>
      </div>
    {:else if filteredSuggestions.length === 0}
      <div class="no-data">
        <i class="fas fa-search"></i>
        <p>Không tìm thấy gợi ý nào phù hợp với tiêu chí lọc.</p>
      </div>
    {:else}
      <div class="table-container">
        <table class="suggestion-table">
          <thead>
            <tr>
              <th>ID</th>
              <th>ID Người dùng</th>
              <th>Tên</th>
              <th>Email</th>
              <th>Điểm đến</th>
              <th style="min-width: 180px;">Khoảng giá</th>
              <th style="min-width: 180px;">Thời gian</th>
              <th>Số người</th>
              <th>Ghi chú</th>
              <th>Trạng thái</th>
              <th style="text-align: center; min-width: 140px;">Thao tác</th>
            </tr>
          </thead>
          <tbody>
            {#each displayedSuggestions as suggestion (suggestion.ma_goi_y)}
              <tr class:updating={suggestion.isUpdating}> <!-- Optional: Add class for visual feedback -->
                <td>{suggestion.ma_goi_y}</td>
                <td>
                  <span class="user-id">{suggestion.ma_nguoi_dung}</span>
                </td>
                <td>
                  {#if suggestion.ten_nguoi_dung || (suggestion.user_info && suggestion.user_info.ho_ten)}
                    <span class="user-name" title={suggestion.ten_nguoi_dung || suggestion.user_info?.ho_ten}>
                      {suggestion.ten_nguoi_dung || suggestion.user_info?.ho_ten || 'N/A'}
                    </span>
                  {:else}
                    <span class="user-name">N/A</span>
                  {/if}
                </td>
                <td>
                  {#if suggestion.email || (suggestion.user_info && suggestion.user_info.email)}
                    <span class="user-email" title={suggestion.email || suggestion.user_info?.email}>
                      {suggestion.email || suggestion.user_info?.email || 'N/A'}
                    </span>
                  {:else}
                    <span class="user-email">N/A</span>
                  {/if}
                </td>
                <td title={suggestion.diem_den}>{suggestion.diem_den || 'N/A'}</td>
                <td class="price-range">
                  {#if suggestion.gia_tu !== undefined && suggestion.gia_den !== undefined}
                    <div class="price-container">
                      <span class="price-from">{formatPrice(suggestion.gia_tu)}</span>
                      <span class="price-separator">-</span>
                      <span class="price-to">{formatPrice(suggestion.gia_den)}</span>
                    </div>
                  {:else}
                    N/A
                  {/if}
                </td>
                <td class="date-range">
                  <div class="date-container">
                    <span class="date-from">{formatDate(suggestion.ngay_bat_dau)}</span>
                    <span class="date-separator">-</span>
                    <span class="date-to">{formatDate(suggestion.ngay_ket_thuc)}</span>
                  </div>
                </td>
                <td>{suggestion.so_nguoi ?? 'N/A'}</td>
                <td>
                  <div class="note-content" title={suggestion.ghi_chu}>
                    {suggestion.ghi_chu || 'Không có'}
                  </div>
                </td>
                <td>
                  {#if suggestion.trang_thai}
                    {@const statusInfo = getStatusInfo(suggestion.trang_thai)}
                    <span class="status-badge {statusInfo.class}">
                      {statusInfo.text}
                    </span>
                  {:else}
                    <span class="status-badge unknown">Không xác định</span>
                  {/if}
                </td>
                <td class="actions-cell">
                   <!-- Nút Xác nhận: Hiển thị khi trạng thái là 'Chờ xử lý' hoặc 'Đang xử lý' -->
                   {#if suggestion.trang_thai === 'cho_xu_ly' || suggestion.trang_thai === STATUS_PENDING}
                     <button
                       class="action-btn confirm"
                       aria-label="Xác nhận gợi ý"
                       title="Xác nhận gợi ý tour"
                       on:click={() => updateSuggestionStatus(suggestion.ma_goi_y, STATUS_CONFIRMED)}
                       disabled={suggestion.isUpdating}
                     >
                       <i class="fas fa-check" aria-hidden="true"></i>
                       <span class="tooltip">Xác nhận</span>
                     </button>
                   {/if}

                   <!-- Nút Hủy xác nhận: Hiển thị khi trạng thái là 'Đã gợi ý' -->
                   {#if suggestion.trang_thai === STATUS_CONFIRMED}
                     <button
                       class="action-btn unconfirm"
                       aria-label="Hủy xác nhận gợi ý"
                       title="Hủy xác nhận (về Đang xử lý)"
                       on:click={() => updateSuggestionStatus(suggestion.ma_goi_y, STATUS_PENDING)}
                       disabled={suggestion.isUpdating}
                     >
                       <i class="fas fa-undo" aria-hidden="true"></i>
                       <span class="tooltip">Hủy xác nhận</span>
                     </button>
                   {/if}

                    <!-- Nút Xóa: Luôn hiển thị -->
                   <button
                     class="action-btn delete"
                     aria-label="Xóa gợi ý"
                     title="Xóa gợi ý tour"
                     on:click={() => deleteSuggestion(suggestion.ma_goi_y)}
                     disabled={suggestion.isUpdating}
                   >
                     <i class="fas fa-trash" aria-hidden="true"></i>
                     <span class="tooltip">Xóa</span>
                   </button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      {#if totalPages > 1}
        <div class="pagination">
          <button
            class="page-btn first"
            disabled={currentPage === 1 || isLoading}
            on:click={() => currentPage = 1}
            aria-label="Trang đầu"
            title="Trang đầu"
          >
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button
            class="page-btn prev"
            disabled={currentPage === 1 || isLoading}
            on:click={() => currentPage--}
            aria-label="Trang trước"
            title="Trang trước"
          >
            <i class="fas fa-angle-left"></i>
          </button>

          <span class="page-info">Trang {currentPage} / {totalPages}</span>

          <button
            class="page-btn next"
            disabled={currentPage === totalPages || isLoading}
            on:click={() => currentPage++}
            aria-label="Trang sau"
            title="Trang sau"
          >
            <i class="fas fa-angle-right"></i>
          </button>
          <button
            class="page-btn last"
            disabled={currentPage === totalPages || isLoading}
            on:click={() => currentPage = totalPages}
            aria-label="Trang cuối"
            title="Trang cuối"
          >
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      {/if}
    {/if}
  </div>

  <style>
      /* --- General Styles --- */
      .tour-suggestion-management {
        padding: 24px 28px;
        background-color: #f8f9fa;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        color: #343a40;
        transition: all 0.3s ease;
      }
      .actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        flex-wrap: wrap;
        gap: 18px;
        background-color: white;
        padding: 16px 20px;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.03);
      }

      .filters {
        display: flex;
        gap: 22px;
        flex-wrap: wrap;
        flex-grow: 1;
        align-items: center;
      }

      .status-filter, .search-box {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .status-filter label {
        font-weight: 600;
        color: #303f9f; /* Matching theme color */
        white-space: nowrap;
        font-size: 0.95rem;
      }

      .status-filter select, .search-box input {
        padding: 10px 14px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background-color: white;
        font-size: 14px;
        color: #495057;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      }

      .status-filter select:focus, .search-box input:focus {
        border-color: #3f51b5;
        outline: 0;
        box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.15);
      }

      .status-filter select {
        cursor: pointer;
        min-width: 160px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23303f9f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px;
        padding-right: 36px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
      }

      .search-box {
        position: relative;
        flex-grow: 1;
        max-width: 380px;
      }

      .search-box i {
        position: absolute;
        left: 14px;
        top: 50%;
        transform: translateY(-50%);
        color: #3f51b5;
        font-size: 15px;
      }

      .search-box input {
        width: 100%;
        padding-left: 40px;
        transition: all 0.3s ease;
      }

      .search-box input:hover {
        border-color: #c5cae9;
      }

      .retry-button {
        background: linear-gradient(135deg, #2196f3, #1976d2);
        color: white;
        border: none;
        padding: 10px 18px;
        border-radius: 8px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        white-space: nowrap;
        box-shadow: 0 2px 5px rgba(25, 118, 210, 0.2);
      }

      .retry-button i {
        font-size: 14px;
      }

      .retry-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: none !important;
      }

      /* Retry button hover and focus styles */
      .retry-button:hover:not(:disabled) {
        background: linear-gradient(135deg, #1e88e5, #1565c0);
        box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
        transform: translateY(-1px);
      }

      .retry-button:focus:not(:disabled) {
        outline: none;
        box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
      }


      /* --- Loading, Error, No Data States --- */
      .loading-container, .error-message, .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 50px 30px;
        text-align: center;
        margin-top: 20px;
        border-radius: 12px;
        background-color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.03);
        transition: all 0.3s ease;
      }

      .loading-spinner {
        width: 48px;
        height: 48px;
        border: 4px solid rgba(63, 81, 181, 0.1);
        border-top-color: #3f51b5;
        border-radius: 50%;
        animation: spin 0.9s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        margin-bottom: 20px;
        box-shadow: 0 0 10px rgba(63, 81, 181, 0.1);
      }

      @keyframes spin {
        to { transform: rotate(360deg); }
      }

      .fa-spin {
        animation: spin 1s linear infinite;
      }

      .loading-container p {
        font-size: 1.1rem;
        color: #3f51b5;
        font-weight: 500;
        margin-top: 5px;
      }

      .error-message {
        background-color: #fff8f8;
        color: #d32f2f;
        border: 1px solid rgba(211, 47, 47, 0.1);
        padding: 40px 30px;
      }

      .error-message i.fa-exclamation-triangle {
        font-size: 42px;
        margin-bottom: 16px;
        color: #d32f2f;
        opacity: 0.9;
      }

      .error-message p {
        margin-bottom: 20px;
        max-width: 600px;
        word-wrap: break-word;
        line-height: 1.5;
        font-size: 1.05rem;
      }

      .error-message strong {
        display: block;
        margin-bottom: 8px;
        font-size: 1.2rem;
        font-weight: 600;
      }

      .no-data {
        background-color: #f5f7ff;
        color: #3f51b5;
        border: 1px solid rgba(63, 81, 181, 0.1);
        padding: 50px 30px;
      }

      .no-data i {
        font-size: 48px;
        margin-bottom: 20px;
        color: #3f51b5;
        opacity: 0.8;
      }

      .no-data p {
        font-size: 1.1rem;
        font-weight: 500;
        margin-top: 5px;
        color: #303f9f;
      }

      /* --- Table Styles --- */
      .table-container {
        overflow-x: auto;
        margin-bottom: 30px;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
        border: 1px solid rgba(0, 0, 0, 0.03);
      }

      .suggestion-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        font-size: 14px;
        min-width: 1000px;
      }

      .suggestion-table th, .suggestion-table td {
        padding: 14px 18px;
        border-bottom: 1px solid #e9ecef;
        vertical-align: middle;
        text-align: left;
        transition: all 0.25s ease;
      }

      .suggestion-table th {
        background: linear-gradient(to bottom, #f8f9fa, #f1f3f5);
        font-weight: 600;
        color: #303f9f;
        white-space: nowrap;
        border-bottom: 2px solid #c5cae9;
        position: sticky;
        top: 0;
        z-index: 10;
        padding-top: 16px;
        padding-bottom: 16px;
        font-size: 0.95rem;
      }

      .suggestion-table th:first-child {
        border-top-left-radius: 12px;
      }

      .suggestion-table th:last-child {
        border-top-right-radius: 12px;
      }

      .suggestion-table tr:last-child td {
        border-bottom: none;
      }

      .suggestion-table tr:last-child td:first-child {
        border-bottom-left-radius: 12px;
      }

      .suggestion-table tr:last-child td:last-child {
        border-bottom-right-radius: 12px;
      }

      .suggestion-table tr:hover td:not(.actions-cell) {
        background-color: #f5f7ff;
      }

      .suggestion-table tr:nth-child(even) {
        background-color: #fafbff;
      }

      /* Style for row being updated */
      .suggestion-table tr.updating td {
        background-color: #e8eaf6 !important;
        opacity: 0.8;
        box-shadow: inset 0 0 0 1px rgba(63, 81, 181, 0.1);
      }


      /* User information styles */
      .user-id {
        font-weight: 600;
        color: #303f9f;
        font-size: 13px;
        background-color: rgba(63, 81, 181, 0.08);
        padding: 3px 8px;
        border-radius: 4px;
        display: inline-block;
      }

      .user-name, .user-email {
        font-size: 13.5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 150px;
        padding: 2px 0;
        transition: all 0.2s ease;
        display: inline-block;
      }

      .user-name {
        color: #212529;
        font-weight: 500;
      }

      .user-email {
        color: #5c6bc0;
        font-style: italic;
      }

      .user-name:hover, .user-email:hover {
        color: #3f51b5;
      }

      /* Price and date range styles */
      .price-range, .date-range {
        white-space: nowrap;
      }

      .price-container, .date-container {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 13.5px;
      }

      .price-from, .price-to, .date-from, .date-to {
        display: inline-block;
      }

      .price-separator, .date-separator {
        color: #9e9e9e;
        font-weight: bold;
      }

      .price-from, .date-from {
        color: #303f9f;
      }

      .price-to, .date-to {
        color: #5c6bc0;
      }

      .note-content {
        max-width: 280px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 13.5px;
        color: #555;
        cursor: default;
        padding: 4px 8px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border-left: 3px solid #c5cae9;
        transition: all 0.2s ease;
      }

      .note-content:hover {
        background-color: #eef;
        border-left-color: #3f51b5;
        max-width: 350px;
      }

      /* --- Status Badges --- */
      .status-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12.5px;
        font-weight: 600;
        text-align: center;
        white-space: nowrap;
        line-height: 1;
        min-width: 100px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .status-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        opacity: 0.7;
      }

      .status-badge.waiting {
        background-color: #fff8e1;
        color: #ff8f00;
      }
      .status-badge.waiting::before { background-color: #ff8f00; }

      .status-badge.processing {
        background-color: #e3f2fd;
        color: #1976d2;
      }
      .status-badge.processing::before { background-color: #1976d2; }

      .status-badge.completed {
        background-color: #e8f5e9;
        color: #2e7d32;
      }
      .status-badge.completed::before { background-color: #2e7d32; }

      .status-badge.cancelled {
        background-color: #ffebee;
        color: #c62828;
      }
      .status-badge.cancelled::before { background-color: #c62828; }

      .status-badge.unknown {
        background-color: #f5f5f5;
        color: #616161;
      }
      .status-badge.unknown::before { background-color: #616161; }

      /* --- Action Buttons --- */
      .actions-cell {
        white-space: nowrap;
        display: flex;
        gap: 8px;
        justify-content: center;
        align-items: center;
        min-width: 140px; /* Ensure enough width for all buttons */
        padding: 0 10px; /* Add some padding */
      }

      .action-btn {
        width: 36px;
        height: 36px;
        border: none;
        background: white;
        border-radius: 8px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        color: #6c757d;
        font-size: 15px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
        position: relative;
        overflow: hidden;
        flex-shrink: 0; /* Prevent buttons from shrinking */
        
      }

      .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: currentColor;
        opacity: 0.1;
        transition: opacity 0.3s ease;
      }

      .action-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
      }

      .action-btn:hover:not(:disabled)::before {
        opacity: 0.2;
      }

      .action-btn:focus:not(:disabled) {
        outline: none;
        box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.2);
      }

      .action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
      }

      .action-btn.confirm {
        color: #2e7d32;
      }

      .action-btn.confirm:hover:not(:disabled) {
        color: #1b5e20;
      }

      .action-btn.unconfirm {
        color: #ff8f00;
      }

      .action-btn.unconfirm:hover:not(:disabled) {
        color: #ef6c00;
      }

      .action-btn.delete {
        color: #c62828;
      }

      .action-btn.delete:hover:not(:disabled) {
        color: #b71c1c;
      }

      /* Tooltip styles */
      .action-btn .tooltip {
        position: absolute;
        bottom: -30px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        pointer-events: none;
        z-index: 100;
      }

      .action-btn .tooltip::before {
        content: '';
        position: absolute;
        top: -4px;
        left: 50%;
        transform: translateX(-50%) rotate(45deg);
        width: 6px;
        height: 6px;
        background-color: rgba(0, 0, 0, 0.8);
      }

      .action-btn:hover .tooltip {
        opacity: 1;
        visibility: visible;
        bottom: -28px;
      }

      /* --- Pagination Styles --- */
      .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-top: 30px;
        padding: 16px 0;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
      }

      .page-btn {
        width: 38px;
        height: 38px;
        border: none;
        background-color: white;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #3f51b5;
        font-size: 15px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
      }

      .page-btn:hover:not(:disabled) {
        background-color: #e8eaf6;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
      }

      .page-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        color: #9e9e9e;
        background-color: #f5f5f5;
        box-shadow: none;
        transform: none !important;
      }

      .page-btn:focus:not(:disabled) {
        outline: none;
        box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.2);
      }

      .page-info {
        font-size: 14px;
        color: #303f9f;
        padding: 0 10px;
        font-weight: 500;
        background-color: #e8eaf6;
        border-radius: 20px;
        padding: 8px 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      /* Toast Notification */
      .toast-notification {
        position: fixed;
        bottom: 25px;
        right: 25px;
        padding: 14px 20px;
        border-radius: 10px;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
        z-index: 1000;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 12px;
        animation: slideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        max-width: 380px;
        border: 1px solid rgba(0, 0, 0, 0.03);
      }

      .toast-notification.success {
        background: linear-gradient(to right, #e8f5e9, #f1f8e9);
        color: #2e7d32;
        border-left: 5px solid #4caf50;
      }

      .toast-notification.error {
        background: linear-gradient(to right, #ffebee, #fce4ec);
        color: #c62828;
        border-left: 5px solid #f44336;
      }

      /* Styling for icons inside toast notifications */
      :global(.toast-notification i) {
        font-size: 18px;
        opacity: 0.9;
      }

      .toast-notification.hide {
        animation: slideOut 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
      }

      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }

      @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
      }
  </style>
