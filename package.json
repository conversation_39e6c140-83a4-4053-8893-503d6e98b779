{"name": "frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/kit": "^2.20.2", "@sveltejs/vite-plugin-svelte": "^5.0.0", "sass-embedded": "^1.86.3", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "^5.0.0", "vite": "^6.0.0"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/svelte-fontawesome": "^0.2.3", "aos": "^2.3.4", "axios": "^1.8.4", "bootstrap": "^5.3.5", "chart.js": "^4.4.9", "cors": "^2.8.5", "express": "^5.1.0", "gsap": "^3.12.7", "jsonwebtoken": "^9.0.2", "lucide-svelte": "^0.484.0", "mysql2": "^3.14.1", "node-fetch": "^3.3.2", "svelte-chartjs": "^3.1.5"}}