<script>
  import { createEventDispatcher } from 'svelte';
  import { onMount } from 'svelte';

  const dispatch = createEventDispatcher();

  export let staffId; 

  let nhanvien = {
    ma_nhan_vien: '',
    ho_ten: '',
    email: '',
    mat_khau: '', 
    so_dien_thoai: '',
    chuc_vu: '',
    ngay_sinh: '',
    dia_chi: ''
  };

  let isLoading = false;
  let hasError = false;
  let errorMessage = '';

  onMount(async () => {
    console.log('EditNhanVienForm mounted with staffId:', staffId);
    if (staffId) {
      await fetchNhanVienDetails();
    } else {
      console.error('No staffId provided on mount');
    }
  });

  $: {
    if (staffId) {
      console.log('staffId changed to:', staffId);
      setTimeout(() => {
        fetchNhanVienDetails();
      }, 0);
    }
  }

  async function fetchNhanVienDetails() {
    console.log('fetchNhanVienDetails called with staffId:', staffId);

    if (!staffId) {
      console.error('Invalid staffId:', staffId);
      hasError = true;
      errorMessage = 'ID nhân viên không hợp lệ';
      alert(errorMessage);
      return;
    }

    isLoading = true;
    hasError = false;
    errorMessage = '';

    try {
      const url = `http://localhost:5000/api/nhanvien/${staffId}`;
      console.log('Fetching from URL:', url);

      const response = await fetch(url);
      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error('Không thể lấy thông tin nhân viên');
      }

      const data = await response.json();
      console.log('Received data:', data);

      if (!data || typeof data !== 'object') {
        console.error('Invalid data received:', data);
        throw new Error('Dữ liệu nhân viên không hợp lệ');
      }

      const nhanvienData = data.nhanvien || data;
      console.log('Extracted nhanvien data:', nhanvienData);

      if (!nhanvienData || typeof nhanvienData !== 'object') {
        console.error('Invalid nhanvien data structure:', data);
        throw new Error('Cấu trúc dữ liệu nhân viên không hợp lệ');
      }

      console.log('Updating nhanvien with data:', nhanvienData);

      nhanvien = {
        ma_nhan_vien: nhanvienData.ma_nhan_vien || '',
        ho_ten: nhanvienData.ho_ten || '',
        email: nhanvienData.email || '',
        mat_khau: nhanvienData.mat_khau || '', 
        so_dien_thoai: nhanvienData.so_dien_thoai || '',
        chuc_vu: nhanvienData.chuc_vu || '',
        ngay_sinh: nhanvienData.ngay_sinh || '',
        dia_chi: nhanvienData.dia_chi || ''
      };

      console.log('Updated nhanvien:', nhanvien);

      if (nhanvien.ngay_sinh) {
        try {
          console.log('Original date string:', nhanvien.ngay_sinh);
          const date = new Date(nhanvien.ngay_sinh);
          console.log('Parsed date object:', date);

          if (!isNaN(date.getTime())) {
            const formattedDate = date.toISOString().split('T')[0];
            console.log('Formatted date string:', formattedDate);

            nhanvien = {
              ...nhanvien,
              ngay_sinh: formattedDate
            };
            console.log('Updated nhanvien with formatted date:', nhanvien);
          } else {
            console.error('Invalid date:', nhanvien.ngay_sinh);
          }
        } catch (error) {
          console.error('Error formatting date:', error);
        }
      } else {
        console.log('No date to format');
      }

      isLoading = false;
    } catch (error) {
      console.error('Error fetching nhanvien details:', error);

      isLoading = false;
      hasError = true;
      errorMessage = 'Có lỗi khi lấy thông tin nhân viên: ' + error.message;

      alert(errorMessage);
    }
  }

  async function handleSubmit() {
    console.log('handleSubmit called with staffId:', staffId);
    console.log('Data to submit:', nhanvien);

    isLoading = true;
    hasError = false;
    errorMessage = '';

    try {
      const url = `http://localhost:5000/api/nhanvien/${staffId}`;
      console.log('Sending PUT request to:', url);

      const dataToSend = { ...nhanvien };

      if (dataToSend.ngay_sinh) {
        try {
          const date = new Date(dataToSend.ngay_sinh);

          if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            dataToSend.ngay_sinh = `${year}-${month}-${day}`;
            console.log('Formatted date for submission:', dataToSend.ngay_sinh);
          } else {
            console.error('Invalid date for submission:', dataToSend.ngay_sinh);
          }
        } catch (error) {
          console.error('Error formatting date for submission:', error);
        }
      }

      if (!dataToSend.ho_ten) {
        throw new Error('Họ tên không được để trống');
      }
      if (!dataToSend.email) {
        throw new Error('Email không được để trống');
      }
      if (!dataToSend.so_dien_thoai) {
        throw new Error('Số điện thoại không được để trống');
      }
      if (!dataToSend.chuc_vu) {
        throw new Error('Chức vụ không được để trống');
      }
      if (!dataToSend.ngay_sinh) {
        throw new Error('Ngày sinh không được để trống');
      }
      if (!dataToSend.dia_chi) {
        throw new Error('Địa chỉ không được để trống');
      }

      const requestData = {
        ho_ten: dataToSend.ho_ten,
        email: dataToSend.email,
        so_dien_thoai: dataToSend.so_dien_thoai,
        chuc_vu: dataToSend.chuc_vu,
        ngay_sinh: dataToSend.ngay_sinh,
        dia_chi: dataToSend.dia_chi
      };

      if (dataToSend.mat_khau && dataToSend.mat_khau.trim() !== '') {
        requestData.mat_khau = dataToSend.mat_khau;
        console.log('Including password in request');
      } else {
        console.log('Password not included in request');
      }

      console.log('Data after formatting:', requestData);

      console.log('Sending data to server:', JSON.stringify(requestData));

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      console.log('PUT response status:', response.status);

      if (!response.ok) {
        let errorMessage = 'Không thể cập nhật thông tin nhân viên';

        try {
          const errorData = await response.json();
          console.error('Error response data:', errorData);

          if (errorData && errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData && errorData.error) {
            errorMessage = errorData.error;
          }

          console.error('Error details:', {
            status: response.status,
            statusText: response.statusText,
            errorData: errorData
          });
        } catch (parseError) {
          console.error('Error parsing error response:', parseError);

          try {
            const errorText = await response.text();
            console.error('Error response text:', errorText);

            if (errorText) {
              errorMessage = errorText;
            }
          } catch (textError) {
            console.error('Error getting error text:', textError);
          }
        }

        const detailedError = `Lỗi ${response.status}: ${errorMessage}`;
        console.error('Detailed error:', detailedError);

        throw new Error(detailedError);
      }

      isLoading = false;

      console.log('Dispatching staffUpdated event');
      dispatch('staffUpdated');

      alert('Cập nhật thông tin nhân viên thành công!');
    } catch (error) {
      console.error('Error updating nhanvien:', error);

      isLoading = false;
      hasError = true;
      errorMessage = 'Có lỗi khi cập nhật thông tin nhân viên: ' + error.message;

      alert(errorMessage);
    }
  }
</script>

<div class="edit-staff-form">
  <h2>Sửa Thông Tin Nhân Viên</h2>

  {#if isLoading}
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Đang tải thông tin nhân viên...</p>
    </div>
  {:else if hasError}
    <div class="error-container">
      <p class="error-message">{errorMessage}</p>
      <button class="btn-retry" on:click={fetchNhanVienDetails}>Thử lại</button>
    </div>
  {:else}
    <form on:submit|preventDefault={handleSubmit}>
      <div class="form-grid">
        <div class="form-group">
          <label for="hoTen">Họ tên</label>
          <input
            id="hoTen"
            type="text"
            bind:value={nhanvien.ho_ten}
            placeholder="Nhập họ tên"
            required
          >
        </div>

        <div class="form-group">
          <label for="email">Email</label>
          <input
            id="email"
            type="email"
            bind:value={nhanvien.email}
            placeholder="Nhập email"
            required
          >
        </div>

        <div class="form-group">
          <label for="matKhau">Mật khẩu</label>
          <input
            id="matKhau"
            type="password"
            bind:value={nhanvien.mat_khau}
            placeholder="Nhập mật khẩu mới (để trống nếu không thay đổi)"
          >
        </div>

        <div class="form-group">
          <label for="soDienThoai">Số điện thoại</label>
          <input
            id="soDienThoai"
            type="tel"
            bind:value={nhanvien.so_dien_thoai}
            placeholder="Nhập số điện thoại"
            required
          >
        </div>

        <div class="form-group">
          <label for="chucVu">Chức vụ</label>
          <input
            id="chucVu"
            type="text"
            bind:value={nhanvien.chuc_vu}
            placeholder="Nhập chức vụ"
            required
          >
        </div>

        <div class="form-group">
          <label for="ngaySinh">Ngày sinh</label>
          <input
            id="ngaySinh"
            type="date"
            bind:value={nhanvien.ngay_sinh}
            required
          >
        </div>

        <div class="form-group full-width">
          <label for="diaChi">Địa chỉ</label>
          <textarea
            id="diaChi"
            bind:value={nhanvien.dia_chi}
            placeholder="Nhập địa chỉ"
            required
            rows="3"
          ></textarea>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn-cancel" on:click={() => {
          console.log('Cancel button clicked');
          dispatch('cancel');
        }}>Hủy</button>
        <button type="submit" class="btn-save">Lưu Thay Đổi</button>
      </div>
    </form>
  {/if}
</div>

<style>
  .edit-staff-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  /* Loading styles */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Error styles */
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
  }

  .error-message {
    color: #e53e3e;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  .btn-retry {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .btn-retry:hover {
    background-color: #45a049;
  }

  h2 {
    color: #2c3e50;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    font-weight: 600;
    text-align: center;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group.full-width {
    grid-column: 1 / -1;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    color: #4a5568;
    font-weight: 500;
    font-size: 0.95rem;
  }

  input,
  textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: white;
  }

  input:hover,
  textarea:hover {
    border-color: #cbd5e0;
  }

  input:focus,
  textarea:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
  }

  textarea {
    resize: vertical;
    min-height: 120px;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .btn-save,
  .btn-cancel {
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-save {
    background: #4CAF50;
    color: white;
    border: none;
  }

  .btn-save:hover {
    background: #45a049;
    transform: translateY(-1px);
  }

  .btn-cancel {
    background: white;
    border: 2px solid #e2e8f0;
    color: #4a5568;
  }

  .btn-cancel:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
  }

  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
