<script>
    import { fly } from 'svelte/transition';
  
    let currentIndex = 0;
    let carouselEl;
  
    const images = [
      { src: "/images/slide1.jpg", title: "Chào mừng bạn!", description: "H<PERSON>y khám phá những điều tuyệt vời cùng chúng tôi" },
      { src: "/images/slide2.jpg", title: "Xin chào!", description: "Trải nghiệm những khoảnh khắc đáng nhớ" },
      { src: "/images/slide3.jpg", title: "Chào mừng!", description: "Cùng tận hưởng hành trình thú vị" },
      { src: "/images/slide4.jpg", title: "Hello!", description: "Khám phá vẻ đẹp bất tận" },
      { src: "/images/slide5.jpg", title: "Welcome!", description: "Hành trình của bạn bắt đầu từ đây" },
      { src: "/images/slide6.jpg", title: "Chào bạn!", description: "<PERSON><PERSON>ng chúng tôi tạo nên những kỷ niệm đẹp" },
      { src: "/images/slide7.jpg", title: "Xin chào!", description: "Hãy để chúng tôi đồng hành cùng bạn" },
      { src: "/images/slide8.jpg", title: "Welcome!", description: "Khám phá những điều mới mẻ mỗi ngày" }
    ];
  
    function moveLeft() {
      currentIndex = currentIndex === 0
        ? images.length - 1
        : currentIndex - 1;
      scrollToCurrent();
    }
  
    function moveRight() {
      currentIndex = currentIndex === images.length - 1
        ? 0
        : currentIndex + 1;
      scrollToCurrent();
    }
  
    function scrollToCurrent() {
      if (carouselEl) {
        const scrollAmount = currentIndex * 140;
        carouselEl.scrollTo({ left: scrollAmount, behavior: 'smooth' });
      }
    }
  </script>
  
  <div class="slide-container">
    <div class="frame">
      <!-- Ảnh chính -->
      <div class="left-side">
        {#if images[currentIndex]}
          <img
            src={images[currentIndex].src}
            alt={`Image ${currentIndex + 1}`}
            class="featured-image"
          />
        {/if}
      </div>
  
      <!-- Phần chữ với fly transition -->
      <div class="right-side">
        {#if images[currentIndex]}
          {#key currentIndex}
            <div
              class="text-block"
              in:fly={{ x: 200, duration: 400 }}
              out:fly={{ x: -200, duration: 400 }}
            >
              <h2>{images[currentIndex].title}</h2>
              <p>{images[currentIndex].description}</p>
            </div>
          {/key}
        {/if}
  
        <!-- Carousel thumbnails -->
        <div class="carousel" bind:this={carouselEl}>
          {#each images as img, index}
            <img
              src={img.src}
              alt={`Image ${index + 1}`}
              class={currentIndex === index ? "active" : ""}
              on:click={() => {
                currentIndex = index;
                scrollToCurrent();
              }}
            />
          {/each}
        </div>
  
        <!-- Nút điều hướng -->
        <button class="carousel-button left" on:click={moveLeft} aria-label="Previous slide">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button class="carousel-button right" on:click={moveRight} aria-label="Next slide">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>
  </div>
<style>
 /* Container chính */
.slide-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 80px;
    padding: 20px;
    background: linear-gradient(135deg, #e3f2fd, #ffffff);
    border-radius: 16px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Khung chính */
.frame {
    display: flex;
    flex-wrap: wrap;
    width: 95%;
    height: 500px; /* Tăng chiều cao khung */
    border-radius: 24px;
    overflow: hidden;
    background: white;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
    position: relative;
}

/* Phần bên trái */
.left-side {
    flex: 1 1 50%;
    padding: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f9fbfd;
    border-radius: 24px 0 0 24px;
}

.left-side .featured-image {
    width: 500px; /* Giảm kích thước ảnh */
    height: 350px;
    object-fit: cover;
    border-radius: 16px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.left-side .featured-image:hover {
    transform: scale(1.08);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.3);
}

/* Phần bên phải */
.right-side {
    flex: 1 1 50%;
    padding: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 0 24px 24px 0;
}

.right-side h2 {
    font-size: 36px; /* Kích thước tiêu đề */
    margin-bottom: 10px;
    font-weight: bold;
}

.right-side p {
    font-size: 20px; /* Kích thước mô tả */
    line-height: 1.6;
}

/* Carousel */
.carousel {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    scroll-behavior: smooth;
    width: 100%;
    max-width: 600px;
    padding: 10px 0;
    scroll-snap-type: x mandatory;
    scrollbar-width: none;
    margin-top: 20px;
}

.carousel::-webkit-scrollbar {
    display: none;
}

.carousel img {
    width: 120px; /* Kích thước ảnh nhỏ */
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    flex-shrink: 0;
    scroll-snap-align: start;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.carousel img:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.carousel img.active {
    border: 3px solid #ff9800;
    transform: scale(1.1);
}

/* Nút điều hướng carousel */
.carousel-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(255, 255, 255, 0.95);
    color: #0056b3;
    border: none;
    width: 45px;
    height: 45px;
    cursor: pointer;
    z-index: 2;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease, opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
    opacity: 0;
}

.frame:hover .carousel-button {
    opacity: 1;
}

.carousel-button i {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.carousel-button:hover {
    background-color: #0056b3;
    color: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.carousel-button:hover i {
    transform: scale(1.2);
}

.carousel-button:active {
    transform: translateY(-50%) scale(0.95);
}

.carousel-button.left {
    left: 20px;
}

.carousel-button.right {
    right: 20px;
}

/* Banner styles */
.banner {
    position: relative;
    height: 100vh;
    min-height: 600px;
    overflow: hidden;
}

.banner-slide {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.banner-slide.active {
    opacity: 1;
}

.banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.7);
}

.banner-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 2;
    width: 90%;
    max-width: 800px;
    animation: fadeInUp 1s ease-out;
}

.banner-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    opacity: 0;
    animation: slideInDown 1s ease-out forwards;
}

.banner-description {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
    opacity: 0;
    animation: slideInUp 1s ease-out 0.5s forwards;
}

.banner-button {
    display: inline-block;
    padding: 1rem 2.5rem;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #3498db, #2ecc71);
    color: white;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    opacity: 0;
    animation: fadeIn 1s ease-out 1s forwards;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.banner-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #2ecc71, #3498db);
}

.banner-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: white;
    transform: scale(1.2);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate(-50%, -30%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .banner-title {
        font-size: 2.5rem;
    }

    .banner-description {
        font-size: 1.2rem;
    }

    .banner-button {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .banner-title {
        font-size: 2rem;
    }

    .banner-description {
        font-size: 1rem;
    }
}
</style>