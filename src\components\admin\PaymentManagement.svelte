<script lang="ts">
  import { onMount } from 'svelte';
  import { bookings, hotelBookings } from '../../stores/userStore';

  interface Booking {
    ma_dat_tour: number;
    ma_tour: number;
    ma_nguoi_dung: number;
    ten_tour: string;
    ten_khach_hang?: string;
    email?: string;
    so_dien_thoai?: string;
    dia_diem: string;
    hinh_anh: string;
    ngay_bat_dau: string;
    ngay_ket_thuc: string;
    ngay_dat: string;
    so_nguoi: number;
    gia: number;
    tong_tien?: number;
    trang_thai: string;
    ghi_chu?: string;
  }

  interface Payment {
    ma_thanh_toan: number;
    ma_dat_tour?: number;
    hotel_booking_id?: number;
    ma_nguoi_dung?: number;
    ten_khach_hang?: string;
    email?: string;
    so_dien_thoai?: string;
    ten_tour?: string;
    ten_khach_san?: string;
    ngay_thanh_toan: string;
    so_tien: number;
    trang_thai_thanh_toan: string;
    payment_type?: 'tour' | 'hotel'; 
  }

  let allPayments: Payment[] = [];
  let allBookings: Booking[] = [];
  let currentPage = 1;
  let itemsPerPage = 10;
  let searchQuery = '';
  let statusFilter = 'all';
  let isLoading = true;
  let error = '';
  let showAddPaymentForm = false;
  let selectedBookingId: number | null = null;
  let selectedBooking: Booking | null = null;
  let paymentAmount: number | null = null;

  function getAdjustedBookingId(bookings: Booking[], currentBooking: Booking): number {
    if (bookings.length === 0) return 1;

    const minBookingId = Math.min(...bookings.map(b => b.ma_dat_tour));

    return currentBooking.ma_dat_tour - minBookingId + 1;
  }

  function getAdjustedPaymentId(payments: Payment[], currentPayment: Payment): number {
    if (payments.length === 0) return 1;

    const minPaymentId = Math.min(...payments.map(p => p.ma_thanh_toan));

    return currentPayment.ma_thanh_toan - minPaymentId + 1;
  }

  function sortPayments(paymentsToSort: Payment[]): Payment[] {
    return [...paymentsToSort].sort((a, b) => a.ma_thanh_toan - b.ma_thanh_toan);
  }

  $: filteredPayments = sortPayments(allPayments.filter(payment => {
    if (statusFilter !== 'all' && payment.trang_thai_thanh_toan !== statusFilter) {
      return false;
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        payment.ten_khach_hang?.toLowerCase().includes(query) ||
        payment.ten_tour?.toLowerCase().includes(query)
      );
    }

    return true;
  }));

  $: paginatedPayments = filteredPayments.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  $: totalPages = Math.ceil(filteredPayments.length / itemsPerPage);

  onMount(() => {
    loadPayments();
    loadBookings();

    const unsubscribe = bookings.subscribe(updatedBookings => {
      allBookings = updatedBookings;
      console.log('Bookings updated from store:', allBookings.length);
    });

    return unsubscribe;
  });

  async function loadBookings() {
    try {
      console.log('Loading bookings...');

      if ($bookings.length > 0) {
        allBookings = $bookings;
        console.log('Loaded bookings from store:', allBookings.length);
        return;
      }

      const response = await fetch('http://localhost:5000/api/bookings');

      if (!response.ok) {
        throw new Error(`Lỗi ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      allBookings = data.bookings || [];

      bookings.set(allBookings);

      console.log('Loaded bookings from API:', allBookings.length);
    } catch (err) {
      console.error('Lỗi khi tải dữ liệu đặt tour:', err);
    }
  }

  async function loadPayments() {
    try {
      isLoading = true;
      error = '';

      const response = await fetch('http://localhost:5000/api/payments');

      if (!response.ok) {
        throw new Error(`Lỗi ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      allPayments = data.payments || [];

      console.log('Loaded payments:', allPayments);
    } catch (err) {
      console.error('Lỗi khi tải dữ liệu thanh toán:', err);
      error = `Không thể tải dữ liệu thanh toán: ${err.message}`;
    } finally {
      isLoading = false;
    }
  }

  function formatPrice(price: number): string {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' VNĐ';
  }

  function formatDate(dateString: string): string {
    if (!dateString) return 'Chưa thanh toán';
    return new Date(dateString).toLocaleDateString('vi-VN');
  }

  function getStatusText(status: string): string {
    switch (status) {
      case 'hoan_tat': return 'Đã thanh toán';
      case 'cho_xu_ly': return 'Chờ thanh toán';
      case 'that_bai': return 'Thất bại';
      default: return status;
    }
  }

  function getStatusClass(status: string): string {
    switch (status) {
      case 'hoan_tat': return 'status-success';
      case 'cho_xu_ly': return 'status-pending';
      case 'that_bai': return 'status-cancelled';
      default: return '';
    }
  }

  async function updatePaymentStatus(payment: Payment, newStatus: string) {
    try {
      console.log(`Cập nhật trạng thái thanh toán ID ${payment.ma_thanh_toan} thành ${newStatus}`);

      isLoading = true;

      const response = await fetch(`http://localhost:5000/api/payments/${payment.ma_thanh_toan}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          trang_thai_thanh_toan: newStatus
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Lỗi ${response.status}: ${response.statusText}`);
      }

      await loadPayments();

      if (newStatus === 'hoan_tat') {
        if (payment.ma_dat_tour) {
          const relatedBooking = allBookings.find(booking => booking.ma_dat_tour === payment.ma_dat_tour);

          if (relatedBooking) {
            console.log(`Updating tour booking status for booking ID ${payment.ma_dat_tour} to "da_thanh_toan"`);
            try {
              console.log('Updating tour booking status in API...');
              const updatePayload = {
                ma_dat_tour: relatedBooking.ma_dat_tour,
                ma_tour: relatedBooking.ma_tour,
                ma_nguoi_dung: relatedBooking.ma_nguoi_dung,
                so_nguoi: relatedBooking.so_nguoi,
                trang_thai: 'da_thanh_toan',
                ghi_chu: relatedBooking.ghi_chu || null
              };

              console.log('Sending update with payload:', updatePayload);

              const updateResponse = await fetch(`http://localhost:5000/api/bookings/${relatedBooking.ma_dat_tour}`, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify(updatePayload)
              });

              if (!updateResponse.ok) {
                console.error('Failed to update tour booking status in API:', await updateResponse.text());
              } else {
                console.log('Successfully updated tour booking status in API');
              }
            } catch (updateError) {
              console.error('Error updating tour booking status in API:', updateError);
            }
            bookings.update(currentBookings => {
              return currentBookings.map((booking: Booking) => {
                if (booking.ma_dat_tour === payment.ma_dat_tour) {
                  return { ...booking, trang_thai: 'da_thanh_toan' };
                }
                return booking;
              });
            });
            try {
              console.log('Creating bookingStatusChanged event with details:', {
                bookingId: payment.ma_dat_tour,
                bookingType: 'tour',
                newStatus: 'da_thanh_toan'
              });

              const bookingStatusChangedEvent = new CustomEvent('bookingStatusChanged', {
                detail: {
                  bookingId: payment.ma_dat_tour,
                  bookingType: 'tour',
                  newStatus: 'da_thanh_toan',
                  timestamp: new Date().toISOString()
                }
              });

              console.log('Dispatching bookingStatusChanged event on document');
              document.dispatchEvent(bookingStatusChangedEvent);

              console.log('Dispatching bookingStatusChanged event on window');
              window.dispatchEvent(bookingStatusChangedEvent);
              const simpleEvent = new CustomEvent('bookingStatusChanged');
              document.dispatchEvent(simpleEvent);
            } catch (eventError) {
              console.error('Error dispatching bookingStatusChanged event for tour:', eventError);
            }

            console.log('PaymentManagement: Dispatched bookingStatusChanged event with details:', {
              bookingId: payment.ma_dat_tour,
              bookingType: 'tour',
              newStatus: 'da_thanh_toan',
              timestamp: new Date().toISOString()
            });
          }
        }
        else if (payment.hotel_booking_id) {
          console.log(`Updating hotel booking status for booking ID ${payment.hotel_booking_id} to "confirmed"`);
          hotelBookings.update(currentBookings => {
            return currentBookings.map((booking: any) => {
              if (booking.id === payment.hotel_booking_id || booking.ma_dat_phong === payment.hotel_booking_id) {
                console.log(`Updating hotel booking ${payment.hotel_booking_id} status from ${booking.status || 'N/A'} to confirmed`);
                return { ...booking, status: 'confirmed' };
              }
              return booking;
            });
          });
          try {
            console.log('Creating bookingStatusChanged event for hotel booking with details:', {
              bookingId: payment.hotel_booking_id,
              bookingType: 'hotel',
              newStatus: 'confirmed'
            });

            const bookingStatusChangedEvent = new CustomEvent('bookingStatusChanged', {
              detail: {
                bookingId: payment.hotel_booking_id,
                bookingType: 'hotel',
                newStatus: 'confirmed',
                timestamp: new Date().toISOString()
              }
            });

            console.log('Dispatching hotel bookingStatusChanged event on document');
            document.dispatchEvent(bookingStatusChangedEvent);

            console.log('Dispatching hotel bookingStatusChanged event on window');
            window.dispatchEvent(bookingStatusChangedEvent);
          } catch (eventError) {
            console.error('Error dispatching bookingStatusChanged event for hotel:', eventError);
          }

          console.log('PaymentManagement: Dispatched bookingStatusChanged event for hotel with details:', {
            bookingId: payment.hotel_booking_id,
            bookingType: 'hotel',
            newStatus: 'confirmed',
            timestamp: new Date().toISOString()
          });
        }
      }

      await loadBookings();

      alert(`Đã cập nhật trạng thái thanh toán thành ${getStatusText(newStatus)}`);

    } catch (err) {
      console.error('Lỗi khi cập nhật trạng thái thanh toán:', err);
      error = `Không thể cập nhật trạng thái thanh toán: ${err.message}`;
      alert(error);
    } finally {
      isLoading = false;
    }
  }

  async function deletePayment(payment: Payment) {
    if (confirm('Bạn có chắc chắn muốn xóa thanh toán này không?')) {
      try {
        console.log(`Xóa thanh toán ID ${payment.ma_thanh_toan}`);

        isLoading = true;

        const response = await fetch(`http://localhost:5000/api/payments/${payment.ma_thanh_toan}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Lỗi ${response.status}: ${response.statusText}`);
        }

        await loadPayments();

        alert('Đã xóa thanh toán thành công');

      } catch (err) {
        console.error('Lỗi khi xóa thanh toán:', err);
        error = `Không thể xóa thanh toán: ${err.message}`;
        alert(error);
      } finally {
        isLoading = false;
      }
    }
  }

  async function createPayment() {
    if (!selectedBookingId || !paymentAmount) {
      alert('Vui lòng nhập đầy đủ thông tin');
      return;
    }

    try {
      isLoading = true;

      const response = await fetch('http://localhost:5000/api/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ma_dat_tour: selectedBookingId,
          so_tien: paymentAmount,
          trang_thai_thanh_toan: 'cho_xu_ly'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Lỗi ${response.status}: ${response.statusText}`);
      }

      selectedBookingId = null;
      selectedBooking = null;
      paymentAmount = null;
      showAddPaymentForm = false;

      await loadPayments();

      alert('Đã tạo thanh toán mới thành công');

    } catch (err) {
      console.error('Lỗi khi tạo thanh toán mới:', err);
      error = `Không thể tạo thanh toán mới: ${err.message}`;
      alert(error);
    } finally {
      isLoading = false;
    }
  }

  function toggleAddPaymentForm() {
    showAddPaymentForm = !showAddPaymentForm;
    if (!showAddPaymentForm) {
      selectedBookingId = null;
      selectedBooking = null;
      paymentAmount = null;
    }
  }

  function handleBookingSelection(event: Event) {
    const selectElement = event.target as HTMLSelectElement;
    const bookingId = parseInt(selectElement.value);

    if (bookingId) {
      selectedBookingId = bookingId;
      selectedBooking = allBookings.find(b => b.ma_dat_tour === bookingId) || null;

      if (selectedBooking) {
        paymentAmount = selectedBooking.tong_tien || (selectedBooking.gia * selectedBooking.so_nguoi);
      }
    } else {
      selectedBookingId = null;
      selectedBooking = null;
      paymentAmount = null;
    }
  }

  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
    }
  }
</script>

<div class="payment-management">
  {#if isLoading}
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Đang tải dữ liệu thanh toán...</p>
    </div>
  {:else if error}
    <div class="error-message">
      <i class="fas fa-exclamation-circle"></i>
      <p>{error}</p>
      <button on:click={loadPayments} class="retry-button">
        <i class="fas fa-sync-alt"></i> Thử lại
      </button>
    </div>
  {:else}
    <div class="actions">
      <div class="filters">
        <div class="status-filter">
          <label for="status-select">Trạng thái:</label>
          <select id="status-select" bind:value={statusFilter}>
            <option value="all">Tất cả</option>
            <option value="hoan_tat">Hoàn tất</option>
            <option value="cho_xu_ly">Chờ xử lý</option>
            <option value="that_bai">Thất bại</option>
          </select>
        </div>
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input
            type="search"
            placeholder="Tìm kiếm theo tên khách hàng, tour..."
            bind:value={searchQuery}
            aria-label="Tìm kiếm thanh toán"
          >
        </div>
      </div>
      <button class="add-payment-btn" on:click={toggleAddPaymentForm}>
        <i class="fas fa-plus"></i> Thêm thanh toán mới
      </button>
    </div>

    {#if showAddPaymentForm}
      <div class="add-payment-form">
        <h3>Thêm thanh toán mới</h3>
        <div class="form-group">
          <label for="booking-id">Chọn đặt tour:</label>
          <select
            id="booking-id"
            on:change={handleBookingSelection}
            class="booking-select"
          >
            <option value="">-- Chọn đặt tour --</option>
            {#each allBookings.filter(b => b.trang_thai !== 'da_huy') as booking}
              <option value={booking.ma_dat_tour}>
                #{getAdjustedBookingId(allBookings, booking)} - {booking.ten_tour} - {booking.ten_khach_hang || 'Không có tên'}
              </option>
            {/each}
          </select>
        </div>

        {#if selectedBooking}
          <div class="booking-details">
            <h4>Thông tin đặt tour</h4>
            <div class="detail-row">
              <span class="detail-label">Tên tour:</span>
              <span class="detail-value">{selectedBooking.ten_tour}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Khách hàng:</span>
              <span class="detail-value">{selectedBooking.ten_khach_hang || 'Không có tên'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Ngày tour:</span>
              <span class="detail-value">{formatDate(selectedBooking.ngay_bat_dau)} - {formatDate(selectedBooking.ngay_ket_thuc)}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Số người:</span>
              <span class="detail-value">{selectedBooking.so_nguoi}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Tổng tiền:</span>
              <span class="detail-value">{formatPrice(selectedBooking.tong_tien || selectedBooking.gia * selectedBooking.so_nguoi)}</span>
            </div>
          </div>
        {/if}

        <div class="form-group">
          <label for="payment-amount">Số tiền thanh toán:</label>
          <input
            type="number"
            id="payment-amount"
            bind:value={paymentAmount}
            placeholder="Nhập số tiền thanh toán"
            min="1000"
          />
        </div>
        <div class="form-actions">
          <button class="cancel-btn" on:click={toggleAddPaymentForm}>Hủy</button>
          <button class="submit-btn" on:click={createPayment}>Tạo thanh toán</button>
        </div>
      </div>
    {/if}

    <div class="table-container">
      <table class="payment-table">
        <thead>
          <tr>
            <th>Mã thanh toán</th>
            <th>Khách hàng</th>
            <th>Tour</th>
            <th>Ngày thanh toán</th>
            <th>Số tiền</th>
            <th>Trạng thái</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {#if paginatedPayments.length === 0}
            <tr>
              <td colspan="7" class="no-data">
                <i class="fas fa-info-circle"></i>
                <p>Không có dữ liệu thanh toán</p>
              </td>
            </tr>
          {:else}
            {#each paginatedPayments as payment}
              <tr>
                <td>{getAdjustedPaymentId(allPayments, payment)}</td>
                <td class="customer-info">
                  <div class="customer-name">{payment.ten_khach_hang || 'Không có thông tin'}</div>
                </td>
                <td>{payment.ten_tour || 'Không có thông tin'}</td>
                <td>{formatDate(payment.ngay_thanh_toan)}</td>
                <td class="price-cell">{formatPrice(payment.so_tien)}</td>
                <td>
                  <span class="status-badge {getStatusClass(payment.trang_thai_thanh_toan)}">
                    {getStatusText(payment.trang_thai_thanh_toan)}
                  </span>
                </td>
                <td class="actions-cell">
                  {#if payment.trang_thai_thanh_toan === 'cho_xu_ly'}
                    <button
                      class="action-btn approve"
                      aria-label="Đã thanh toán"
                      title="Đã thanh toán"
                      on:click={() => updatePaymentStatus(payment, 'hoan_tat')}
                    >
                      <i class="fas fa-check"></i>
                    </button>
                    <button
                      class="action-btn reject"
                      aria-label="Đánh dấu thất bại"
                      title="Đánh dấu thất bại"
                      on:click={() => updatePaymentStatus(payment, 'that_bai')}
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  {/if}
                  <button
                    class="action-btn delete"
                    aria-label="Xóa thanh toán"
                    title="Xóa thanh toán"
                    on:click={() => deletePayment(payment)}
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>

    {#if totalPages > 1}
      <div class="pagination">
        <button
          class="pagination-btn"
          disabled={currentPage === 1}
          on:click={() => goToPage(1)}
          aria-label="Trang đầu"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button
          class="pagination-btn"
          disabled={currentPage === 1}
          on:click={() => goToPage(currentPage - 1)}
          aria-label="Trang trước"
        >
          <i class="fas fa-angle-left"></i>
        </button>

        <span class="pagination-info">Trang {currentPage} / {totalPages}</span>

        <button
          class="pagination-btn"
          disabled={currentPage === totalPages}
          on:click={() => goToPage(currentPage + 1)}
          aria-label="Trang sau"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button
          class="pagination-btn"
          disabled={currentPage === totalPages}
          on:click={() => goToPage(totalPages)}
          aria-label="Trang cuối"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
    {/if}
  {/if}
</div>

<style>
  .payment-management {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
  }

  .loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #3f51b5;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .error-message i {
    font-size: 1.5rem;
  }

  .retry-button {
    background-color: #c62828;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: auto;
  }

  .actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .filters {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
  }

  .status-filter,
  .payment-method-filter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .status-filter label {
    font-weight: 500;
    color: #555;
  }

  .status-filter select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
  }

  .add-payment-btn {
    background-color: #1a237e;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.2s;
  }

  .add-payment-btn:hover {
    background-color: #303f9f;
  }

  .add-payment-form {
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .add-payment-form h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #1a237e;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .form-group input,
  .form-group select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .booking-select {
    background-color: white;
    font-size: 14px;
  }

  .booking-details {
    background-color: #f0f4f8;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
    border-left: 4px solid #1a237e;
  }

  .booking-details h4 {
    margin-top: 0;
    margin-bottom: 0.75rem;
    color: #1a237e;
    font-size: 1rem;
  }

  .detail-row {
    display: flex;
    margin-bottom: 0.5rem;
  }

  .detail-label {
    font-weight: 500;
    width: 100px;
    color: #555;
  }

  .detail-value {
    flex: 1;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
  }

  .cancel-btn {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
  }

  .submit-btn {
    background-color: #1a237e;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
  }

  .search-box {
    position: relative;
    flex-grow: 1;
  }

  .search-box i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #777;
  }

  .search-box input {
    padding: 0.5rem 0.5rem 0.5rem 2rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 100%;
    min-width: 250px;
  }

  .table-container {
    overflow-x: auto;
    margin-bottom: 1.5rem;
  }

  .payment-table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
  }

  .payment-table th {
    background-color: #f5f5f5;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #ddd;
  }

  .payment-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
  }

  .payment-table tr:hover {
    background-color: #f9f9f9;
  }

  .customer-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .customer-name {
    font-weight: 500;
  }

  .customer-email,
  .customer-phone {
    font-size: 0.85rem;
    color: #666;
  }

  .price-cell {
    font-weight: 500;
    color: #1a237e;
  }

  .status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
  }

  .status-success {
    background-color: #e8f5e9;
    color: #2e7d32;
  }

  .status-warning {
    background-color: #fff8e1;
    color: #f57f17;
  }

  .status-pending {
    background-color: #e3f2fd;
    color: #1565c0;
  }

  .status-cancelled {
    background-color: #ffebee;
    color: #c62828;
  }

  .actions-cell {
    display: flex;
    gap: 0.5rem;
  }

  .action-btn {
    background: none;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
  }

  .action-btn.approve {
    background-color: #e8f5e9;
    color: #2e7d32;
  }

  .action-btn.approve:hover {
    background-color: #c8e6c9;
  }

  .action-btn.reject {
    background-color: #fff8e1;
    color: #f57f17;
  }

  .action-btn.reject:hover {
    background-color: #ffecb3;
  }

  .action-btn.delete {
    background-color: #ffebee;
    color: #c62828;
  }

  .action-btn.delete:hover {
    background-color: #ffcdd2;
  }

  .no-data {
    text-align: center;
    padding: 2rem !important;
    color: #777;
  }

  .no-data i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #bbb;
  }

  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
  }

  .pagination-btn {
    background: none;
    border: 1px solid #ddd;
    width: 36px;
    height: 36px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
  }

  .pagination-btn:hover:not(:disabled) {
    background-color: #f5f5f5;
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .pagination-info {
    margin: 0 0.5rem;
    color: #555;
  }

  @media (max-width: 768px) {
    .filters {
      flex-direction: column;
      align-items: stretch;
    }

    .search-box {
      width: 100%;
    }

    .payment-table th:nth-child(4),
    .payment-table td:nth-child(4) {
      display: none;
    }
  }
</style>
