<script lang="ts">
  import { onMount } from 'svelte';
  import AddHotelForm from './AddHotelForm.svelte';
  import EditHotelForm from './EditHotelForm.svelte';

  interface Hotel {
    ma_khach_san: number;
    ten_khach_san: string;
    dia_diem: string;
    dia_chi: string;
    so_dien_thoai?: string;
    so_sao: number;
    gia?: number | string;
    danh_gia_trung_binh?: number;
    mo_ta?: string;
    hinh_anh?: string;
  }

  let hotels: Hotel[] = [];
  let currentPage = 1;
  let itemsPerPage = 10;
  let searchQuery = '';
  let showAddHotelPage = false;
  let showEditHotelPage = false;
  let selectedHotelId: number | null = null;
  let isLoading = true;
  let fetchError: string | null = null;

  $: filteredHotels = hotels.filter(hotel => {
    const searchLower = searchQuery.toLowerCase();
    return (
      (hotel.ten_khach_san && hotel.ten_khach_san.toLowerCase().includes(searchLower)) ||
      (hotel.dia_diem && hotel.dia_diem.toLowerCase().includes(searchLower)) ||
      (hotel.dia_chi && hotel.dia_chi.toLowerCase().includes(searchLower)) ||
      (hotel.mo_ta && hotel.mo_ta.toLowerCase().includes(searchLower))
    );
  });

  $: startIndex = (currentPage - 1) * itemsPerPage;
  $: endIndex = startIndex + itemsPerPage;
  $: displayedHotels = filteredHotels.slice(startIndex, endIndex);
  $: totalPages = Math.ceil(filteredHotels.length / itemsPerPage);

  async function fetchHotels() {
    isLoading = true;
    fetchError = null;
    try {
      const response = await fetch('http://localhost:5000/api/khachsan');
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(errorData.message || 'Không thể tải danh sách khách sạn');
      }
      const data = await response.json();

      const processedHotels = (data.khachsan || []).map((hotel: Hotel) => {
        if (hotel.hinh_anh && !hotel.hinh_anh.includes('/')) {
          hotel.hinh_anh = `images/${hotel.hinh_anh}`;
        }
        return hotel;
      });

      hotels = processedHotels;
    } catch (error: any) {
      console.error('Error fetching hotels:', error);
      fetchError = 'Có lỗi khi tải danh sách khách sạn: ' + error.message;
    } finally {
      isLoading = false;
    }
  }

  async function handleDelete(id: number) {
    if (confirm('Bạn có chắc muốn xóa khách sạn này?')) {
      try {
        const response = await fetch(`http://localhost:5000/api/khachsan/${id}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: response.statusText }));
          throw new Error(errorData.message || 'Không thể xóa khách sạn');
        }

        hotels = hotels.filter(hotel => hotel.ma_khach_san !== id);
        alert('Xóa khách sạn thành công!');
      } catch (error: any) {
        console.error('Error deleting hotel:', error);
        alert('Có lỗi khi xóa khách sạn: ' + error.message);
      }
    }
  }

  function toggleAddHotelPage() {
    showAddHotelPage = !showAddHotelPage;
    showEditHotelPage = false;
    selectedHotelId = null;
  }

  function showEditHotel(id: number) {
    selectedHotelId = id;
    showEditHotelPage = true;
    showAddHotelPage = false;
  }

  function closeEditHotelPage() {
    showEditHotelPage = false;
    selectedHotelId = null;
  }

  function handleHotelAdded() {
    fetchHotels(); 
    showAddHotelPage = false; 
  }

  function handleHotelUpdated() {
    fetchHotels();
    closeEditHotelPage(); 
  }

  function formatPrice(price: number | string | undefined): string {
    if (price === undefined || price === null) return 'Chưa cập nhật';
    if (typeof price === 'string') {
      price = parseFloat(price);
    }
    return isNaN(price) ? 'Chưa cập nhật' : price.toLocaleString('vi-VN') + ' VNĐ';
  }

  function formatStars(stars: number): string {
    return '★'.repeat(stars) + '☆'.repeat(5 - stars);
  }

  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
    }
  }

  function formatImageUrl(imageUrl: string | undefined): string {
    if (!imageUrl) return '';

    if (imageUrl.startsWith('data:image')) {
      return imageUrl;
    } else if (imageUrl.startsWith('http')) {
      return imageUrl;
    } else {
      if (imageUrl.includes('/')) {
        return `/${imageUrl}`; 
      } else {
        return `/images/${imageUrl}`; 
      }
    }
  }

  onMount(fetchHotels);
</script>

<div class="hotel-management">
  <div class="content-header">
    <h2><i class="fas fa-hotel"></i> Quản lý Khách sạn</h2>
  </div>

  {#if isLoading}
    <div class="loading-indicator">Đang tải dữ liệu khách sạn...</div>
  {:else if fetchError}
    <div class="error-message">{fetchError}</div>
  {:else if !showAddHotelPage && !showEditHotelPage}
    <div class="actions">
      <button class="add-button" on:click={toggleAddHotelPage}>
        <i class="fas fa-plus"></i> Thêm Khách sạn Mới
      </button>
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="search"
          placeholder="Tìm kiếm theo tên, địa điểm, địa chỉ..."
          bind:value={searchQuery}
          aria-label="Tìm kiếm khách sạn"
        >
      </div>
    </div>

    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Mã</th>
            <th>Hình ảnh</th>
            <th>Tên Khách sạn</th>
            <th>Địa điểm</th>
            <th>Địa chỉ</th>
            <th>Số sao</th>
            <th>Giá</th>
            <th>Đánh giá</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {#each displayedHotels as hotel (hotel.ma_khach_san)}
            <tr>
              <td>{hotel.ma_khach_san}</td>
              <td class="image-cell">
                {#if hotel.hinh_anh}
                  <img
                    src={formatImageUrl(hotel.hinh_anh)}
                    alt={hotel.ten_khach_san}
                    class="hotel-thumbnail"
                    on:error={(e) => {
                      console.error('Lỗi tải hình ảnh:', hotel.hinh_anh);
                      const img = e.target as HTMLImageElement;
                      if (img.src.includes('/images/')) {
                        img.src = `http://localhost:5000${hotel.hinh_anh.startsWith('/') ? '' : '/'}${hotel.hinh_anh}`;
                      } else {
                        img.style.display = 'none';
                        const nextEl = img.nextElementSibling as HTMLElement;
                        if (nextEl) nextEl.style.display = 'flex';
                      }
                    }}
                  />
                  <div class="no-image" style="display: none;">Ảnh lỗi</div>
                {:else}
                  <div class="no-image">Không có ảnh</div>
                {/if}
              </td>
              <td class="hotel-name">{hotel.ten_khach_san}</td>
              <td>{hotel.dia_diem}</td>
              <td>{hotel.dia_chi || 'Chưa cập nhật'}</td>
              <td class="stars">{formatStars(hotel.so_sao)}</td>
              <td class="price">{formatPrice(hotel.gia)}</td>
              <td class="rating">
                {#if hotel.danh_gia_trung_binh}
                  <span class="rating-value">{hotel.danh_gia_trung_binh.toFixed(1)}</span>
                {:else}
                  <span class="no-rating">Chưa có</span>
                {/if}
              </td>
              <td class="actions-cell">
                <button
                  class="action-btn edit"
                  aria-label="Sửa khách sạn {hotel.ten_khach_san}"
                  title="Sửa khách sạn"
                  on:click={() => showEditHotel(hotel.ma_khach_san)}
                >
                  <i class="fas fa-edit" aria-hidden="true"></i>
                </button>
                <button
                  class="action-btn delete"
                  aria-label="Xóa khách sạn {hotel.ten_khach_san}"
                  title="Xóa khách sạn"
                  on:click={() => handleDelete(hotel.ma_khach_san)}
                >
                  <i class="fas fa-trash" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          {:else}
            <tr>
              <td colspan="9" class="no-data">
                {#if hotels.length === 0}
                  Chưa có khách sạn nào được tạo.
                {:else}
                  Không tìm thấy khách sạn nào phù hợp.
                {/if}
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>

    {#if totalPages > 1}
      <div class="pagination">
        <button
          class="pagination-btn"
          disabled={currentPage === 1}
          on:click={() => goToPage(1)}
          aria-label="Trang đầu"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button
          class="pagination-btn"
          disabled={currentPage === 1}
          on:click={() => goToPage(currentPage - 1)}
          aria-label="Trang trước"
        >
          <i class="fas fa-angle-left"></i>
        </button>

        {#each Array(totalPages) as _, i}
          {#if i + 1 === currentPage || i + 1 === 1 || i + 1 === totalPages || (i + 1 >= currentPage - 1 && i + 1 <= currentPage + 1)}
            <button
              class="pagination-btn page-number {i + 1 === currentPage ? 'active' : ''}"
              on:click={() => goToPage(i + 1)}
            >
              {i + 1}
            </button>
          {:else if i + 1 === currentPage - 2 || i + 1 === currentPage + 2}
            <span class="pagination-ellipsis">...</span>
          {/if}
        {/each}

        <button
          class="pagination-btn"
          disabled={currentPage === totalPages}
          on:click={() => goToPage(currentPage + 1)}
          aria-label="Trang sau"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button
          class="pagination-btn"
          disabled={currentPage === totalPages}
          on:click={() => goToPage(totalPages)}
          aria-label="Trang cuối"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
    {/if}
  {:else if showAddHotelPage}
    <div class="add-hotel-container">
      <div class="view-header">
        <button class="back-button" on:click={toggleAddHotelPage}>
          <i class="fas fa-arrow-left"></i> Quay lại danh sách
        </button>
      </div>
      <AddHotelForm on:hotelAdded={handleHotelAdded} />
    </div>
  {:else if showEditHotelPage && selectedHotelId !== null}
    <div class="edit-hotel-container">
      <div class="view-header">
        <button class="back-button" on:click={closeEditHotelPage}>
          <i class="fas fa-arrow-left"></i> Quay lại danh sách
        </button>
      </div>
      <EditHotelForm
        hotelId={selectedHotelId}
        on:hotelUpdated={handleHotelUpdated}
        on:cancel={closeEditHotelPage}
      />
    </div>
  {/if}
</div>

<style>
  .hotel-management {
    padding: 25px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .content-header h2 {
    font-size: 26px;
    color: #343a40;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
  }

  .actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
  }

  .add-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    transition: background-color 0.2s ease;
  }

  .add-button:hover {
    background-color: #0069d9;
  }

  .search-box {
    position: relative;
    flex-grow: 1;
    max-width: 400px;
  }

  .search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
  }

  .search-box input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }

  .search-box input:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .table-container {
    overflow-x: auto;
    margin-bottom: 25px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }

  table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
    font-size: 14px;
  }

  th, td {
    padding: 14px 16px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
    white-space: nowrap;
  }

  th {
    background-color: #e9ecef;
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
  }

  tr:last-child td {
    border-bottom: none;
  }

  tr:hover td {
    background-color: #f8f9fa;
  }

  .hotel-name {
    font-weight: 500;
    color: #212529;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .price {
    font-weight: 500;
    color: #28a745;
  }

  .stars {
    color: #ffc107;
    letter-spacing: 2px;
  }

  .rating-value {
    display: inline-block;
    background-color: #28a745;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-weight: 500;
  }

  .no-rating {
    color: #6c757d;
    font-style: italic;
  }

  .actions-cell {
    white-space: nowrap;
    text-align: center;
  }

  .action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    margin: 0 2px;
  }

  .action-btn.edit {
    color: #007bff;
  }

  .action-btn.delete {
    color: #dc3545;
  }

  .action-btn:hover {
    background-color: #f8f9fa;
  }

  .action-btn.edit:hover {
    color: #0056b3;
  }

  .action-btn.delete:hover {
    color: #bd2130;
  }

  .image-cell {
    width: 100px;
    text-align: center;
  }

  .hotel-thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    vertical-align: middle;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    background-color: #f8f9fa;
  }

  .hotel-thumbnail:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .no-image {
    width: 80px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
    color: #6c757d;
    font-size: 11px;
    border-radius: 4px;
    margin: 0 auto;
    text-align: center;
    line-height: 1.2;
  }

  /* Pagination */
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 25px;
  }

  .pagination-btn {
    background-color: #fff;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .pagination-btn:hover:not(:disabled) {
    background-color: #e9ecef;
    border-color: #ced4da;
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .pagination-btn.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
  }

  .pagination-ellipsis {
    color: #6c757d;
    padding: 0 4px;
  }

  /* No Data / Loading / Error Styles */
  .no-data, .loading-indicator, .error-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-size: 16px;
  }

  .error-message {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
  }

  /* Back Button and View Headers */
  .view-header {
    margin-bottom: 20px;
  }

  .back-button {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: 5px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    transition: background-color 0.2s ease;
  }

  .back-button:hover {
    background-color: #5a6268;
  }

  .add-hotel-container, .edit-hotel-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    margin-top: 20px;
  }
</style>