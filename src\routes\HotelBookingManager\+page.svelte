<!-- <script lang="ts">
  import { onMount } from 'svelte';
  import { user } from '../../stores/userStore';
  import { goto } from '$app/navigation';
  import Navbar from '../../components/Navbar.svelte';
  import Footer from '../../components/Footer.svelte';
  import HotelBookingManager from '../../components/admin/HotelBookingManager.svelte';

  onMount(() => {
    if (!$user || $user.vai_tro !== 'admin') {
      goto('/login');
    }
  });
</script>

<Navbar />

<div class="container">
  <div class="page-header">
    <h1><i class="fas fa-concierge-bell"></i> Quản lý Đặt phòng Khách sạn</h1>
    <p>Quản lý tất cả các đặt phòng khách sạn trong hệ thống</p>
  </div>

  <HotelBookingManager />
</div>

<Footer />

<style>
  .container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
  }

  .page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
  }

  .page-header h1 {
    font-size: 2rem;
    color: #1a237e;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .page-header p {
    color: #666;
    font-size: 1.1rem;
  }

  .page-header i {
    color: #303f9f;
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 0.5rem;
    }

    .page-header h1 {
      font-size: 1.75rem;
    }
  }
</style> -->
