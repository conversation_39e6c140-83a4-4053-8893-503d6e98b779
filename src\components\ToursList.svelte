<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';

  export let title = "Tour Du Lịch <PERSON>";

  // State variables
  let categoriesWithTours = [];
  let isLoading = true;
  let error = null;

  // Format price to VND
  function formatPrice(price) {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  }

  // Format date to DD/MM/YYYY
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  }

  // Calculate tour duration in days
  function calculateDuration(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }

  // Fetch tour categories and their tours
  async function fetchCategoriesWithTours() {
    isLoading = true;
    error = null;

    try {
      // Fetch all categories
      const categoriesResponse = await fetch('http://localhost:5000/api/phan-loai-tour');

      if (!categoriesResponse.ok) {
        throw new Error(`Lỗi ${categoriesResponse.status}: Không thể tải danh sách phân loại tour.`);
      }

      const categoriesData = await categoriesResponse.json();
      const categories = categoriesData.data || [];

      // Fetch tours for each category
      const categoriesWithToursPromises = categories.map(async (category) => {
        try {
          const toursResponse = await fetch(`http://localhost:5000/api/phan-loai-tour/${category.ma_phan_loai}/tours`);

          if (!toursResponse.ok) {
            console.error(`Không thể tải tour cho danh mục ${category.ten_phan_loai}`);
            return {
              ...category,
              tours: []
            };
          }

          const toursData = await toursResponse.json();
          return {
            ...category,
            tours: toursData.data || []
          };
        } catch (err) {
          console.error(`Lỗi khi tải tour cho danh mục ${category.ten_phan_loai}:`, err);
          return {
            ...category,
            tours: []
          };
        }
      });

      // Wait for all promises to resolve
      categoriesWithTours = await Promise.all(categoriesWithToursPromises);

      // Fetch all tours for "Tất cả tour" section
      const allToursResponse = await fetch('http://localhost:5000/api/tours');

      if (allToursResponse.ok) {
        const allToursData = await allToursResponse.json();
        // Add "Tất cả tour" as the first category
        categoriesWithTours.unshift({
          ma_phan_loai: 0,
          ten_phan_loai: 'Tất cả tour',
          mo_ta: 'Tất cả các tour du lịch',
          tours: allToursData.data || []
        });
      }

      // Filter out categories with no tours
      categoriesWithTours = categoriesWithTours.filter(category => category.tours.length > 0);

      isLoading = false;
    } catch (err) {
      console.error('Lỗi khi tải danh sách phân loại tour:', err);
      error = err.message;
      isLoading = false;
    }
  }

  // View tour details
  function viewTourDetails(tourId) {
    goto(`/tour/${tourId}`);
  }

  // Initialize component
  onMount(fetchCategoriesWithTours);
</script>

<section class="tours-section">
  <div class="container">
    <div class="section-header">
      <h2>{title}</h2>
      <p>Khám phá những điểm đến tuyệt vời cùng chúng tôi</p>
    </div>

    <!-- Loading and Error States -->
    {#if isLoading}
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <p>Đang tải danh sách tour...</p>
      </div>
    {:else if error}
      <div class="error-container">
        <p class="error-message">{error}</p>
        <button on:click={fetchCategoriesWithTours}>
          Thử lại
        </button>
      </div>
    {:else if categoriesWithTours.length === 0}
      <div class="no-results">
        <p>Không tìm thấy tour nào.</p>
      </div>
    {:else}
      <!-- Categories with Tours -->
      {#each categoriesWithTours as category (category.ma_phan_loai)}
        <div class="category-section">
          <h1 class="category-title">{category.ten_phan_loai}</h1>
          {#if category.mo_ta}
            <p class="category-description">{category.mo_ta}</p>
          {/if}

          {#if category.tours.length === 0}
            <div class="no-tours-in-category">
              <p>Không có tour nào trong danh mục này.</p>
            </div>
          {:else}
            <div class="tours-grid">
              {#each category.tours.slice(0, 6) as tour (tour.ma_tour)}
                <div class="tour-card">
                  <div class="tour-image">
                    <button
                      class="image-button"
                      on:click={() => viewTourDetails(tour.ma_tour)}
                      aria-label={`Xem chi tiết tour ${tour.ten_tour}`}
                    >
                      <img
                        src={tour.hinh_anh ? `/images/${tour.hinh_anh}` : '/images/default-tour.svg'}
                        alt={tour.ten_tour}
                      />
                    </button>
                    <div class="tour-type">
                      {tour.loai_tour === 'trong_nuoc' ? 'Trong nước' : 'Quốc tế'}
                    </div>
                  </div>

                  <div class="tour-content">
                    <button class="tour-title-button" on:click={() => viewTourDetails(tour.ma_tour)}>
                      <h3 class="tour-title">{tour.ten_tour}</h3>
                    </button>

                    <div class="tour-info">
                      <div class="info-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>{tour.diem_di} - {tour.diem_den}</span>
                      </div>

                      <div class="info-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>{formatDate(tour.ngay_bat_dau)} - {formatDate(tour.ngay_ket_thuc)}</span>
                      </div>

                      <div class="info-item">
                        <i class="fas fa-clock"></i>
                        <span>{calculateDuration(tour.ngay_bat_dau, tour.ngay_ket_thuc)} ngày</span>
                      </div>

                      <div class="info-item">
                        <i class="fas fa-users"></i>
                        <span>Còn {tour.so_cho_trong} chỗ</span>
                      </div>
                    </div>

                    <div class="tour-footer">
                      <div class="tour-price">
                        <span class="price">{formatPrice(tour.gia)}</span>
                        <span class="price-per">/người</span>
                      </div>

                      <button
                        class="view-details-button"
                        on:click={() => viewTourDetails(tour.ma_tour)}
                        disabled={tour.so_cho_trong <= 0}
                      >
                        {tour.so_cho_trong > 0 ? 'Xem thêm' : 'Hết chỗ'}
                      </button>
                    </div>
                  </div>
                </div>
              {/each}

              {#if category.tours.length > 6}
                <div class="view-more-container">
                  <button class="view-more-button" on:click={() => viewTourDetails(category.ma_phan_loai)}>
                    Xem thêm tour {category.ten_phan_loai}
                  </button>
                </div>
              {/if}
            </div>
          {/if}
        </div>
      {/each}
    {/if}
  </div>
</section>

<style>
  .tours-section {
    padding: 4rem 0;
    background-color: #f8f9fa;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .section-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .section-header h2 {
    font-size: 2.5rem;
    color: #343a40;
    margin-bottom: 0.5rem;
  }

  .section-header p {
    font-size: 1.1rem;
    color: #6c757d;
  }

  /* Category Sections */
  .category-section {
    margin-bottom: 4rem;
  }

  .category-title {
    font-size: 2.2rem;
    color: #343a40;
    margin-bottom: 0.5rem;
    position: relative;
    padding-bottom: 0.5rem;
  }

  .category-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background-color: #007bff;
  }

  .category-description {
    color: #6c757d;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
  }

  .no-tours-in-category {
    background-color: #f8f9fa;
    padding: 2rem;
    text-align: center;
    border-radius: 8px;
    color: #6c757d;
  }

  .view-more-container {
    text-align: center;
    margin-top: 1.5rem;
    grid-column: 1 / -1;
  }

  .view-more-button {
    padding: 0.75rem 1.5rem;
    background-color: transparent;
    color: #007bff;
    border: 1px solid #007bff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
  }

  .view-more-button:hover {
    background-color: #007bff;
    color: white;
  }

  /* Loading and Error States */
  .loading-container,
  .error-container,
  .no-results {
    text-align: center;
    padding: 3rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .loading-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-message {
    color: #dc3545;
    margin-bottom: 1rem;
  }

  .error-container button {
    padding: 0.75rem 1.5rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .error-container button:hover {
    background-color: #0069d9;
  }

  /* Tour Grid */
  .tours-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .tour-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
  }

  .tour-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .tour-image {
    position: relative;
    height: 200px;
    overflow: hidden;
  }

  .image-button {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    border: none;
    background: none;
    cursor: pointer;
    overflow: hidden;
  }

  .tour-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
  }

  .image-button:hover img {
    transform: scale(1.05);
  }

  .tour-type {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: rgba(0, 123, 255, 0.8);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
  }

  .tour-content {
    padding: 1.5rem;
  }

  .tour-title-button {
    background: none;
    border: none;
    padding: 0;
    width: 100%;
    text-align: left;
    cursor: pointer;
    margin-bottom: 1rem;
  }

  .tour-title {
    font-size: 1.25rem;
    margin: 0;
    color: #343a40;
    transition: color 0.2s;
  }

  .tour-title-button:hover .tour-title {
    color: #007bff;
  }

  .tour-info {
    margin-bottom: 1.5rem;
  }

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: #6c757d;
  }

  .info-item i {
    width: 20px;
    margin-right: 0.5rem;
    color: #007bff;
  }

  .tour-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
  }

  .tour-price {
    display: flex;
    flex-direction: column;
  }

  .price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #28a745;
  }

  .price-per {
    font-size: 0.8rem;
    color: #6c757d;
  }

  .view-details-button {
    padding: 0.5rem 1.25rem;
    background-color: transparent;
    color: #007bff;
    border: 1px solid #007bff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
  }

  .view-details-button:hover {
    background-color: #007bff;
    color: white;
  }

  .view-details-button:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .tours-grid {
      grid-template-columns: 1fr;
    }

    .category-title {
      font-size: 1.8rem;
    }

    .category-description {
      font-size: 1rem;
    }
  }
</style>