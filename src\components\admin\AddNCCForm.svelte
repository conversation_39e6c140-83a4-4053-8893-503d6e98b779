<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher<{
    supplierAdded: { ma_nha_cung_cap: number }
  }>();

  const serviceTypes = [
    { value: 'tour', label: 'Tour' },
    { value: 'khach_san', label: 'Khách sạn' },
    { value: 'phuong_tien', label: 'Phương tiện' },
    { value: 'vui_choi', label: 'Vui chơi' }
  ];
  let formData = {
    ten_nha_cung_cap: '',
    loai_dich_vu: 'tour',
    dia_chi: '',
    so_dien_thoai: '',
    email: '',
    ghi_chu: ''
  };
  let isSubmitting = false;
  let error: string | null = null;
  let success = false;
  let errors = {
    ten_nha_cung_cap: '',
    loai_dich_vu: '',
    email: '',
    ghi_chu: ''
  };
  function validateForm() {
    let isValid = true;
    errors = {
      ten_nha_cung_cap: '',
      loai_dich_vu: '',
      email: '',
      ghi_chu: ''
    };
    if (!formData.ten_nha_cung_cap.trim()) {
      errors.ten_nha_cung_cap = 'Tên nhà cung cấp không được để trống';
      isValid = false;
    }

    if (!formData.loai_dich_vu) {
      errors.loai_dich_vu = 'Vui lòng chọn loại dịch vụ';
      isValid = false;
    }
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Email không hợp lệ';
      isValid = false;
    }

    return isValid;
  }
  async function handleSubmit() {
    if (!validateForm()) {
      return;
    }

    isSubmitting = true;
    error = null;
    success = false;

    try {
      const response = await fetch('http://localhost:5000/api/nhacungcap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Lỗi ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      success = true;

      formData = {
        ten_nha_cung_cap: '',
        loai_dich_vu: 'tour',
        dia_chi: '',
        so_dien_thoai: '',
        email: '',
        ghi_chu: ''
      };
      dispatch('supplierAdded', data);
    } catch (err) {
      console.error('Lỗi khi thêm nhà cung cấp:', err);
      error = err.message;
    } finally {
      isSubmitting = false;
    }
  }
</script>

<div class="add-supplier-form">
  <h2>Thêm Nhà Cung Cấp Mới</h2>

  {#if error}
    <div class="error-message">
      <i class="fas fa-exclamation-circle"></i> {error}
    </div>
  {/if}

  {#if success}
    <div class="success-message">
      <i class="fas fa-check-circle"></i> Thêm nhà cung cấp thành công!
    </div>
  {/if}

  <form on:submit|preventDefault={handleSubmit}>
    <div class="form-group">
      <label for="ten_nha_cung_cap">
        Tên nhà cung cấp <span class="required">*</span>
      </label>
      <input
        type="text"
        id="ten_nha_cung_cap"
        bind:value={formData.ten_nha_cung_cap}
        class:error={errors.ten_nha_cung_cap}
        placeholder="Nhập tên nhà cung cấp"
        required
      />
      {#if errors.ten_nha_cung_cap}
        <div class="error-text">{errors.ten_nha_cung_cap}</div>
      {/if}
    </div>

    <div class="form-group">
      <label for="loai_dich_vu">
        Loại dịch vụ <span class="required">*</span>
      </label>
      <select
        id="loai_dich_vu"
        bind:value={formData.loai_dich_vu}
        class:error={errors.loai_dich_vu}
        required
      >
        {#each serviceTypes as type}
          <option value={type.value}>{type.label}</option>
        {/each}
      </select>
      {#if errors.loai_dich_vu}
        <div class="error-text">{errors.loai_dich_vu}</div>
      {/if}
    </div>

    <div class="form-group">
      <label for="dia_chi">Địa chỉ</label>
      <input
        type="text"
        id="dia_chi"
        bind:value={formData.dia_chi}
        placeholder="Nhập địa chỉ"
      />
    </div>

    <div class="form-group">
      <label for="so_dien_thoai">Số điện thoại</label>
      <input
        type="tel"
        id="so_dien_thoai"
        bind:value={formData.so_dien_thoai}
        placeholder="Nhập số điện thoại"
      />
    </div>

    <div class="form-group">
      <label for="email">Email</label>
      <input
        type="email"
        id="email"
        bind:value={formData.email}
        class:error={errors.email}
        placeholder="Nhập email"
      />
      {#if errors.email}
        <div class="error-text">{errors.email}</div>
      {/if}
    </div>



    <div class="form-group">
      <label for="ghi_chu">Ghi chú</label>
      <textarea
        id="ghi_chu"
        bind:value={formData.ghi_chu}
        class:error={errors.ghi_chu}
        placeholder="Nhập ghi chú"
        rows="3"
      ></textarea>
      {#if errors.ghi_chu}
        <div class="error-text">{errors.ghi_chu}</div>
      {/if}
    </div>

    <div class="form-actions">
      <button type="submit" class="submit-button" disabled={isSubmitting}>
        {#if isSubmitting}
          <i class="fas fa-spinner fa-spin"></i> Đang xử lý...
        {:else}
          <i class="fas fa-plus"></i> Thêm nhà cung cấp
        {/if}
      </button>
    </div>
  </form>
</div>

<style>
  .add-supplier-form {
    max-width: 800px;
    margin: 0 auto;
    padding: 1.5rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  h2 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #1a237e;
    font-size: 1.5rem;
  }

  .form-group {
    margin-bottom: 1.25rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
  }

  .required {
    color: #f44336;
  }

  input, select, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s;
  }

  input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #1a237e;
    box-shadow: 0 0 0 2px rgba(26, 35, 126, 0.1);
  }

  input.error, select.error, textarea.error {
    border-color: #f44336;
  }

  .error-text {
    color: #f44336;
    font-size: 0.85rem;
    margin-top: 0.25rem;
  }

  .form-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: flex-end;
  }

  .submit-button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.3s;
  }

  .submit-button:hover:not(:disabled) {
    background-color: #45a049;
  }

  .submit-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  .error-message, .success-message {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .error-message {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
  }

  .success-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
  }
</style>
