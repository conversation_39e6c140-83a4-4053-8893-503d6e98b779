<script lang="ts">
  import { onMount } from 'svelte';
  import { bookings as bookingsStore } from '../../stores/userStore';
  import { Chart, registerables } from 'chart.js';
  import { onDestroy } from 'svelte';

  Chart.register(...registerables);

  interface DashboardStats {
    totalUsers: number;
    totalTours: number;
    totalBookings: number;
    totalRevenue: number;
  }

  interface Booking {
    ma_dat_tour: number;
    ma_tour: number;
    ma_nguoi_dung: number;
    ten_tour: string;
    ten_khach_hang: string;
    email?: string;
    so_dien_thoai?: string;
    dia_diem?: string;
    hinh_anh?: string;
    ngay_bat_dau?: string;
    ngay_ket_thuc?: string;
    ngay_dat: string;
    so_nguoi: number;
    gia: number;
    tong_tien?: number;
    trang_thai: string;
    ghi_chu?: string;
  }

  interface EntityCounts {
    tours: number;
    employees: number;
    users: number;
    hotels: number;
    bookings: number;
    reviews: number;
  }

  let pieChartCanvas: HTMLCanvasElement;
  let barChartCanvas: HTMLCanvasElement;

  let pieChart: Chart;
  let barChart: Chart;

  let stats: DashboardStats = {
    totalUsers: 0,
    totalTours: 0,
    totalBookings: 0,
    totalRevenue: 0
  };

  let recentBookings: Booking[] = [];

  let counts: EntityCounts = {
    tours: 0,
    employees: 0,
    users: 0,
    hotels: 0,
    bookings: 0,
    reviews: 0
  };

  let isLoading = true;
  let hasError = false;
  let errorMessage = '';

  function formatPrice(amount: number): string {
    if (amount === null || amount === undefined || isNaN(amount) || typeof amount !== 'number') {
      amount = 0;
    }
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' VNĐ';
  }

  function formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  function formatStatus(status: string): string {
    switch (status) {
      case 'cho_duyet':
        return 'Chờ duyệt';
      case 'da_xac_nhan':
        return 'Đã xác nhận';
      case 'chua_thanh_toan':
        return 'Chưa thanh toán';
      case 'da_thanh_toan':
        return 'Đã thanh toán';
      case 'da_huy':
        return 'Đã hủy';
      default:
        return status;
    }
  }

  function getStatusClass(status: string): string {
    switch (status) {
      case 'cho_duyet':
        return 'status-pending';
      case 'da_xac_nhan':
        return 'status-confirmed';
      case 'chua_thanh_toan':
        return 'status-warning';
      case 'da_thanh_toan':
        return 'status-success';
      case 'da_huy':
        return 'status-danger';
      default:
        return 'status-default';
    }
  }

  async function checkBackendConnection() {
    try {
      const response = await fetch('http://localhost:5000/api/tours', { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      console.error('Backend server không khả dụng:', error);
      return false;
    }
  }

  async function fetchDashboardStats() {
    isLoading = true;
    hasError = false;
    errorMessage = '';

    const isBackendAvailable = await checkBackendConnection();

    if (!isBackendAvailable) {
      console.warn('Backend server không khả dụng, sử dụng dữ liệu mẫu');
      errorMessage = 'Không thể kết nối đến backend server. Vui lòng kiểm tra server đã được khởi động chưa.';
      hasError = true;
      isLoading = false;
      return;
    }

    try {
      console.log('Fetching dashboard stats from API...');
      const response = await fetch('http://localhost:5000/api/dashboard/stats');

      if (!response.ok) {
        console.error(`Error fetching dashboard stats: ${response.status}`);

        try {
          const errorData = await response.json();
          console.error('Error details:', errorData);
          errorMessage = `Lỗi API (${response.status}): ${errorData.message || 'Không có thông báo lỗi'}`;
        } catch (jsonError) {
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          errorMessage = `Lỗi API (${response.status}): ${errorText || 'Không có thông báo lỗi'}`;
        }

        hasError = true;
      } else {
        stats = await response.json();
        console.log('Dashboard stats loaded:', stats);
      }

      await Promise.all([fetchRecentBookings(), fetchEntityCounts()]);

      try {
        const toursResponse = await fetch('http://localhost:5000/api/tours');
        let tours = [];

        if (toursResponse.ok) {
          const toursData = await toursResponse.json();
          tours = toursData || [];
          console.log('Tours loaded for revenue calculation:', tours);
        }

        const bookingsResponse = await fetch('http://localhost:5000/api/bookings');
        if (bookingsResponse.ok) {
          const data = await bookingsResponse.json();
          const allBookings = data.bookings || [];

          const processedBookings = allBookings.map((booking: any) => {
            const tourInfo = tours.find(tour => tour.ma_tour === booking.ma_tour);

            if (tourInfo && tourInfo.gia) {
              console.log(`Found tour info for booking ${booking.ma_dat_tour}:`, tourInfo);
              booking.gia = tourInfo.gia;
            }

            if ((booking.tong_tien === undefined || booking.tong_tien === null || isNaN(booking.tong_tien)) &&
                booking.gia && booking.so_nguoi && !isNaN(booking.gia) && !isNaN(booking.so_nguoi)) {
              booking.tong_tien = booking.gia * booking.so_nguoi;
              console.log(`Calculated tong_tien for booking ${booking.ma_dat_tour}: ${booking.tong_tien}`);
            }
            return booking;
          });

          stats.totalRevenue = calculateTotalRevenue(processedBookings);
          console.log('Total revenue recalculated:', stats.totalRevenue);
        }
      } catch (bookingsError) {
        console.error('Error fetching all bookings for revenue calculation:', bookingsError);
      }

      isLoading = false;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      errorMessage = `Lỗi kết nối: ${error.message}`;
      hasError = true;
      isLoading = false;
    }
  }

  function calculateTotalRevenue(bookings: Booking[]): number {
    console.log('Calculating total revenue from bookings:', bookings);

    return bookings.reduce((total: number, booking: Booking) => {
      let bookingTotal = 0;

      if (booking.tong_tien !== undefined && booking.tong_tien !== null && !isNaN(booking.tong_tien)) {
        bookingTotal = booking.tong_tien;
        console.log(`Booking ${booking.ma_dat_tour}: Using tong_tien = ${bookingTotal}`);
      } else if (booking.gia && booking.so_nguoi && !isNaN(booking.gia) && !isNaN(booking.so_nguoi)) {
        bookingTotal = booking.gia * booking.so_nguoi;
        console.log(`Booking ${booking.ma_dat_tour}: Calculated from gia * so_nguoi = ${booking.gia} * ${booking.so_nguoi} = ${bookingTotal}`);
      } else {
        console.log(`Booking ${booking.ma_dat_tour}: No valid price data. gia=${booking.gia}, so_nguoi=${booking.so_nguoi}, tong_tien=${booking.tong_tien}`);
      }

      return total + bookingTotal;
    }, 0);
  }

  async function fetchRecentBookings() {
    try {
      let storeBookings = [];
      const unsubscribe = bookingsStore.subscribe(value => {
        storeBookings = value || [];
      });
      unsubscribe();

      console.log('Bookings from store:', storeBookings.length);

      if (storeBookings.length > 0) {
        recentBookings = [...storeBookings]
          .sort((a, b) => new Date(b.ngay_dat).getTime() - new Date(a.ngay_dat).getTime())
          .slice(0, 5);

        recentBookings = recentBookings.map(booking => {
          if ((booking.tong_tien === undefined || booking.tong_tien === null || isNaN(booking.tong_tien)) &&
              booking.gia && booking.so_nguoi && !isNaN(booking.gia) && !isNaN(booking.so_nguoi)) {
            booking.tong_tien = booking.gia * booking.so_nguoi;
          }
          return booking;
        });

        console.log('Recent bookings loaded from store:', recentBookings);
        return;
      }

      try {
        const response = await fetch('http://localhost:5000/api/dashboard/recent-bookings');

        if (response.ok) {
          const data = await response.json();
          recentBookings = data;
          console.log('Recent bookings loaded from dashboard API:', recentBookings);
          return;
        } else {
          console.error(`Error fetching recent bookings from dashboard API: ${response.status}`);
        }
      } catch (dashboardError) {
        console.error('Error with dashboard recent bookings endpoint:', dashboardError);
      }

      try {
        const toursResponse = await fetch('http://localhost:5000/api/tours');
        let tours = [];

        if (toursResponse.ok) {
          const toursData = await toursResponse.json();
          tours = toursData || [];
          console.log('Tours loaded for price information:', tours);
        }

        const response = await fetch('http://localhost:5000/api/bookings');

        if (response.ok) {
          const data = await response.json();
          recentBookings = (data.bookings || [])
            .sort((a: Booking, b: Booking) => new Date(b.ngay_dat).getTime() - new Date(a.ngay_dat).getTime())
            .slice(0, 5);

          recentBookings = recentBookings.map(booking => {
            const tourInfo = tours.find(tour => tour.ma_tour === booking.ma_tour);

            if (tourInfo && tourInfo.gia) {
              booking.gia = tourInfo.gia;
            }

            if ((booking.tong_tien === undefined || booking.tong_tien === null || isNaN(booking.tong_tien)) &&
                booking.gia && booking.so_nguoi && !isNaN(booking.gia) && !isNaN(booking.so_nguoi)) {
              booking.tong_tien = booking.gia * booking.so_nguoi;
            }

            return booking;
          });

          bookingsStore.set(recentBookings);

          console.log('Recent bookings loaded from regular bookings API:', recentBookings);
        } else {
          console.error(`Error fetching bookings from regular API: ${response.status}`);
          recentBookings = [];
        }
      } catch (bookingsError) {
        console.error('Error with regular bookings endpoint:', bookingsError);
        recentBookings = [];
      }
    } catch (error) {
      console.error('Error in fetchRecentBookings function:', error);
      recentBookings = [];
    }
  }

  async function fetchEntityCounts() {
    try {
      const response = await fetch('http://localhost:5000/api/dashboard/counts');

      if (!response.ok) {
        console.error(`Error fetching entity counts: ${response.status}`);
        return;
      }

      const data = await response.json();
      if (data && data.counts) {
        counts = data.counts;
        console.log('Entity counts loaded:', counts);
      }
    } catch (error) {
      console.error('Error fetching entity counts:', error);
    }
  }

  function createPieChart() {
    if (pieChart) {
      pieChart.destroy();
    }

    if (!pieChartCanvas) return;

    const ctx = pieChartCanvas.getContext('2d');
    if (!ctx) return;

    const data = {
      labels: [
        'Tours',
        'Nhân viên',
        'Người dùng',
        'Khách sạn',
        'Đặt tour',
        'Đánh giá'
      ],
      datasets: [{
        data: [
          counts.tours,
          counts.employees,
          counts.users,
          counts.hotels,
          counts.bookings,
          counts.reviews
        ],
        backgroundColor: [
          '#4CAF50', 
          '#2196F3', 
          '#FFC107', 
          '#9C27B0', 
          '#FF5722', 
          '#E91E63'  
        ],
        borderWidth: 1
      }]
    };

    pieChart = new Chart(ctx, {
      type: 'pie',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right',
            labels: {
              font: {
                size: 14
              }
            }
          },
          title: {
            display: true,
            text: 'Phân bố dữ liệu hệ thống',
            font: {
              size: 18
            }
          }
        }
      }
    });
  }

  // Function to create bar chart for booking status distribution
  function createBarChart(bookings: Booking[]) {
    if (barChart) {
      barChart.destroy();
    }

    if (!barChartCanvas) return;

    const ctx = barChartCanvas.getContext('2d');
    if (!ctx) return;

    // Count bookings by status
    const statusCounts = {
      'cho_duyet': 0,
      'da_xac_nhan': 0,
      'chua_thanh_toan': 0,
      'da_thanh_toan': 0,
      'da_huy': 0
    };

    bookings.forEach(booking => {
      if (booking.trang_thai in statusCounts) {
        statusCounts[booking.trang_thai]++;
      }
    });

    const data = {
      labels: [
        'Chờ duyệt',
        'Đã xác nhận',
        'Chưa thanh toán',
        'Đã thanh toán',
        'Đã hủy'
      ],
      datasets: [{
        label: 'Số lượng đặt tour',
        data: [
          statusCounts.cho_duyet,
          statusCounts.da_xac_nhan,
          statusCounts.chua_thanh_toan,
          statusCounts.da_thanh_toan,
          statusCounts.da_huy
        ],
        backgroundColor: [
          '#FFC107', // Chờ duyệt - Yellow
          '#2196F3', // Đã xác nhận - Blue
          '#FF9800', // Chưa thanh toán - Orange
          '#4CAF50', // Đã thanh toán - Green
          '#F44336'  // Đã hủy - Red
        ],
        borderWidth: 1
      }]
    };

    barChart = new Chart(ctx, {
      type: 'bar',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: 'Thống kê trạng thái đặt tour',
            font: {
              size: 18
            }
          }
        }
      }
    });
  }

  // Function to update charts
  function updateCharts() {
    createPieChart();

    // Fetch all bookings for bar chart
    fetch('http://localhost:5000/api/bookings')
      .then(response => response.json())
      .then(data => {
        const allBookings = data.bookings || [];
        createBarChart(allBookings);
      })
      .catch(error => {
        console.error('Error fetching bookings for chart:', error);
        // If API fails, use recent bookings
        createBarChart(recentBookings);
      });
  }

  // Load data when component mounts and set up event listener for booking status changes
  onMount(() => {
    fetchDashboardStats().then(() => {
      // Create charts after data is loaded
      updateCharts();
    });

    // Subscribe to bookingsStore to update when bookings change
    const unsubscribeBookings = bookingsStore.subscribe(updatedBookings => {
      console.log('DashboardManager: Detected bookings store update, refreshing data...');
      if (updatedBookings && updatedBookings.length > 0) {
        // Update recent bookings from store
        recentBookings = [...updatedBookings]
          .sort((a, b) => new Date(b.ngay_dat).getTime() - new Date(a.ngay_dat).getTime())
          .slice(0, 5);

        // Recalculate dashboard stats
        stats.totalRevenue = calculateTotalRevenue(recentBookings);

        // Update charts
        updateCharts();
      }
    });

    // Add event listener for booking status changes
    const handleBookingStatusChanged = (event: CustomEvent<{bookingId: number, newStatus: string}>) => {
      console.log('DashboardManager: Detected booking status change event, refreshing data...', event.detail);
      fetchDashboardStats().then(() => {
        updateCharts();
      });
    };

    document.addEventListener('bookingStatusChanged', handleBookingStatusChanged);

    // Clean up event listener and subscription when component is destroyed
    return () => {
      document.removeEventListener('bookingStatusChanged', handleBookingStatusChanged);
      unsubscribeBookings();

      // Destroy charts
      if (pieChart) pieChart.destroy();
      if (barChart) barChart.destroy();
    };
  });
</script>

<div class="dashboard-container">
  <div class="content-header">
    <h2><i class="fas fa-tachometer-alt"></i> Bảng điều khiển</h2>
  </div>

  {#if isLoading}
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Đang tải dữ liệu thống kê...</p>
    </div>
  {:else if hasError}
    <div class="error-container">
      <p class="error-message">
        <i class="fas fa-exclamation-triangle"></i> {errorMessage}
      </p>
      <div class="error-details">
        <p>Vui lòng kiểm tra:</p>
        <ul>
          <li>Backend server đang chạy ở <code>http://localhost:5000</code></li>
          <li>API endpoint <code>/api/dashboard/stats</code> đã được đăng ký trong server.js</li>
          <li>Cấu trúc database đúng với các bảng: nguoi_dung, tour_du_lich, dat_tour</li>
        </ul>
      </div>
      <button class="btn-retry" on:click={fetchDashboardStats}>
        <i class="fas fa-sync"></i> Thử lại
      </button>
    </div>
  {:else}
    <!-- Main Dashboard Stats -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <i class="fas fa-users"></i>
        <div class="stat-info">
          <h3>Người dùng</h3>
          <p>{stats.totalUsers}</p>
        </div>
      </div>
      <div class="stat-card">
        <i class="fas fa-map-marked-alt"></i>
        <div class="stat-info">
          <h3>Tours</h3>
          <p>{stats.totalTours}</p>
        </div>
      </div>
      <div class="stat-card">
        <i class="fas fa-calendar-check"></i>
        <div class="stat-info">
          <h3>Đặt tour</h3>
          <p>{stats.totalBookings}</p>
        </div>
      </div>
      <div class="stat-card">
        <i class="fas fa-money-bill-wave"></i>
        <div class="stat-info">
          <h3>Doanh thu</h3>
          <p>{formatPrice(stats.totalRevenue)}</p>
        </div>
      </div>
    </div>

    <!-- Entity Counts Section -->
    <div class="entity-counts-section">
      <h3><i class="fas fa-database"></i> Thống kê hệ thống</h3>
      <div class="entity-counts-grid">
        <div class="entity-count-item">
          <i class="fas fa-map-marked-alt"></i>
          <div class="entity-count-info">
            <h4>Tours</h4>
            <p>{counts.tours}</p>
          </div>
        </div>
        <div class="entity-count-item">
          <i class="fas fa-user-tie"></i>
          <div class="entity-count-info">
            <h4>Nhân viên</h4>
            <p>{counts.employees}</p>
          </div>
        </div>
        <div class="entity-count-item">
          <i class="fas fa-users"></i>
          <div class="entity-count-info">
            <h4>Người dùng</h4>
            <p>{counts.users}</p>
          </div>
        </div>
        <div class="entity-count-item">
          <i class="fas fa-hotel"></i>
          <div class="entity-count-info">
            <h4>Khách sạn</h4>
            <p>{counts.hotels}</p>
          </div>
        </div>
        <div class="entity-count-item">
          <i class="fas fa-calendar-check"></i>
          <div class="entity-count-info">
            <h4>Đặt tour</h4>
            <p>{counts.bookings}</p>
          </div>
        </div>
        <div class="entity-count-item">
          <i class="fas fa-star"></i>
          <div class="entity-count-info">
            <h4>Đánh giá</h4>
            <p>{counts.reviews}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <h3><i class="fas fa-chart-pie"></i> Biểu đồ thống kê</h3>

      <div class="charts-container">
        <!-- Pie Chart -->
        <div class="chart-card">
          <div class="chart-container">
            <canvas bind:this={pieChartCanvas}></canvas>
          </div>
        </div>

        <!-- Bar Chart -->
        <div class="chart-card">
          <div class="chart-container">
            <canvas bind:this={barChartCanvas}></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Bookings Section -->
    <div class="recent-bookings-section">
      <h3><i class="fas fa-calendar-alt"></i> Đặt tour gần đây</h3>

      {#if recentBookings.length === 0}
        <p class="placeholder-text">Chưa có dữ liệu đặt tour gần đây</p>
      {:else}
        <div class="table-container">
          <table class="bookings-table">
            <thead>
              <tr>
                <th>Mã đặt</th>
                <th>Khách hàng</th>
                <th>Email</th>
                <th>Tour</th>
                <th>Ngày đặt</th>
                <th>Số người</th>
                <th>Tổng tiền</th>
                <th>Trạng thái</th>
              </tr>
            </thead>
            <tbody>
              {#each recentBookings as booking}
                <tr>
                  <td>#{booking.ma_dat_tour}</td>
                  <td>{booking.ten_khach_hang || 'N/A'}</td>
                  <td>{booking.email || 'N/A'}</td>
                  <td>{booking.ten_tour || 'N/A'}</td>
                  <td>{formatDate(booking.ngay_dat)}</td>
                  <td>{booking.so_nguoi || 0}</td>
                  <td class="price">
                    {#if booking.tong_tien !== undefined && booking.tong_tien !== null && !isNaN(booking.tong_tien)}
                      {formatPrice(booking.tong_tien)}
                    {:else if booking.gia && booking.so_nguoi && !isNaN(booking.gia) && !isNaN(booking.so_nguoi)}
                      {formatPrice(booking.gia * booking.so_nguoi)}
                    {:else}
                      {formatPrice(0)}
                    {/if}
                  </td>
                  <td>
                    <span class="status-badge {getStatusClass(booking.trang_thai)}">
                      {formatStatus(booking.trang_thai)}
                    </span>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .dashboard-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  .content-header h2 {
    font-size: 26px;
    color: #343a40;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
  }

  /* Loading and Error Styles */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-container {
    background-color: #fff3f3;
    border-left: 4px solid #dc3545;
    padding: 20px;
    margin-bottom: 30px;
    border-radius: 4px;
  }

  .error-message {
    color: #dc3545;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
  }

  .error-details {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
  }

  .error-details code {
    background-color: #e9ecef;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: monospace;
  }

  .btn-retry {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
  }

  .btn-retry:hover {
    background-color: #5a6268;
  }

  /* Dashboard Stats Styles */
  .dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }

  .stat-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
  }

  .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }

  .stat-card i {
    font-size: 2.5rem;
    margin-right: 20px;
    color: #007bff;
  }

  .stat-card:nth-child(2) i {
    color: #28a745;
  }

  .stat-card:nth-child(3) i {
    color: #fd7e14;
  }

  .stat-card:nth-child(4) i {
    color: #6f42c1;
  }

  .stat-info h3 {
    font-size: 16px;
    color: #6c757d;
    margin: 0 0 5px 0;
  }

  .stat-info p {
    font-size: 24px;
    font-weight: 600;
    color: #343a40;
    margin: 0;
  }

  /* Entity Counts Section Styles */
  .entity-counts-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }

  .entity-counts-section h3 {
    font-size: 20px;
    color: #343a40;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .entity-counts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
  }

  .entity-count-item {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
  }

  .entity-count-item:hover {
    background-color: #e9ecef;
  }

  .entity-count-item i {
    font-size: 1.8rem;
    margin-right: 15px;
    color: #6c757d;
  }

  .entity-count-info h4 {
    font-size: 14px;
    color: #6c757d;
    margin: 0 0 3px 0;
  }

  .entity-count-info p {
    font-size: 18px;
    font-weight: 600;
    color: #343a40;
    margin: 0;
  }

  /* Recent Bookings Section Styles */
  .recent-bookings-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }

  .recent-bookings-section h3 {
    font-size: 20px;
    color: #343a40;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .placeholder-text {
    color: #6c757d;
    text-align: center;
    padding: 30px;
    background-color: #f8f9fa;
    border-radius: 6px;
    font-style: italic;
  }

  .table-container {
    overflow-x: auto;
    margin-top: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .bookings-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    font-size: 14px;
    table-layout: fixed;
  }

  .bookings-table th,
  .bookings-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .bookings-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
  }

  .bookings-table tr:last-child td {
    border-bottom: none;
  }

  .bookings-table tr:hover {
    background-color: #f8f9fa;
  }

  /* Column widths */
  .bookings-table th:nth-child(1),
  .bookings-table td:nth-child(1) {
    width: 80px;
  }

  .bookings-table th:nth-child(2),
  .bookings-table td:nth-child(2) {
    width: 150px;
  }

  .bookings-table th:nth-child(3),
  .bookings-table td:nth-child(3) {
    width: 180px;
  }

  .bookings-table th:nth-child(4),
  .bookings-table td:nth-child(4) {
    width: 180px;
  }

  .bookings-table th:nth-child(5),
  .bookings-table td:nth-child(5) {
    width: 100px;
  }

  .bookings-table th:nth-child(6),
  .bookings-table td:nth-child(6) {
    width: 80px;
    text-align: center;
  }

  .bookings-table th:nth-child(7),
  .bookings-table td:nth-child(7) {
    width: 180px;
    text-align: right;
    padding-right: 20px;
  }

  .bookings-table .price {
    font-weight: 600;
    color: #28a745;
    min-width: 150px;
    display: inline-block;
  }

  .status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
  }

  .status-success {
    background-color: #d4edda;
    color: #155724;
  }

  .status-warning {
    background-color: #fff3cd;
    color: #856404;
  }

  .status-danger {
    background-color: #f8d7da;
    color: #721c24;
  }

  .status-pending {
    background-color: #fff3cd;
    color: #856404;
  }

  .status-confirmed {
    background-color: #d1ecf1;
    color: #0c5460;
  }

  .status-default {
    background-color: #e9ecef;
    color: #495057;
  }

  /* Charts Section Styles */
  .charts-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }

  .charts-section h3 {
    font-size: 20px;
    color: #343a40;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 20px;
  }

  .chart-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
  }

  .chart-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }

  .chart-container {
    position: relative;
    height: 300px;
    width: 100%;
  }

  @media (max-width: 992px) {
    .charts-container {
      grid-template-columns: 1fr;
    }
  }
</style>