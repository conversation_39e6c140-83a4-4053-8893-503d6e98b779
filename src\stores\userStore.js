import { writable } from 'svelte/store';

// User store for authentication state
export const user = writable(null);

// Bookings store for tour bookings
export const bookings = writable([]);

// Hotel bookings store for hotel reservations
export const hotelBookings = writable([]);

// Helper function to get user from localStorage on app initialization
export function initializeUserStore() {
  if (typeof window !== 'undefined') {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        user.set(userData);
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        localStorage.removeItem('user');
      }
    }
  }
}

// Helper function to save user to localStorage
export function saveUserToStorage(userData) {
  if (typeof window !== 'undefined') {
    if (userData) {
      localStorage.setItem('user', JSON.stringify(userData));
    } else {
      localStorage.removeItem('user');
    }
  }
}

// Subscribe to user changes and save to localStorage
if (typeof window !== 'undefined') {
  user.subscribe(userData => {
    saveUserToStorage(userData);
  });
}

// Helper functions for managing bookings
export function addBooking(booking) {
  bookings.update(currentBookings => [...currentBookings, booking]);
}

export function updateBooking(updatedBooking) {
  bookings.update(currentBookings => 
    currentBookings.map(booking => 
      booking.ma_dat_tour === updatedBooking.ma_dat_tour ? updatedBooking : booking
    )
  );
}

export function removeBooking(bookingId) {
  bookings.update(currentBookings => 
    currentBookings.filter(booking => booking.ma_dat_tour !== bookingId)
  );
}

// Helper functions for managing hotel bookings
export function addHotelBooking(hotelBooking) {
  hotelBookings.update(currentBookings => [...currentBookings, hotelBooking]);
}

export function updateHotelBooking(updatedBooking) {
  hotelBookings.update(currentBookings => 
    currentBookings.map(booking => 
      (booking.id === updatedBooking.id || booking.ma_dat_phong === updatedBooking.ma_dat_phong) 
        ? updatedBooking 
        : booking
    )
  );
}

export function removeHotelBooking(bookingId) {
  hotelBookings.update(currentBookings => 
    currentBookings.filter(booking => 
      booking.id !== bookingId && booking.ma_dat_phong !== bookingId
    )
  );
}

// Helper function to clear all stores (for logout)
export function clearAllStores() {
  user.set(null);
  bookings.set([]);
  hotelBookings.set([]);
  if (typeof window !== 'undefined') {
    localStorage.removeItem('user');
  }
}
