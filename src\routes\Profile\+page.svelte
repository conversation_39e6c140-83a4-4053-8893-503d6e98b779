<script lang="ts">
  import { onMount } from 'svelte';
  import { user, bookings, hotelBookings } from '../../stores/userStore';
  import { goto } from '$app/navigation';
  import Navbar from '../../components/Navbar.svelte';
  import Footer from '../../components/Footer.svelte';

  let activeTab = 'bookings';
  let isLoading = false;
  let error = '';

  $: tourBookingsCount = $bookings.length;
  $: hotelBookingsCount = $hotelBookings.length;
  $: totalBookingsCount = tourBookingsCount + hotelBookingsCount;

  onMount(() => {
    if (!$user) {
      goto('/login');
      return;
    }

    loadUserBookings();

    const checkPaymentStatus = async () => {
      try {
        console.log('Profile: Checking payment status from API...');
        const response = await fetch('http://localhost:5000/api/payments');

        if (response.ok) {
          const data = await response.json();
          const payments = data.payments || [];

          console.log('Profile: Loaded payments from API:', payments);

          const completedPayments = payments.filter((payment: any) =>
            payment.trang_thai_thanh_toan === 'hoan_tat' && payment.ma_dat_tour
          );

          console.log('Profile: Found completed payments:', completedPayments);

          for (const payment of completedPayments) {
            const booking = $bookings.find((b: any) => b.ma_dat_tour === payment.ma_dat_tour);

            if (booking && booking.trang_thai !== 'da_thanh_toan') {
              console.log(`Profile: Found booking ${payment.ma_dat_tour} with completed payment, updating status...`);
              await updateBookingStatus(payment.ma_dat_tour, 'da_thanh_toan');
            }
          }
        }
      } catch (err) {
        console.error('Profile: Error checking payment status:', err);
      }
    };

    setTimeout(checkPaymentStatus, 1000);

    const handleBookingStatusChanged = async (event: CustomEvent) => {
      console.log('Profile: Detected booking status change event:', event);
      console.log('Profile: Event detail:', event.detail);

      if (event.detail) {
        const { bookingId, bookingType, newStatus } = event.detail;

        console.log('Profile: Processing event with details:', { bookingId, bookingType, newStatus });

        if (bookingType === 'tour' && bookingId) {
          await updateBookingStatus(bookingId, newStatus);
        } else if (bookingType === 'hotel' && bookingId) {
          hotelBookings.update(currentBookings => {
            console.log(`Profile: Attempting to update hotel booking ${bookingId} to status ${newStatus}`);

            const updatedBookings = currentBookings.map((booking: any) => {
              if (booking.id === bookingId) {
                console.log(`Profile: Updating hotel booking ${bookingId} status from ${booking.status} to ${newStatus}`);
                return { ...booking, status: newStatus };
              }
              return booking;
            });

            return updatedBookings;
          });
        }
      } else {
        console.log('Profile: Event without detail, reloading bookings');
      }

      loadUserBookings();
    };

    document.addEventListener('bookingStatusChanged', handleBookingStatusChanged);

    return () => {
      document.removeEventListener('bookingStatusChanged', handleBookingStatusChanged);
    };
  });

  bookings.subscribe(currentBookings => {
    console.log('Profile: Bookings store updated, new length:', currentBookings.length);

    const paidBookings = currentBookings.filter((booking: any) => booking.trang_thai === 'da_thanh_toan');
    console.log('Profile: Number of paid bookings:', paidBookings.length);
  });

  hotelBookings.subscribe(currentBookings => {
    console.log('Profile: Hotel bookings store updated, new length:', currentBookings.length);

    const paidHotelBookings = currentBookings.filter((booking: any) =>
      booking.status === 'confirmed' || booking.status === 'completed'
    );
    console.log('Profile: Number of paid hotel bookings:', paidHotelBookings.length);
  });

  async function loadUserBookings() {
    if (!$user) return;

    isLoading = true;
    error = '';

    try {
      const response = await fetch(`http://localhost:5000/api/bookings/user/${$user.ma_nguoi_dung}`);

      if (!response.ok) {
        throw new Error('Không thể tải danh sách đặt tour');
      }

      const data = await response.json();

      const bookingsWithUserInfo = data.bookings.map((booking: any) => ({
        ...booking,
        ten_khach_hang: booking.ten_khach_hang || $user.ho_ten || '',
        email: booking.email || $user.email || '',
        so_dien_thoai: booking.so_dien_thoai || $user.so_dien_thoai || ''
      }));

      console.log('Profile: Loaded bookings from API:', bookingsWithUserInfo);

      const paidBookings = bookingsWithUserInfo.filter((booking: any) => booking.trang_thai === 'da_thanh_toan');
      console.log('Profile: Number of paid bookings from API:', paidBookings.length);

      bookings.set(bookingsWithUserInfo);
    } catch (err) {
      console.error('Lỗi khi tải danh sách đặt tour:', err);
      error = 'Không thể tải danh sách đặt tour. Vui lòng thử lại sau.';
    } finally {
      isLoading = false;
    }
  }

  async function updateBookingStatus(bookingId: number, newStatus: string) {
    console.log(`Profile: Manually updating booking ${bookingId} to status ${newStatus}`);

    try {
      const booking = $bookings.find((b: any) => b.ma_dat_tour === bookingId);

      if (!booking) {
        console.error(`Profile: Booking ${bookingId} not found in store`);
        return;
      }

      try {
        console.log('Profile: Updating booking status in API...');

        const updatePayload = {
          ma_dat_tour: booking.ma_dat_tour,
          ma_tour: booking.ma_tour,
          ma_nguoi_dung: booking.ma_nguoi_dung,
          so_nguoi: booking.so_nguoi,
          trang_thai: newStatus,
          ghi_chu: booking.ghi_chu || null
        };

        console.log('Profile: Sending update with payload:', updatePayload);

        const updateResponse = await fetch(`http://localhost:5000/api/bookings/${bookingId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updatePayload)
        });

        if (!updateResponse.ok) {
          console.error('Profile: Failed to update booking status in API:', await updateResponse.text());
        } else {
          console.log('Profile: Successfully updated booking status in API');
        }
      } catch (updateError) {
        console.error('Profile: Error updating booking status in API:', updateError);
      }

      bookings.update(currentBookings => {
        return currentBookings.map((b: any) => {
          if (b.ma_dat_tour === bookingId) {
            console.log(`Profile: Updating booking ${bookingId} status from ${b.trang_thai} to ${newStatus}`);
            return { ...b, trang_thai: newStatus };
          }
          return b;
        });
      });

      console.log(`Profile: Successfully updated booking ${bookingId} status to ${newStatus} in store`);
    } catch (err) {
      console.error(`Profile: Error updating booking ${bookingId} status:`, err);
    }
  }

  function formatPrice(price: number): string {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' VNĐ';
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('vi-VN');
  }

  function getStatusText(status: string): string {
    switch (status) {
      case 'cho_duyet': return 'Chờ duyệt';
      case 'chua_thanh_toan': return 'Chờ thanh toán'; 
      case 'da_xac_nhan': return 'Đã xác nhận';
      case 'da_thanh_toan': return 'Đã thanh toán';
      case 'da_huy': return 'Đã hủy';
      default: return status;
    }
  }

  function getStatusClass(status: string): string {
    switch (status) {
      case 'cho_duyet': return 'pending';
      case 'chua_thanh_toan': return 'status-warning'; 
      case 'da_xac_nhan': return 'confirmed';
      case 'da_thanh_toan': return 'status-success';
      case 'da_huy': return 'cancelled';
      default: return '';
    }
  }

  async function deleteBooking(booking: any) {
    if (confirm('Bạn có chắc chắn muốn xóa tour này không?')) {
      try {
        const response = await fetch(`http://localhost:5000/api/bookings/${booking.ma_dat_tour}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          throw new Error('Không thể xóa đặt tour');
        }

        await loadUserBookings();

      } catch (err) {
        console.error('Lỗi khi xóa đặt tour:', err);
        error = 'Không thể xóa đặt tour. Vui lòng thử lại sau.';
      }
    }
  }

  function deleteHotelBooking(bookingToDelete: any) {
    if (confirm('Bạn có chắc chắn muốn xóa đặt phòng này không?')) {
      hotelBookings.update(currentBookings =>
        currentBookings.filter((booking: any) =>
          booking.bookingDate !== bookingToDelete.bookingDate ||
          booking.hotelDetails.ma_khach_san !== bookingToDelete.hotelDetails.ma_khach_san
        )
      );
    }
  }
</script>

<Navbar />

<div class="profile-container">
  {#if $user}
    <div class="profile-header">
      <div class="user-info">
        <div class="avatar">{$user.anh_dai_dien || ''}</div>
        <div class="user-details">
          <h2>{$user.name || 'Người dùng'}</h2>
          <p>{$user.email}</p>
        </div>
      </div>
      <div class="booking-stats">
        <div class="stat-card">
          <i class="fas fa-suitcase-rolling"></i>
          <div class="stat-info">
            <span class="stat-label">Tour đã đặt</span>
            <span class="stat-value">{tourBookingsCount}</span>
          </div>
        </div>
        <div class="stat-card">
          <i class="fas fa-hotel"></i>
          <div class="stat-info">
            <span class="stat-label">Khách sạn đã đặt</span>
            <span class="stat-value">{hotelBookingsCount}</span>
          </div>
        </div>
        <div class="stat-card">
          <i class="fas fa-calendar-check"></i>
          <div class="stat-info">
            <span class="stat-label">Tổng số đặt</span>
            <span class="stat-value">{totalBookingsCount}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="tabs">
      <button
        class:active={activeTab === 'bookings'}
        on:click={() => activeTab = 'bookings'}
      >
        Các tour đã đặt
      </button>
      <button
        class:active={activeTab === 'hotelBookings'}
        on:click={() => activeTab = 'hotelBookings'}
      >
        Khách sạn đã đặt
      </button>
      <button
        class:active={activeTab === 'info'}
        on:click={() => activeTab = 'info'}
      >
        Thông tin cá nhân
      </button>
    </div>

    {#if activeTab === 'bookings'}
      <div class="bookings-section">
        {#if isLoading}
          <div class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Đang tải dữ liệu...</p>
          </div>
        {:else if error}
          <div class="error-message">
            <i class="fas fa-exclamation-circle"></i>
            <p>{error}</p>
            <button on:click={loadUserBookings} class="retry-button">
              <i class="fas fa-redo"></i> Thử lại
            </button>
          </div>
        {:else if $bookings.length === 0}
          <div class="no-bookings">
            <i class="fas fa-suitcase-rolling"></i>
            <p>Bạn chưa đặt tour nào</p>
            <a href="/" class="browse-tours">Khám phá các tour</a>
          </div>
        {:else}
          <div class="bookings-grid">
            {#each $bookings as booking}
              <div class="booking-card">
                <img
                  src={`/images/${booking.hinh_anh}`}
                  alt={booking.ten_tour}
                  class="tour-image"
                  on:error={(e) => {
                    console.error('Lỗi tải hình ảnh:', booking.hinh_anh);
                    (e.target as HTMLImageElement).src = '#';
                  }}
                />
                <div class="booking-info">
                  <h3 title={booking.ten_tour}>{booking.ten_tour}</h3>
                  <div class="booking-details">
                    <p>
                      <i class="fas fa-map-marker-alt"></i>
                      {booking.dia_diem}
                    </p>
                    <p>
                      <i class="fas fa-calendar-alt"></i>
                      {formatDate(booking.ngay_bat_dau)} - {formatDate(booking.ngay_ket_thuc)}
                    </p>
                    <p>
                      <i class="fas fa-users"></i>
                      Số người: {booking.so_nguoi}
                    </p>
                    <p class="price-display">
                      <i class="fas fa-dollar-sign"></i>
                      <span class="price-value">{formatPrice(booking.gia)}</span>
                    </p>
                    <p>
                      <i class="fas fa-clock"></i>
                      Đã đặt ngày: {formatDate(booking.ngay_dat)}
                    </p>
                    {#if booking.ghi_chu}
                      <p>
                        <i class="fas fa-sticky-note"></i>
                        Ghi chú: {booking.ghi_chu}
                      </p>
                    {/if}
                  </div>
                  {#if booking.trang_thai === 'da_thanh_toan'}
                    <div class="booking-status status-success">
                      Đã thanh toán
                    </div>
                  {:else}
                    <div class="booking-status {getStatusClass(booking.trang_thai)}">
                      {getStatusText(booking.trang_thai)}
                    </div>
                    {#if booking.trang_thai === 'cho_duyet' || booking.trang_thai === 'chua_thanh_toan' || booking.trang_thai === 'da_xac_nhan'}
                      <div class="booking-status status-warning s-U5V2eDmFYI9i">
                        Chờ thanh toán
                      </div>
                      <a href={`/Payment?booking_id=${booking.ma_dat_tour}&type=tour`} class="payment-button">
                        <i class="fas fa-credit-card"></i> Thanh toán ngay
                      </a>
                    {/if}
                  {/if}
                  <button class="delete-button" on:click={() => deleteBooking(booking)}>
                    <i class="fas fa-trash"></i> Xóa tour
                  </button>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    {:else if activeTab === 'hotelBookings'}
      <div class="bookings-section">
        {#if $hotelBookings.length === 0}
          <div class="no-bookings">
            <i class="fas fa-hotel"></i>
            <p>Bạn chưa đặt phòng khách sạn nào</p>
            <a href="/hotels" class="browse-tours">Khám phá các khách sạn</a>
          </div>
        {:else}
          <div class="bookings-grid">
            {#each $hotelBookings as booking}
              <div class="booking-card">
                <img
                  src={`/images/${booking.hotelDetails.hinh_anh}`}
                  alt={booking.hotelDetails.ten_khach_san}
                  class="tour-image"
                  on:error={(e) => {
                    console.error('Lỗi tải hình ảnh khách sạn:', booking.hotelDetails.hinh_anh);
                    (e.target as HTMLImageElement).src = '#';
                  }}
                />
                <div class="booking-info">
                  <h3 title={booking.hotelDetails.ten_khach_san}>{booking.hotelDetails.ten_khach_san}</h3>
                  <div class="booking-details">
                    <p>
                      <i class="fas fa-map-marker-alt"></i>
                      {booking.hotelDetails.dia_diem}
                    </p>
                    <p>
                      <i class="fas fa-calendar-alt"></i>
                      {formatDate(booking.checkInDate)} - {formatDate(booking.checkOutDate)}
                    </p>
                    <p>
                      <i class="fas fa-user-friends"></i>
                      {booking.guests} người, {booking.rooms} phòng
                    </p>
                    <p class="price-display">
                      <i class="fas fa-dollar-sign"></i>
                      <span class="price-value">{formatPrice(booking.totalPrice)}</span>
                    </p>
                    <p>
                      <i class="fas fa-clock"></i>
                      Đã đặt ngày: {formatDate(booking.bookingDate)}
                    </p>
                  </div>
                  {#if booking.status === 'confirmed' || booking.status === 'completed'}
                    <div class="booking-status status-success">
                      Đã thanh toán
                    </div>
                  {:else}
                    <div class="booking-status {booking.status === 'pending' ? 'pending' : booking.status === 'awaiting_payment' ? 'awaiting_payment' : booking.status === 'confirmed' ? 'confirmed' : booking.status === 'cancelled' ? 'cancelled' : booking.status === 'completed' ? 'completed' : 'confirmed'}">
                      {booking.status === 'pending' ? 'Chờ xác nhận' : booking.status === 'awaiting_payment' ? 'Chờ thanh toán' : booking.status === 'confirmed' ? 'Đã xác nhận' : booking.status === 'cancelled' ? 'Đã hủy' : booking.status === 'completed' ? 'Hoàn thành' : 'Đã xác nhận'}
                    </div>
                    {#if booking.status === 'pending' || booking.status === 'awaiting_payment'}
                      <div class="booking-status status-warning s-U5V2eDmFYI9i">
                        Chờ thanh toán
                      </div>
                      <a href={`/Payment?booking_id=${booking.id}&type=hotel`} class="payment-button">
                        <i class="fas fa-credit-card"></i> Thanh toán ngay
                      </a>
                    {/if}
                  {/if}
                  <button class="delete-button" on:click={() => deleteHotelBooking(booking)}>
                    <i class="fas fa-trash"></i> Xóa đặt phòng
                  </button>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    {:else}
      <div class="personal-info">
        <div class="info-group">
          <label for="fullname">Họ và tên</label>
          <input id="fullname" type="text" value={$user.ho_ten || ''} readonly />
        </div>
        <div class="info-group">
          <label for="email">Email</label>
          <input id="email" type="email" value={$user.email || ''} readonly />
        </div>
        <div class="info-group">
          <label for="phone">Số điện thoại</label>
          <input id="phone" type="tel" value={$user.so_dien_thoai || ''} readonly />
        </div>
      </div>
    {/if}
  {/if}
</div>

<Footer />

<style>
  .profile-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
  }

  .profile-header {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-top: 60px;
    margin-bottom: 2rem;
  }

  .booking-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 12px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .stat-card i {
    font-size: 1.8rem;
    color: #4caf50;
  }

  .stat-card:nth-child(2) i {
    color: #2196f3;
  }

  .stat-card:nth-child(3) i {
    color: #ff9800;
  }

  .stat-info {
    display: flex;
    flex-direction: column;
  }

  .stat-label {
    font-size: 0.85rem;
    color: #6c757d;
  }

  .stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #343a40;
  }

  .avatar {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #42a5f5, #66bb6a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: bold;
  }

  .user-details h2 {
    margin: 0;
    color: #333;
  }

  .user-details p {
    margin: 0.5rem 0 0;
    color: #666;
  }

  .tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .tabs button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    background: #f5f5f5;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .tabs button:hover {
    background: #e0f7fa;
  }

  .tabs button.active {
    background: linear-gradient(135deg, #42a5f5, #66bb6a);
    color: white;
  }

  .bookings-section {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .loading, .error-message, .no-bookings {
    text-align: center;
    padding: 3rem;
    color: #666;
  }

  .loading i, .error-message i, .no-bookings i {
    font-size: 4rem;
    margin-bottom: 1rem;
  }

  .loading i {
    color: #42a5f5;
  }

  .error-message i {
    color: #f44336;
  }

  .no-bookings i {
    color: #ccc;
  }

  .retry-button {
    display: inline-block;
    margin-top: 1rem;
    padding: 0.75rem 1.5rem;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: transform 0.3s ease;
    font-size: 0.9rem;
  }

  .retry-button:hover {
    transform: translateY(-2px);
    background: #e53935;
  }

  .retry-button i {
    font-size: 0.9rem !important;
    margin-right: 0.5rem;
    margin-bottom: 0 !important;
  }

  .browse-tours {
    display: inline-block;
    margin-top: 1rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #42a5f5, #66bb6a);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: transform 0.3s ease;
  }

  .browse-tours:hover {
    transform: translateY(-2px);
  }

  .bookings-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .booking-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .booking-card:hover {
    transform: translateY(-4px);
  }

  .tour-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  .booking-info {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .booking-info h3 {
    margin: 0 0 1rem;
    color: #333;
    font-size: 1.2rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    height: 1.5rem;
    line-height: 1.5rem;
  }

  .booking-details {
    display: grid;
    gap: 0.5rem;
    flex: 1;
    margin-bottom: 1rem;
  }

  .booking-details p {
    margin: 0;
    color: #666;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .booking-details i {
    color: #42a5f5;
    width: 20px;
  }

  .price-display {
    display: flex;
    align-items: center;
  }

  .price-value {
    font-weight: 600;
    color: #28a745;
    min-width: 150px;
    display: inline-block;
  }

  .booking-status {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    text-align: center;
    font-weight: 500;
  }

  .booking-status.confirmed {
    background: #e8f5e9;
    color: #2e7d32;
  }

  .booking-status.pending {
    background: #fff8e1;
    color: #ff8f00;
  }

  .booking-status.awaiting_payment {
    background: #e6f7ff;
    color: #0c5460;
  }

  .booking-status.cancelled {
    background: #ffebee;
    color: #c62828;
  }

  .booking-status.completed {
    background: #e0e7ff;
    color: #3730a3;
  }

  .booking-status.status-warning {
    background: #fff3cd;
    color: #856404;
  }

  .s-U5V2eDmFYI9i {
    margin-top: 0.5rem;
  }

  .booking-status.status-success {
    background: #d4edda;
    color: #155724;
  }

  .delete-button {
    margin-top: 20px;
    padding: 0.75rem 1.5rem;
    background: #ff5252;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(255, 82, 82, 0.2);
    width: 100%;
  }

  .delete-button:hover {
    background: #ff1744;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 82, 82, 0.3);
  }

  .delete-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(255, 82, 82, 0.2);
  }

  .delete-button i {
    font-size: 1rem;
    transition: transform 0.3s ease;
  }

  .delete-button:hover i {
    transform: rotate(12deg);
  }

  .payment-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #42a5f5, #66bb6a);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.95rem;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(66, 165, 245, 0.2);
    width: 100%;
    text-align: center;
  }

  .payment-button:hover {
    background: linear-gradient(135deg, #1e88e5, #43a047);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(66, 165, 245, 0.3);
  }

  .payment-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(66, 165, 245, 0.2);
  }

  .payment-button i {
    font-size: 1rem;
  }

  .personal-info {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .info-group {
    margin-bottom: 1.5rem;
  }

  .info-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #666;
    font-size: 0.9rem;
  }

  .info-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    color: #333;
    background: #f9f9f9;
  }
</style>