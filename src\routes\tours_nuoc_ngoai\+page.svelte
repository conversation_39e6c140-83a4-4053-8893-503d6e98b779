<script>
    import { onMount } from 'svelte';
    import Navbar from '../../components/Navbar.svelte';
    import Banner from '../../components/Banner.svelte';
    import ToursNuocNgoai from '../../components/tours_nuoc_ngoai.svelte';
    import CTA from '../../components/CTA.svelte';
    import Footer from '../../components/Footer.svelte';
    let tours = [];
  
    let searchName = '';
    let searchLocation = '';
    let searchPriceRange = '';
    let searchStartDate = '';
    let searchEndDate = '';
  
    let tourListRef;
    const scrollAmount = 320 * 3;
  
    const priceOptions = {
      'under1': (gia) => gia < 1000000,
      '1to5': (gia) => gia >= 1000000 && gia <= 5000000,
      '5to10': (gia) => gia > 5000000 && gia <= 10000000,
      'above10': (gia) => gia > 10000000,
    };
  
    onMount(async () => {
      try {
        const response = await fetch('http://localhost:5000/api/tours/domestic');
        if (!response.ok) {
          throw new Error('Không thể tải danh sách tour trong nước');
        }
        tours = await response.json();
      } catch (error) {
        console.error('Lỗi khi tải tour trong nước:', error);
      }
    });
  
    $: filteredTours = tours?.filter((tour) => {
      const matchName = searchName
        ? tour.ten_tour.toLowerCase().includes(searchName.toLowerCase())
        : true;
  
      const matchLocation = searchLocation
        ? tour.dia_diem.toLowerCase().includes(searchLocation.toLowerCase())
        : true;
  
      const matchPrice =
        searchPriceRange && priceOptions[searchPriceRange]
          ? priceOptions[searchPriceRange](Number(tour.gia))
          : true;
  
      const matchStartDate = searchStartDate
        ? new Date(tour.ngay_bat_dau) >= new Date(searchStartDate)
        : true;
  
      const matchEndDate = searchEndDate
        ? new Date(tour.ngay_ket_thuc) <= new Date(searchEndDate)
        : true;
  
      return matchName && matchLocation && matchPrice && matchStartDate && matchEndDate;
    }) ?? [];
  
    const formatDate = (dateStr) => new Date(dateStr).toLocaleDateString('vi-VN');
  </script>
  <Navbar />
  <Banner />
  <ToursNuocNgoai />
  <CTA />
  <Footer />
   