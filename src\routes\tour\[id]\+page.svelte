<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { user, bookings } from '../../../stores/userStore';
	import { goto } from '$app/navigation';
	import { fade } from 'svelte/transition';
	import Navbar from '../../../components/Navbar.svelte';
	import Footer from '../../../components/Footer.svelte';
	import CTA from '../../../components/CTA.svelte';

	let tourId = $page.params.id;
	let tour: any = null;
	let isLoading = true;
	let error: string | null = null;
	let showBookingSuccess = false;
	let relatedTours: any[] = [];
	let suggestedTours: any[] = [];
	let isLoadingSuggestions = false;

	let numberOfPeople = 1;
	let notes = '';
	let bookingStatus: 'idle' | 'loading' | 'success' | 'error' = 'idle';
	let bookingError = '';

	let ratings: any[] = [];
	let isLoadingRatings = false;
	let ratingError: string | null = null;
	let averageRating = 0;
	let totalRatings = 0;
	let userRating: any = null;

	let ratingValue = 5;
	let ratingComment = '';
	let ratingStatus: 'idle' | 'loading' | 'success' | 'error' = 'idle';
	let ratingFormError = '';
	let showRatingForm = false;

	function formatPrice(price: number | string): string {
		if (price === null || price === undefined) return 'N/A';
		return Number(price).toLocaleString('vi-VN') + ' VNĐ';
	}

	function formatDate(dateString: string): string {
		if (!dateString) return 'N/A';
		try {
			const date = new Date(dateString);
			if (isNaN(date.getTime())) {
				return 'Ngày không hợp lệ';
			}
			return date.toLocaleDateString('vi-VN', { day: 'numeric', month: 'long', year: 'numeric' });
		} catch (e) {
			console.error('Error formatting date:', dateString, e);
			return 'Ngày không hợp lệ';
		}
	}

	function formatImageUrl(imageUrl: string): string {
		if (!imageUrl) return '/images/default-tour.svg';
		return `/images/${imageUrl}`;
	}

	function calculateDays(startDate: string, endDate: string): number {
		if (!startDate || !endDate) return 0;
		try {
			const start = new Date(startDate);
			const end = new Date(endDate);

			if (isNaN(start.getTime()) || isNaN(end.getTime())) {
				return 0;
			}

			const diffTime = Math.abs(end.getTime() - start.getTime());
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

			return Math.max(1, diffDays);
		} catch (e) {
			console.error('Error calculating days:', e);
			return 0;
		}
	}

	async function handleBooking() {
		if (!$user) {
			goto('/login');
			return;
		}

		if (numberOfPeople < 1) {
			bookingError = 'Vui lòng chọn ít nhất 1 người.';
			bookingStatus = 'error';
			return;
		}
		if (tour && numberOfPeople > tour.so_cho_trong) {
			bookingError = `Số lượng người vượt quá số chỗ còn trống (${tour.so_cho_trong}).`;
			bookingStatus = 'error';
			return;
		}

		bookingStatus = 'loading';
		bookingError = '';

		try {
			const bookingData = {
				ma_nguoi_dung: $user.ma_nguoi_dung,
				ma_tour: parseInt(tourId),
				so_nguoi: numberOfPeople,
				ghi_chu: notes || null,
				ten_khach_hang: $user.ho_ten || '',
				email: $user.email || '',
				so_dien_thoai: $user.so_dien_thoai || ''
			};

			const response = await fetch('http://localhost:5000/api/bookings', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(bookingData)
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Không thể đặt tour. Vui lòng thử lại.');
			}

			await response.json();


			bookingStatus = 'success';
			showBookingSuccess = true;

			setTimeout(() => {
				showBookingSuccess = false;
				goto('/Profile');
			}, 2500);

		} catch (err: any) {
			console.error('Lỗi khi đặt tour:', err);
			bookingStatus = 'error';
			bookingError = err.message || 'Có lỗi xảy ra khi đặt tour. Vui lòng thử lại sau.';
		}
	}



	async function fetchTourRatings() {
		isLoadingRatings = true;
		ratingError = null;
		try {
			const response = await fetch(`http://localhost:5000/api/ratings/tour/${tourId}`);

			if (!response.ok) {
				if (response.status === 404) {
					ratings = [];
					averageRating = 0;
					totalRatings = 0;
					return;
				}
				throw new Error(`Lỗi ${response.status}: Không thể tải đánh giá.`);
			}

			const data = await response.json();
			ratings = data.ratings || [];
			totalRatings = ratings.length;

			if (totalRatings > 0) {
				const sum = ratings.reduce((acc: number, rating: any) => acc + rating.xep_hang, 0);
				averageRating = sum / totalRatings;
			} else {
				averageRating = 0;
			}

			if ($user) {
				userRating = ratings.find((rating: any) => rating.ma_nguoi_dung === $user.ma_nguoi_dung);

				if (userRating) {
					ratingValue = userRating.xep_hang;
					ratingComment = userRating.binh_luan || '';
				}
			}
		} catch (err: any) {
			console.error('Lỗi khi tải đánh giá:', err);
			ratingError = err.message;
		} finally {
			isLoadingRatings = false;
		}
	}

	async function submitRating() {
		if (!$user) {
			goto('/login');
			return;
		}

		if (ratingValue < 1 || ratingValue > 5) {
			ratingFormError = 'Xếp hạng phải từ 1 đến 5 sao.';
			ratingStatus = 'error';
			return;
		}

		ratingStatus = 'loading';
		ratingFormError = '';

		try {
			const ratingData = {
				ma_nguoi_dung: $user.ma_nguoi_dung,
				ma_tour: parseInt(tourId),
				xep_hang: ratingValue,
				binh_luan: ratingComment || null
			};

			let url = 'http://localhost:5000/api/ratings';
			let method = 'POST';

			if (userRating) {
				url = `http://localhost:5000/api/ratings/${userRating.ma_danh_gia}`;
				method = 'PUT';
			}

			const response = await fetch(url, {
				method: method,
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(ratingData)
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Không thể gửi đánh giá. Vui lòng thử lại.');
			}

			await response.json();

			await fetchTourRatings();

			ratingStatus = 'success';
			showRatingForm = false;

			alert(userRating ? 'Cập nhật đánh giá thành công!' : 'Gửi đánh giá thành công!');

		} catch (err: any) {
			console.error('Lỗi khi gửi đánh giá:', err);
			ratingStatus = 'error';
			ratingFormError = err.message || 'Có lỗi xảy ra khi gửi đánh giá. Vui lòng thử lại sau.';
		}
	}

	function formatReviewDate(dateString: string): string {
		if (!dateString) return 'N/A';
		try {
			const date = new Date(dateString);
			if (isNaN(date.getTime())) {
				return 'Ngày không hợp lệ';
			}

			const monthNames = [
				'tháng 1', 'tháng 2', 'tháng 3', 'tháng 4', 'tháng 5', 'tháng 6',
				'tháng 7', 'tháng 8', 'tháng 9', 'tháng 10', 'tháng 11', 'tháng 12'
			];

			return `Đã đánh giá vào ${monthNames[date.getMonth()]}, ${date.getFullYear()}`;
		} catch (e) {
			console.error('Error formatting review date:', dateString, e);
			return 'Ngày không hợp lệ';
		}
	}

	function generateStars(rating: number): string {
		return '⭐'.repeat(Math.round(rating));
	}

	function getInitials(name: string): string {
		if (!name) return 'NN';

		const parts = name.trim().split(/\s+/);
		if (parts.length === 1) return parts[0].substring(0, 2).toUpperCase();

		return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
	}

	async function fetchTourSuggestions() {
		if (!$user) return;

		isLoadingSuggestions = true;
		try {
			const suggestionsResponse = await fetch(`http://localhost:5000/api/tour-suggestions/user/${$user.ma_nguoi_dung}`);

			if (suggestionsResponse.ok) {
				const suggestionsData = await suggestionsResponse.json();

				const validSuggestions = suggestionsData.suggestions.filter(
					(suggestion: any) => suggestion.trang_thai === 'da_goi_y' && suggestion.suggested_tours?.length > 0
				);

				if (validSuggestions.length > 0) {
					const latestSuggestion = validSuggestions[0];

					suggestedTours = latestSuggestion.suggested_tours
						.filter((suggestedTour: any) => suggestedTour.ma_tour !== parseInt(tourId))
						.slice(0, 3);
				}
			}
		} catch (err) {
			console.error('Lỗi khi lấy gợi ý tour:', err);
		} finally {
			isLoadingSuggestions = false;
		}
	}

	function handleViewTourDetail(tourId: number, event: MouseEvent) {
		event.preventDefault();
		window.location.href = `/tour/${tourId}`;
	}

	onMount(async () => {
		isLoading = true;
		error = null;
		try {
			const response = await fetch(`http://localhost:5000/api/tours/${tourId}`);
			if (!response.ok) {
				if (response.status === 404) {
					throw new Error('Tour không tồn tại hoặc đã bị xóa.');
				}
				throw new Error(`Lỗi ${response.status}: Không thể tải chi tiết tour.`);
			}
			const data = await response.json();
			tour = data.tour || data;

			if (!tour || Object.keys(tour).length === 0) {
				throw new Error('Không tìm thấy thông tin chi tiết cho tour này.');
			}
			try {
				const relatedResponse = await fetch(`http://localhost:5000/api/tours?limit=3&location=${encodeURIComponent(tour.dia_diem)}`);
				if (relatedResponse.ok) {
					const relatedData = await relatedResponse.json();
					relatedTours = relatedData.filter((relatedTour: any) => relatedTour.ma_tour !== tour.ma_tour).slice(0, 3);
				}
			} catch (relatedErr) {
				console.error('Lỗi khi tải tour liên quan:', relatedErr);
			}

			if ($user) {
				await fetchTourSuggestions();
			}
			await fetchTourRatings();
			numberOfPeople = 1;
			notes = '';
			bookingStatus = 'idle';
			bookingError = '';

			isLoading = false;
		} catch (err: any) {
			console.error('Lỗi khi tải chi tiết tour:', err);
			error = err.message;
			isLoading = false;
		}
	});

	$: totalPrice = tour && tour.gia ? tour.gia * numberOfPeople : 0;
	$: isBookingDisabled = bookingStatus === 'loading' || numberOfPeople < 1 || (tour && numberOfPeople > tour.so_cho_trong);

</script>

<Navbar />

<div class="tour-detail-page-container">
	{#if isLoading}
		<div class="loading-spinner-container">
			<div class="spinner"></div>
			<p>Đang tải chi tiết tour...</p>
		</div>
	{:else if error}
		<div class="error-message-container">
			<i class="fas fa-exclamation-triangle error-icon"></i>
			<h2>Đã xảy ra lỗi</h2>
			<p>{error}</p>
			<a href="/tours_trong_nuoc" class="back-button">
				<i class="fas fa-arrow-left"></i> Quay lại danh sách tour
			</a>
		</div>
	{:else if tour}
		<div class="hero-banner" style={tour.hinh_anh ? `background-image: url('${formatImageUrl(tour.hinh_anh)}')` : ''}>
			{#if !tour.hinh_anh}
				<div class="default-banner-gradient"></div>
			{/if}
			<div class="hero-overlay">
				<div class="hero-content">
					<h1>{tour.ten_tour}</h1>
					<div class="hero-location">
						<i class="fas fa-map-marker-alt"></i> {tour.dia_diem || 'N/A'}
					</div>
				</div>
			</div>
		</div>

		<div class="tour-details-layout">
			<div class="main-content">
				<div class="tour-overview section">
					<h2>Tổng quan về tour</h2>
					<div class="tour-info-grid">
						<div class="info-item">
							<i class="fas fa-calendar-alt"></i>
							<div>
								<h4>Thời gian</h4>
								<p>{formatDate(tour.ngay_bat_dau)} - {formatDate(tour.ngay_ket_thuc)}</p>
							</div>
						</div>
						<div class="info-item">
							<i class="fas fa-users"></i>
							<div>
								<h4>Số chỗ còn trống</h4>
								<p>{tour.so_cho_trong ?? 'N/A'} người</p>
							</div>
						</div>
						<div class="info-item">
							<i class="fas fa-tag"></i>
							<div>
								<h4>Giá tour</h4>
								<p class="price">{formatPrice(tour.gia)}/người</p>
							</div>
						</div>
						<div class="info-item">
							<i class="fas fa-globe-asia"></i>
							<div>
								<h4>Loại tour</h4>
								<p>{tour.loai_tour === 'trong_nuoc' ? 'Tour trong nước' : 'Tour nước ngoài'}</p>
							</div>
						</div>
					</div>
				</div>

				{#if tour.mo_ta}
					<div class="description-section section">
						<h2>Mô tả chi tiết</h2>
						<div class="content-box">
							{@html tour.mo_ta}
						</div>
					</div>
				{/if}

				{#if tour.lich_trinh && tour.lich_trinh.length > 0}
					<div class="itinerary-section section">
						<h2>Lịch trình chi tiết</h2>
						{#each tour.lich_trinh as day, index}
							<div class="day-item">
								<div class="day-header">
									<span class="day-number">Ngày {index + 1}</span>
									{#if day.tieu_de}<h3>{day.tieu_de}</h3>{/if}
								</div>
								{#if day.mo_ta_chi_tiet}
									<div class="content-box">
										{@html day.mo_ta_chi_tiet}
									</div>
								{/if}
								{#if day.hinh_anh_ngay}
									<img src={formatImageUrl(day.hinh_anh_ngay)} alt="Hình ảnh ngày {index + 1}" class="day-image"/>
								{/if}
							</div>
						{/each}
					</div>
				{/if}

				<div class="map-section section">
					<h2>Vị trí trên bản đồ</h2>
					<div class="map-placeholder">
						<i class="fas fa-map-marked-alt"></i>
						<p>Bản đồ khu vực {tour.dia_diem}</p>
						<p class="map-note">Nhấp vào biểu tượng để xem trên Google Maps</p>
						<a href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(tour.dia_diem)}`}
						   class="view-on-maps-btn" target="_blank" rel="noopener noreferrer">
							<i class="fas fa-external-link-alt"></i> Xem trên Google Maps
						</a>
					</div>
				</div>

				<div class="reviews-section section">
					<h2>Đánh giá từ khách hàng</h2>

					{#if isLoadingRatings}
						<div class="loading-spinner-container small">
							<div class="spinner"></div>
							<p>Đang tải đánh giá...</p>
						</div>
					{:else if ratingError}
						<div class="error-message">
							<i class="fas fa-exclamation-circle"></i> {ratingError}
						</div>
					{:else}
						<div class="reviews-summary">
							<div class="reviews-score">
								<div class="big-score">{averageRating.toFixed(1)}</div>
								<div class="score-label">
									<div class="score-text">
										{#if averageRating >= 4.5}
											Tuyệt vời
										{:else if averageRating >= 4}
											Rất tốt
										{:else if averageRating >= 3}
											Tốt
										{:else if averageRating >= 2}
											Trung bình
										{:else}
											Cần cải thiện
										{/if}
									</div>
									<div class="score-count">Dựa trên {totalRatings} đánh giá</div>
								</div>
							</div>

							<div class="reviews-breakdown">
								<div class="breakdown-item">
									<span class="breakdown-label">Tổng thể</span>
									<div class="breakdown-bar">
										<div class="breakdown-fill" style="width: {(averageRating / 5) * 100}%"></div>
									</div>
									<span class="breakdown-score">{averageRating.toFixed(1)}</span>
								</div>
							</div>
						</div>

						{#if $user}
							{#if showRatingForm}
								<div class="rating-form-section">
									<div class="form-header">
										<h3>{userRating ? 'Cập nhật đánh giá của bạn' : 'Đánh giá tour này'}</h3>
										<button
											type="button"
											class="close-form-btn"
											on:click={() => showRatingForm = false}
											aria-label="Đóng form"
										>
											<i class="fas fa-times"></i>
										</button>
									</div>

									<form on:submit|preventDefault={submitRating}>
										<div class="form-group">
											<label for="ratingValue">Xếp hạng <span class="required">*</span></label>
											<div class="rating-input">
												{#each Array(5) as _, i}
													<button
														type="button"
														class="star-button {i < ratingValue ? 'active' : ''}"
														on:click={() => ratingValue = i + 1}
														aria-label="Đánh giá {i + 1} sao"
													>
														⭐
													</button>
												{/each}
												<span class="rating-value">{ratingValue}/5</span>
											</div>
										</div>

										<div class="form-group">
											<label for="ratingComment">Nhận xét (tùy chọn)</label>
											<div class="textarea-container">
												<textarea
													id="ratingComment"
													bind:value={ratingComment}
													rows="3"
													placeholder="Chia sẻ trải nghiệm của bạn về tour này..."
												></textarea>
												<div class="textarea-icon">
													<i class="fas fa-comment-dots"></i>
												</div>
											</div>
										</div>

										{#if ratingStatus === 'error' && ratingFormError}
											<div class="error-message form-error">
												<i class="fas fa-exclamation-circle"></i> {ratingFormError}
											</div>
										{/if}

										<div class="form-actions">
											<button
												type="button"
												class="cancel-btn"
												on:click={() => showRatingForm = false}
											>
												Hủy
											</button>
											<button
												type="submit"
												class="rating-submit-button"
												disabled={ratingStatus === 'loading'}
											>
												{#if ratingStatus === 'loading'}
													<span class="spinner-small"></span> Đang xử lý...
												{:else}
													<i class="fas fa-paper-plane"></i> {userRating ? 'Cập nhật đánh giá' : 'Gửi đánh giá'}
												{/if}
											</button>
										</div>
									</form>
								</div>
							{:else}
								<div class="write-review-container">
									<button class="write-review-btn" on:click={() => showRatingForm = true}>
										{userRating ? 'Chỉnh sửa đánh giá của bạn' : 'Viết đánh giá'}
									</button>
								</div>
							{/if}
						{:else}
							<div class="login-prompt rating-login-prompt">
								<i class="fas fa-info-circle"></i>
								<p>Vui lòng <a href="/login?redirect=/tour/{tourId}">đăng nhập</a> để đánh giá tour này.</p>
							</div>
						{/if}

						<div class="reviews-list">
							{#if ratings.length === 0}
								<div class="no-reviews-message">
									<i class="fas fa-comment-slash"></i>
									<p>Chưa có đánh giá nào cho tour này.</p>
									{#if $user}
										<p>Hãy là người đầu tiên đánh giá!</p>
									{/if}
								</div>
							{:else}
								{#each ratings as rating}
									<div class="review-card {rating.ma_nguoi_dung === $user?.ma_nguoi_dung ? 'user-review' : ''}">
										<div class="reviewer-info">
											<div class="reviewer-avatar">
												<div class="avatar-placeholder">{getInitials(rating.ten_nguoi_dung)}</div>
											</div>
											<div class="reviewer-details">
												<div class="reviewer-name">{rating.ten_nguoi_dung}</div>
												<div class="review-date">{formatReviewDate(rating.ngay_danh_gia)}</div>
											</div>
										</div>
										<div class="review-content">
											<div class="review-rating">
												<span class="stars">{generateStars(rating.xep_hang)}</span>
												<span class="review-score-badge">{rating.xep_hang.toFixed(1)}</span>

												{#if $user && $user.ma_nguoi_dung === rating.ma_nguoi_dung}
													<div class="review-actions">
														<button
															class="edit-review-btn"
															on:click={() => {
																ratingValue = rating.xep_hang;
																ratingComment = rating.binh_luan || '';
																showRatingForm = true;
															}}
															aria-label="Chỉnh sửa đánh giá"
														>
															<i class="fas fa-edit"></i>
														</button>
														<button
															class="delete-review-btn"
															on:click={() => {
																if(confirm('Bạn có chắc chắn muốn xóa đánh giá này không?')) {
																	alert('Chức năng xóa đánh giá sẽ được triển khai sau');
																}
															}}
															aria-label="Xóa đánh giá"
														>
															<i class="fas fa-trash-alt"></i>
														</button>
													</div>
												{/if}
											</div>
											{#if rating.binh_luan}
												<p class="review-text">{rating.binh_luan}</p>
											{:else}
												<p class="review-text no-comment">Người dùng không để lại bình luận.</p>
											{/if}
										</div>
									</div>
								{/each}

								{#if ratings.length > 3}
									<button class="view-all-reviews-btn">Xem tất cả đánh giá <i class="fas fa-chevron-right"></i></button>
								{/if}
							{/if}
						</div>
					{/if}
				</div>

			</div>

			<div class="sidebar-content">
				<div class="booking-form-section section sticky-sidebar">
					<h2>Đặt tour ngay</h2>
					{#if !$user}
						<div class="login-prompt">
							<i class="fas fa-info-circle"></i>
							<p>Vui lòng <a href="/login?redirect=/tour/{tourId}">đăng nhập</a> hoặc <a href="/register?redirect=/tour/{tourId}">đăng ký</a> để đặt tour.</p>
						</div>
					{:else}
						<form on:submit|preventDefault={handleBooking}>
							<div class="form-group">
								<label for="numberOfPeople">Số người tham gia <span class="required">*</span></label>
								<input
									type="number"
									id="numberOfPeople"
									bind:value={numberOfPeople}
									min="1"
									max={tour.so_cho_trong ?? 100}
									required
									aria-describedby="people-error"
								/>
								{#if bookingStatus === 'error' && bookingError.includes('Số lượng người')}
									<p id="people-error" class="inline-error">{bookingError}</p>
								{/if}
							</div>

							<div class="form-group">
								<label for="notes">Ghi chú (tùy chọn)</label>
								<textarea
									id="notes"
									bind:value={notes}
									rows="3"
									placeholder="Yêu cầu đặc biệt (ví dụ: ăn chay, phòng riêng...)"
								></textarea>
							</div>

							<div class="booking-summary">
								<h3>Tóm tắt</h3>
								<div class="summary-item">
									<span>Số người:</span>
									<span>{numberOfPeople}</span>
								</div>
								<div class="summary-item">
									<span>Giá/người:</span>
									<span>{formatPrice(tour.gia)}</span>
								</div>
								<hr class="summary-divider">
								<div class="summary-item total">
									<span>Tổng cộng:</span>
									<span class="total-price">{formatPrice(totalPrice)}</span>
								</div>
							</div>

							{#if bookingStatus === 'error' && !bookingError.includes('Số lượng người')}
								<div class="error-message form-error">
									<i class="fas fa-exclamation-circle"></i> {bookingError}
								</div>
							{/if}

							<button
								type="submit"
								class="booking-button"
								disabled={isBookingDisabled}
							>
								{#if bookingStatus === 'loading'}
									<span class="spinner-small"></span> Đang xử lý...
								{:else}
									<i class="fas fa-check-circle"></i> Xác nhận đặt tour
								{/if}
							</button>
							{#if tour && tour.so_cho_trong !== null && tour.so_cho_trong < 5}
								<p class="low-stock-warning">
									<i class="fas fa-exclamation-triangle"></i> Chỉ còn {tour.so_cho_trong} chỗ trống!
								</p>
							{/if}
						</form>
					{/if}
				</div>
			</div>
		</div>

		<div class="related-tours-section section">
			<h2>Tour tương tự có thể bạn quan tâm</h2>

			{#if $user && suggestedTours && suggestedTours.length > 0}
				<div class="suggestion-badge">
					<i class="fas fa-star"></i> Gợi ý dành riêng cho bạn
				</div>

				<div class="related-tours-grid">
					{#each suggestedTours as suggestedTour}
						<div class="related-tour-card suggested">
							<div class="related-tour-image">
								<img src={formatImageUrl(suggestedTour.hinh_anh)} alt={suggestedTour.ten_tour} />
								<div class="related-tour-price">{formatPrice(suggestedTour.gia)}</div>
							</div>
							<div class="related-tour-content">
								<h3>{suggestedTour.ten_tour}</h3>
								<div class="related-tour-info">
									<span><i class="fas fa-map-marker-alt"></i> {suggestedTour.dia_diem}</span>
									<span><i class="fas fa-calendar-alt"></i> {calculateDays(suggestedTour.ngay_bat_dau, suggestedTour.ngay_ket_thuc)} ngày</span>
								</div>
								<a href={`/tour/${suggestedTour.ma_tour}`} class="view-tour-btn" on:click={(e) => handleViewTourDetail(suggestedTour.ma_tour, e)}>Xem chi tiết</a>
							</div>
						</div>
					{/each}
				</div>
			{:else if relatedTours && relatedTours.length > 0}
				<div class="related-tours-grid">
					{#each relatedTours as relatedTour}
						<div class="related-tour-card">
							<div class="related-tour-image">
								<img src={formatImageUrl(relatedTour.hinh_anh)} alt={relatedTour.ten_tour} />
								<div class="related-tour-price">{formatPrice(relatedTour.gia)}</div>
							</div>
							<div class="related-tour-content">
								<h3>{relatedTour.ten_tour}</h3>
								<div class="related-tour-info">
									<span><i class="fas fa-map-marker-alt"></i> {relatedTour.dia_diem}</span>
									<span><i class="fas fa-calendar-alt"></i> {calculateDays(relatedTour.ngay_bat_dau, relatedTour.ngay_ket_thuc)} ngày</span>
								</div>
								<a href={`/tour/${relatedTour.ma_tour}`} class="view-tour-btn" on:click={(e) => handleViewTourDetail(relatedTour.ma_tour, e)}>Xem chi tiết</a>
							</div>
						</div>
					{/each}
				</div>
			{:else}
				<div class="related-tours-grid">
					<div class="related-tour-card">
						<div class="related-tour-image">
							<img src="/images/tour1.jpg" alt="Tour Đà Lạt" />
							<div class="related-tour-price">2,500,000 VNĐ</div>
						</div>
						<div class="related-tour-content">
							<h3>Tour Đà Lạt 3 ngày 2 đêm</h3>
							<div class="related-tour-info">
								<span><i class="fas fa-map-marker-alt"></i> Đà Lạt</span>
								<span><i class="fas fa-calendar-alt"></i> 3 ngày</span>
							</div>
							<a href="/tour/1" class="view-tour-btn" on:click={(e) => handleViewTourDetail(1, e)}>Xem chi tiết</a>
						</div>
					</div>

					<div class="related-tour-card">
						<div class="related-tour-image">
							<img src="/images/tour2.jpg" alt="Tour Nha Trang" />
							<div class="related-tour-price">3,200,000 VNĐ</div>
						</div>
						<div class="related-tour-content">
							<h3>Tour Nha Trang 4 ngày 3 đêm</h3>
							<div class="related-tour-info">
								<span><i class="fas fa-map-marker-alt"></i> Nha Trang</span>
								<span><i class="fas fa-calendar-alt"></i> 4 ngày</span>
							</div>
							<a href="/tour/2" class="view-tour-btn" on:click={(e) => handleViewTourDetail(2, e)}>Xem chi tiết</a>
						</div>
					</div>

					<div class="related-tour-card">
						<div class="related-tour-image">
							<img src="/images/tour3.jpg" alt="Tour Phú Quốc" />
							<div class="related-tour-price">4,500,000 VNĐ</div>
						</div>
						<div class="related-tour-content">
							<h3>Tour Phú Quốc 5 ngày 4 đêm</h3>
							<div class="related-tour-info">
								<span><i class="fas fa-map-marker-alt"></i> Phú Quốc</span>
								<span><i class="fas fa-calendar-alt"></i> 5 ngày</span>
							</div>
							<a href="/tour/3" class="view-tour-btn" on:click={(e) => handleViewTourDetail(3, e)}>Xem chi tiết</a>
						</div>
					</div>
				</div>
			{/if}

			{#if $user && !suggestedTours.length}
				<div class="suggestion-prompt">
					<p>
						<i class="fas fa-lightbulb"></i>
						Bạn muốn nhận gợi ý tour phù hợp với sở thích?
						<a href="/tour-suggestions">Tạo yêu cầu gợi ý tour</a> ngay!
					</p>
				</div>
			{/if}
		</div>

		{#if showBookingSuccess}
			<div class="success-overlay" transition:fade={{ duration: 300 }}>
				<div class="success-message">
					<i class="fas fa-check-circle success-icon"></i>
					<h3>Đặt tour thành công!</h3>
					<p>Thông tin chi tiết đã được gửi đến email của bạn.</p>
					<p>Bạn sẽ được chuyển đến trang cá nhân sau giây lát...</p>
					<div class="progress-bar">
						<div class="progress"></div>
					</div>
				</div>
			</div>
		{/if}

	{:else}
		<div class="error-message-container">
			<i class="fas fa-search error-icon"></i>
			<h2>Không tìm thấy thông tin</h2>
			<p>Rất tiếc, chúng tôi không tìm thấy thông tin chi tiết cho tour này.</p>
			<a href="/tours_trong_nuoc" class="back-button">
				<i class="fas fa-arrow-left"></i> Quay lại danh sách tour
			</a>
		</div>
	{/if}
</div>

<CTA />
<Footer />

<style>
	:root {
		--primary-color: #3498db;
		--secondary-color: #2c3e50;
		--accent-color: #e74c3c;
		--light-gray: #f8f9fa;
		--medium-gray: #ecf0f1;
		--dark-gray: #7f8c8d;
		--text-color: #34495e;
		--white-color: #ffffff;
		--success-color: #2ecc71;
		--error-color: #e74c3c;
		--warning-color: #f39c12;
		--border-color: #dee2e6;
		--border-radius: 8px;
		--box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
		--section-padding: 30px;
		--container-max-width: 1200px;
	}

	.tour-detail-page-container {
		max-width: var(--container-max-width);
		margin: 0 auto;
		padding: 0 15px;
		position: relative;
	}

	.loading-spinner-container,
	.error-message-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-height: 50vh;
		padding: 40px 20px;
		text-align: center;
	}

	.spinner {
		border: 4px solid rgba(0, 0, 0, 0.1);
		border-radius: 50%;
		border-top-color: var(--primary-color);
		width: 50px;
		height: 50px;
		animation: spin 1s linear infinite;
		margin-bottom: 20px;
	}

	.spinner-small {
		border: 3px solid rgba(255, 255, 255, 0.3);
		border-radius: 50%;
		border-top-color: var(--white-color);
		width: 16px;
		height: 16px;
		animation: spin 0.8s linear infinite;
		display: inline-block;
		margin-right: 8px;
		vertical-align: middle;
	}

	@keyframes spin {
		to { transform: rotate(360deg); }
	}

	.error-message-container {
		background-color: var(--light-gray);
		border-radius: var(--border-radius);
		margin: 40px 0;
		color: var(--text-color);
	}
	.error-message-container h2 {
		color: var(--accent-color);
		margin-bottom: 15px;
	}
	.error-icon {
		font-size: 3rem;
		color: var(--accent-color);
		margin-bottom: 20px;
	}

	.back-button {
		display: inline-flex;
		align-items: center;
		gap: 8px;
		margin-top: 25px;
		padding: 12px 25px;
		background-color: var(--primary-color);
		color: var(--white-color);
		text-decoration: none;
		border-radius: 5px;
		transition: background-color 0.3s ease, transform 0.2s ease;
		font-weight: 500;
	}

	.back-button:hover {
		background-color: #2980b9;
		transform: translateY(-2px);
	}
	.back-button i {
		font-size: 0.9em;
	}

	/* Hero Banner */
	.hero-banner {
		height: 50vh; /* Relative height */
		min-height: 350px;
		max-height: 550px;
		position: relative;
		border-radius: var(--border-radius);
		overflow: hidden;
		margin-top: 30px;
		background-color: var(--medium-gray); /* Fallback color */
		/* Basic Parallax Effect */
		background-size: cover;
		background-position: center center;
		background-repeat: no-repeat;
		background-attachment: fixed; /* This creates the parallax effect */
	}

	.default-banner-gradient {
		position: absolute;
		inset: 0;
		background-image: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
	}

	.hero-overlay {
		position: absolute;
		inset: 0; /* Replaces top, left, right, bottom */
		background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.7));
		display: flex;
		align-items: flex-end;
		padding: 40px;
		color: var(--white-color);
	}

	.hero-content {
		max-width: 800px;
	}

	.hero-content h1 {
		font-size: clamp(1.8rem, 4vw, 2.8rem); /* Responsive font size */
		margin-bottom: 10px;
		text-shadow: 2px 2px 5px rgba(0,0,0,0.6);
		font-weight: 700;
	}

	.hero-location {
		font-size: clamp(1rem, 2.5vw, 1.2rem);
		display: flex;
		align-items: center;
		gap: 8px;
		opacity: 0.9;
	}

	/* Layout */
	.tour-details-layout {
		display: grid;
		grid-template-columns: 1fr; /* Default: single column */
		gap: 30px;
		margin-top: 40px;
	}

	@media (min-width: 992px) { /* Apply two columns on larger screens */
		.tour-details-layout {
			grid-template-columns: 2fr 1fr; /* Main content wider than sidebar */
		}
		.sidebar-content {
			position: relative; /* Needed for sticky positioning context */
		}
		.sticky-sidebar {
			position: sticky;
			top: 20px; /* Adjust as needed based on Navbar height */
			z-index: 10;
		}
	}

	/* Sections Styling */
	.section {
		background: var(--white-color);
		border-radius: var(--border-radius);
		box-shadow: var(--box-shadow);
		padding: var(--section-padding);
		margin-bottom: 30px; /* Replaced margin: 30px 0 */
	}
	.section:last-child {
		margin-bottom: 0;
	}

	.section h2 {
		font-size: 1.8rem;
		color: var(--secondary-color);
		margin-top: 0;
		margin-bottom: 25px;
		padding-bottom: 15px;
		border-bottom: 1px solid var(--border-color);
		font-weight: 600;
	}

	/* Tour Overview Grid */
	.tour-info-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
		gap: 25px;
	}

	.info-item {
		display: flex;
		align-items: flex-start;
		gap: 15px;
	}

	.info-item i {
		font-size: 1.6rem;
		color: var(--primary-color);
		margin-top: 5px;
		flex-shrink: 0;
		width: 25px; /* Ensure consistent icon alignment */
		text-align: center;
	}

	.info-item div {
		flex-grow: 1;
	}

	.info-item h4 {
		margin: 0 0 5px 0;
		font-size: 0.95rem;
		color: var(--dark-gray);
		font-weight: 500;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.info-item p {
		margin: 0;
		font-size: 1.1rem;
		font-weight: 500;
		color: var(--text-color);
	}

	.info-item .price {
		color: var(--accent-color);
		font-weight: 700;
		font-size: 1.2rem;
	}

	/* Description & Itinerary Content Box */
	.content-box {
		line-height: 1.7;
		color: var(--text-color);
	}
	/* Removed unused CSS selectors */
	/* Removed additional unused CSS selectors */

	/* Itinerary Specific Styles */
	.itinerary-section .day-item {
		margin-bottom: 30px;
		padding-bottom: 30px;
		border-bottom: 1px dashed var(--border-color);
	}
	.itinerary-section .day-item:last-child {
		border-bottom: none;
		margin-bottom: 0;
		padding-bottom: 0;
	}
	.day-header {
		display: flex;
		align-items: baseline;
		gap: 15px;
		margin-bottom: 15px;
	}
	.day-number {
		background-color: var(--primary-color);
		color: var(--white-color);
		padding: 5px 12px;
		border-radius: 15px;
		font-size: 0.9rem;
		font-weight: 600;
		flex-shrink: 0;
	}
	.day-header h3 {
		margin: 0;
		font-size: 1.3rem;
		color: var(--secondary-color);
		font-weight: 600;
	}
	.day-image {
		max-width: 100%;
		height: auto;
		border-radius: var(--border-radius);
		margin-top: 20px;
		box-shadow: 0 2px 5px rgba(0,0,0,0.1);
	}

	/* Booking Form Section */
	.booking-form-section {
		background-color: var(--light-gray);
		border: 1px solid var(--border-color);
	}

	.form-group {
		margin-bottom: 20px;
	}

	.form-group label {
		display: block;
		margin-bottom: 8px;
		font-weight: 600;
		color: var(--secondary-color);
		font-size: 0.95rem;
	}

	.required {
		color: var(--error-color);
		margin-left: 2px;
	}

	.form-group input[type="number"],
	.form-group textarea {
		width: 100%;
		padding: 12px 15px;
		border: 1px solid var(--border-color);
		border-radius: 8px;
		font-size: 1rem;
		color: var(--text-color);
		transition: all 0.3s ease;
		background-color: white;
	}
	.form-group input[type="number"]:focus,
	.form-group textarea:focus {
		border-color: var(--primary-color);
		outline: none;
		box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
	}
	.form-group textarea {
		resize: vertical;
		min-height: 100px;
		padding-right: 40px; /* Space for the icon */
	}

	/* Textarea container with icon */
	.textarea-container {
		position: relative;
	}

	.textarea-icon {
		position: absolute;
		right: 15px;
		top: 15px;
		color: #bdc3c7;
		font-size: 1.2rem;
		transition: all 0.3s ease;
		pointer-events: none;
	}

	.textarea-container textarea:focus + .textarea-icon {
		color: var(--primary-color);
		transform: scale(1.1);
	}

	.inline-error {
		color: var(--error-color);
		font-size: 0.85rem;
		margin-top: 5px;
	}

	/* Booking Summary */
	.booking-summary {
		background-color: var(--white-color);
		border-radius: var(--border-radius);
		padding: 20px;
		margin: 25px 0;
		border: 1px solid var(--border-color);
	}

	.booking-summary h3 {
		margin-top: 0;
		margin-bottom: 15px;
		font-size: 1.2rem;
		color: var(--secondary-color);
		font-weight: 600;
	}

	.summary-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10px;
		font-size: 1rem;
		color: var(--text-color);
	}
	.summary-item span:first-child {
		color: var(--dark-gray);
	}
	.summary-item span:last-child {
		font-weight: 500;
	}

	.summary-divider {
		border: none;
		border-top: 1px solid var(--border-color);
		margin: 15px 0;
	}

	.summary-item.total {
		font-weight: 600;
		font-size: 1.15rem;
		color: var(--secondary-color);
		margin-top: 15px;
		padding-top: 10px;
	}
	.total-price {
		color: var(--accent-color);
		font-size: 1.3rem;
		font-weight: 700;
	}

	/* Form Error Message */
	.error-message.form-error {
		background-color: #fdedec;
		color: var(--error-color);
		padding: 12px 15px;
		border-radius: 5px;
		margin-bottom: 20px;
		display: flex;
		align-items: center;
		gap: 10px;
		font-size: 0.95rem;
		border: 1px solid var(--error-color);
	}

	/* Booking Button */
	.booking-button {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10px;
		width: 100%;
		padding: 15px;
		background-color: var(--primary-color);
		color: var(--white-color);
		border: none;
		border-radius: 5px;
		font-size: 1.1rem;
		font-weight: 600;
		cursor: pointer;
		transition: background-color 0.3s ease, transform 0.2s ease;
	}

	.booking-button:hover:not(:disabled) {
		background-color: #2980b9;
		transform: translateY(-2px);
	}

	.booking-button:disabled {
		background-color: #bdc3c7;
		cursor: not-allowed;
		opacity: 0.7;
	}
	.booking-button i {
		font-size: 1em;
	}

	/* Low Stock Warning */
	.low-stock-warning {
		text-align: center;
		margin-top: 15px;
		color: var(--warning-color);
		font-weight: 500;
		font-size: 0.9rem;
	}
	.low-stock-warning i {
		margin-right: 5px;
	}

	/* Login Prompt */
	.login-prompt {
		text-align: center;
		padding: 30px 20px;
		background-color: var(--medium-gray);
		border-radius: var(--border-radius);
		border: 1px dashed var(--border-color);
	}
	.login-prompt i {
		color: var(--primary-color);
		font-size: 1.5rem;
		margin-bottom: 10px;
		display: block;
	}
	.login-prompt p {
		margin: 0;
		color: var(--text-color);
		line-height: 1.6;
	}
	.login-prompt a {
		color: var(--primary-color);
		font-weight: 600;
		text-decoration: none;
	}
	.login-prompt a:hover {
		text-decoration: underline;
	}

	/* Success Overlay */
	.success-overlay {
		position: fixed;
		inset: 0;
		background-color: rgba(0, 0, 0, 0.75);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
		padding: 20px;
	}

	.success-message {
		background-color: var(--white-color);
		border-radius: var(--border-radius);
		padding: 40px;
		text-align: center;
		max-width: 450px;
		width: 100%;
		box-shadow: 0 5px 20px rgba(0,0,0,0.2);
	}

	.success-icon {
		font-size: 4rem;
		color: var(--success-color);
		margin-bottom: 25px;
		display: block; /* Make it block to center easily */
		line-height: 1;
	}

	.success-message h3 {
		margin: 0 0 15px 0;
		color: var(--secondary-color);
		font-size: 1.6rem;
		font-weight: 600;
	}

	.success-message p {
		margin: 0 0 10px 0;
		color: var(--dark-gray);
		line-height: 1.6;
	}
	.success-message p:last-of-type {
		margin-bottom: 25px; /* Add space before progress bar */
	}

	/* Progress Bar for Redirect */
	.progress-bar {
		height: 6px;
		background-color: var(--medium-gray);
		border-radius: 3px;
		overflow: hidden;
		width: 80%;
		margin: 0 auto; /* Center the progress bar */
	}
	.progress {
		height: 100%;
		width: 100%;
		background-color: var(--success-color);
		border-radius: 3px;
		animation: progress-decrease 2.5s linear forwards; /* Match timeout duration */
	}
	@keyframes progress-decrease {
		from { width: 100%; }
		to { width: 0%; }
	}



	/* Map Section Styles */
	.map-placeholder {
		background-color: var(--light-gray);
		border: 1px dashed var(--border-color);
		padding: 40px;
		text-align: center;
		border-radius: var(--border-radius);
		color: var(--dark-gray);
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 15px;
	}

	.map-placeholder i {
		font-size: 3rem;
		color: var(--primary-color);
	}

	.map-note {
		font-size: 0.9rem;
		opacity: 0.8;
	}

	.view-on-maps-btn {
		display: inline-flex;
		align-items: center;
		gap: 8px;
		background-color: var(--primary-color);
		color: white;
		padding: 10px 20px;
		border-radius: 5px;
		text-decoration: none;
		font-weight: 500;
		margin-top: 10px;
		transition: all 0.3s ease;
	}

	.view-on-maps-btn:hover {
		background-color: #2980b9;
		transform: translateY(-2px);
	}

	/* Reviews Section Styles */
	.reviews-section {
		position: relative;
	}

	.reviews-section h2 {
		margin-bottom: 30px;
	}

	.reviews-summary {
		display: flex;
		flex-wrap: wrap;
		gap: 35px;
		margin-bottom: 35px;
		padding: 25px;
		border-radius: 12px;
		background-color: #f8f9fa;
		box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
		position: relative;
		overflow: hidden;
	}

	.reviews-summary::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 5px;
		height: 100%;
		background-color: var(--primary-color);
	}

	.reviews-score {
		display: flex;
		align-items: center;
		gap: 18px;
		padding-right: 25px;
		position: relative;
	}

	.reviews-score::after {
		content: '';
		position: absolute;
		top: 50%;
		right: 0;
		transform: translateY(-50%);
		height: 70%;
		width: 1px;
		background-color: var(--border-color);
		display: none;
	}

	@media (min-width: 768px) {
		.reviews-score::after {
			display: block;
		}
	}

	.big-score {
		font-size: 3.5rem;
		font-weight: 800;
		color: var(--primary-color);
		line-height: 1;
		text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
		background: linear-gradient(135deg, var(--primary-color), #2980b9);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
		position: relative;
	}

	.big-score::after {
		content: '/5';
		position: absolute;
		bottom: 0;
		right: -25px;
		font-size: 1rem;
		font-weight: 600;
		color: var(--dark-gray);
		-webkit-text-fill-color: var(--dark-gray);
	}

	.score-label {
		display: flex;
		flex-direction: column;
	}

	.score-text {
		font-weight: 700;
		color: var(--secondary-color);
		font-size: 1.3rem;
		margin-bottom: 5px;
	}

	.score-count {
		color: var(--dark-gray);
		font-size: 0.95rem;
		background-color: rgba(0, 0, 0, 0.05);
		padding: 3px 10px;
		border-radius: 20px;
		display: inline-block;
	}

	.reviews-breakdown {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 15px;
		min-width: 280px;
		padding-left: 10px;
	}

	.breakdown-item {
		display: flex;
		align-items: center;
		gap: 12px;
	}

	.breakdown-label {
		width: 120px;
		font-size: 1rem;
		color: var(--text-color);
		font-weight: 500;
	}

	.breakdown-bar {
		flex: 1;
		height: 10px;
		background-color: #e9ecef;
		border-radius: 5px;
		overflow: hidden;
		box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
	}

	.breakdown-fill {
		height: 100%;
		background: linear-gradient(to right, #3498db, #2980b9);
		border-radius: 5px;
		transition: width 1s ease-in-out;
		position: relative;
		overflow: hidden;
	}

	.breakdown-fill::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(
			45deg,
			rgba(255, 255, 255, 0.2) 25%,
			transparent 25%,
			transparent 50%,
			rgba(255, 255, 255, 0.2) 50%,
			rgba(255, 255, 255, 0.2) 75%,
			transparent 75%,
			transparent
		);
		background-size: 15px 15px;
		z-index: 1;
	}

	.breakdown-score {
		width: 35px;
		text-align: right;
		font-weight: 700;
		color: var(--secondary-color);
		font-size: 1.05rem;
	}

	/* Rating Form Styles */
	.rating-form-section {
		background-color: #f9f9f9;
		padding: 25px;
		border-radius: 12px;
		margin-bottom: 35px;
		border: 1px solid #e0e0e0;
		box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.rating-form-section:hover {
		transform: translateY(-3px);
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
	}

	.rating-form-section h3 {
		margin-top: 0;
		margin-bottom: 20px;
		font-size: 1.3rem;
		color: #333;
		padding-bottom: 12px;
		border-bottom: 1px solid #eee;
		font-weight: 600;
	}

	.rating-input {
		display: flex;
		align-items: center;
		gap: 8px;
		margin-top: 10px;
		margin-bottom: 15px;
		padding: 12px 15px;
		background-color: #f5f5f5;
		border-radius: 8px;
		border: 1px solid #e0e0e0;
	}

	.star-button {
		background: none;
		border: none;
		font-size: 1.8rem;
		color: #d1d1d1;
		cursor: pointer;
		padding: 0;
		transition: all 0.3s ease;
		position: relative;
		margin-right: 2px;
	}

	.star-button:hover {
		transform: scale(1.15);
	}

	.star-button:hover::after {
		content: '';
		position: absolute;
		top: -5px;
		left: -5px;
		right: -5px;
		bottom: -5px;
		background: rgba(255, 204, 0, 0.1);
		border-radius: 50%;
		z-index: -1;
	}

	.star-button.active {
		color: #ffcc00;
		text-shadow: 0 0 5px rgba(255, 204, 0, 0.5);
		opacity: 1;
	}

	.star-button:focus {
		outline: none;
	}

	.rating-value {
		margin-left: 15px;
		font-weight: 600;
		color: #333;
		background: #fff;
		padding: 5px 12px;
		border-radius: 20px;
		border: 1px solid #e0e0e0;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
		font-size: 0.95rem;
	}

	.form-group textarea {
		width: 100%;
		padding: 12px 15px;
		border: 1px solid #e0e0e0;
		border-radius: 8px;
		font-size: 0.95rem;
		resize: vertical;
		min-height: 100px;
		transition: border-color 0.3s ease, box-shadow 0.3s ease;
	}

	.form-group textarea:focus {
		outline: none;
		border-color: var(--primary-color);
		box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
	}

	.rating-submit-button {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10px;
		background: linear-gradient(to right, var(--primary-color), #66bb6a);
		color: white;
		border: none;
		border-radius: 50px;
		padding: 10px 25px;
		font-size: 1.05rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.3s ease;
		margin-top: 25px;
		box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
		width: 100%;
		max-width: 300px;
		margin-left: auto;
		margin-right: auto;
		min-width: 140px;
	}

	.rating-submit-button:hover:not(:disabled) {
		background: linear-gradient(to right, #388e3c, var(--primary-color));
		transform: translateY(-2px);
		box-shadow: 0 6px 15px rgba(76, 175, 80, 0.4);
	}

	.rating-submit-button:active:not(:disabled) {
		transform: translateY(0);
		box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
	}

	.rating-submit-button:disabled {
		opacity: 0.8;
		cursor: not-allowed;
		background: linear-gradient(to right, #78909c, #90a4ae);
		box-shadow: 0 4px 10px rgba(120, 144, 156, 0.3);
	}

	.rating-submit-button i {
		margin-right: 8px;
		font-size: 1.1rem;
	}

	.form-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20px;
	}

	.close-form-btn {
		background: none;
		border: none;
		color: #999;
		font-size: 1.2rem;
		cursor: pointer;
		padding: 5px;
		border-radius: 50%;
		transition: all 0.3s ease;
		width: 30px;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.close-form-btn:hover {
		background: #f5f5f5;
		color: #333;
		transform: rotate(90deg);
	}

	.form-actions {
		display: flex;
		justify-content: flex-end;
		gap: 15px;
		margin-top: 25px;
	}

	.cancel-btn {
		background: #f5f5f5;
		color: #555;
		border: none;
		padding: 10px 20px;
		border-radius: 50px;
		cursor: pointer;
		font-weight: 600;
		transition: all 0.3s ease;
		box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.cancel-btn:hover {
		background: #e0e0e0;
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
	}

	.cancel-btn:active {
		transform: translateY(0);
		box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
	}

	.write-review-container {
		display: flex;
		justify-content: center;
		margin: 20px 0 30px;
	}

	.write-review-btn {
		background: linear-gradient(to right, var(--primary-color), #66bb6a);
		color: white;
		border: none;
		padding: 10px 20px;
		border-radius: 50px;
		cursor: pointer;
		font-weight: 600;
		transition: all 0.3s ease;
		box-shadow: 0 4px 10px rgba(76, 175, 80, 0.2);
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.write-review-btn::before {
		content: '\f304';
		font-family: 'Font Awesome 5 Free';
		font-weight: 900;
	}

	.write-review-btn:hover {
		background: linear-gradient(to right, #388e3c, var(--primary-color));
		transform: translateY(-2px);
		box-shadow: 0 6px 15px rgba(76, 175, 80, 0.3);
	}

	.write-review-btn:active {
		transform: translateY(0);
		box-shadow: 0 4px 8px rgba(76, 175, 80, 0.2);
	}

	.rating-login-prompt {
		margin-bottom: 35px;
		background-color: #e8f4fc;
		padding: 20px;
		border-radius: 8px;
		border-left: 4px solid var(--primary-color);
		text-align: center;
	}

	.rating-login-prompt i {
		color: var(--primary-color);
		font-size: 1.8rem;
		margin-bottom: 12px;
	}

	.rating-login-prompt a {
		font-weight: 700;
		text-decoration: underline;
		transition: color 0.2s ease;
	}

	.rating-login-prompt a:hover {
		color: #2980b9;
	}

	/* Reviews List Styles */
	.reviews-list {
		display: flex;
		flex-direction: column;
		gap: 25px;
		margin-top: 30px;
		position: relative;
	}

	.reviews-list::before {
		content: '';
		position: absolute;
		top: 0;
		left: 25px;
		width: 2px;
		height: calc(100% - 60px);
		background-color: #e9ecef;
		z-index: 0;
		display: none;
	}

	@media (min-width: 768px) {
		.reviews-list::before {
			display: block;
		}
	}

	.no-reviews-message {
		text-align: center;
		padding: 40px 0;
		color: #666;
		background: white;
		border-radius: 12px;
		box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
		border: 1px dashed #ddd;
		margin: 20px 0;
	}

	.no-reviews-message i {
		font-size: 4rem;
		color: #e0e0e0;
		margin-bottom: 20px;
		display: block;
	}

	.no-reviews-message p {
		margin-bottom: 20px;
		font-size: 1.1rem;
	}

	.no-reviews-message p:last-child {
		color: var(--primary-color);
		font-weight: 600;
	}

	.review-card {
		display: flex;
		flex-direction: column;
		gap: 15px;
		padding: 25px;
		background: #f9f9f9;
		border-radius: 12px;
		margin-bottom: 20px;
		box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
		border: 1px solid #eee;
		position: relative;
		z-index: 1;
	}

	@media (min-width: 576px) {
		.review-card {
			flex-direction: row;
			gap: 25px;
		}
	}

	.review-card:hover {
		transform: translateY(-3px);
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
	}

	.review-card.user-review {
		background-color: #e8f4fc; /* Light blue background for user's own review */
		border: 1px solid #bde0f7;
	}

	.review-card.user-review::after {
		content: 'Đánh giá của bạn';
		position: absolute;
		top: -10px;
		right: 20px;
		background-color: var(--primary-color);
		color: white;
		padding: 3px 12px;
		border-radius: 20px;
		font-size: 0.8rem;
		font-weight: 600;
		box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
	}

	.reviewer-info {
		display: flex;
		gap: 15px;
		min-width: 200px;
	}

	.reviewer-avatar {
		width: 60px;
		height: 60px;
		border-radius: 50%;
		overflow: hidden;
		background: linear-gradient(135deg, #4caf50, #66bb6a);
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
		border: 3px solid white;
	}

	.avatar-placeholder {
		color: white;
		font-weight: 700;
		font-size: 1.2rem;
		text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
	}

	.reviewer-details {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.reviewer-name {
		font-weight: 600;
		color: #333;
		margin-bottom: 6px;
		font-size: 1.05rem;
	}

	.review-date {
		font-size: 0.85rem;
		color: #777;
		display: flex;
		align-items: center;
	}

	.review-date::before {
		content: '\f017'; /* Font Awesome clock icon */
		font-family: 'Font Awesome 5 Free';
		font-weight: 400;
		margin-right: 5px;
		font-size: 0.8rem;
		opacity: 0.7;
	}

	.review-content {
		flex: 1;
		position: relative;
	}

	@media (min-width: 576px) {
		.review-content::before {
			content: '';
			position: absolute;
			top: 0;
			left: -15px;
			width: 1px;
			height: 100%;
			background-color: #eaeaea;
		}
	}

	.review-rating {
		display: flex;
		align-items: center;
		margin-bottom: 15px;
		position: relative;
	}

	.stars {
		letter-spacing: 3px;
		font-size: 1.2rem;
		filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
	}

	.review-score-badge {
		background: linear-gradient(to right, #4caf50, #66bb6a);
		color: white;
		padding: 4px 8px;
		border-radius: 4px;
		font-size: 0.85rem;
		font-weight: 600;
		margin-left: 10px;
		box-shadow: 0 2px 5px rgba(76, 175, 80, 0.2);
	}

	.review-actions {
		margin-left: auto;
		display: flex;
		gap: 8px;
	}

	.edit-review-btn, .delete-review-btn {
		background: white;
		border: none;
		cursor: pointer;
		width: 36px;
		height: 36px;
		border-radius: 50%;
		transition: all 0.3s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
	}

	.edit-review-btn {
		color: #2196f3;
	}

	.delete-review-btn {
		color: #f44336;
	}

	.edit-review-btn:hover {
		background: #e3f2fd;
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
	}

	.delete-review-btn:hover {
		background: #ffebee;
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(244, 67, 54, 0.2);
	}

	.edit-review-btn:active, .delete-review-btn:active {
		transform: translateY(0);
	}

	.review-text {
		line-height: 1.7;
		color: #555;
		background: white;
		padding: 15px;
		border-radius: 8px;
		border-left: 4px solid #4caf50;
		box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
		margin: 0;
		font-size: 1.05rem;
	}

	.review-text.no-comment {
		font-style: italic;
		color: #999;
		padding-left: 15px;
		border-left: 4px solid #e0e0e0;
	}

	.review-text.no-comment::before {
		display: none;
	}

	.view-all-reviews-btn {
		align-self: center;
		background-color: white;
		border: 1px solid #e1e1e1;
		color: var(--primary-color);
		font-weight: 600;
		padding: 12px 25px;
		border-radius: 30px;
		cursor: pointer;
		display: flex;
		align-items: center;
		gap: 10px;
		transition: all 0.3s ease;
		margin-top: 10px;
		box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
	}

	.view-all-reviews-btn:hover {
		color: white;
		background-color: var(--primary-color);
		border-color: var(--primary-color);
		transform: translateY(-3px);
		box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
	}

	.view-all-reviews-btn i {
		transition: transform 0.3s ease;
	}

	.view-all-reviews-btn:hover i {
		transform: translateX(5px);
	}

	/* Small loading spinner for ratings section */
	.loading-spinner-container.small {
		padding: 30px;
		min-height: auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #f8f9fa;
		border-radius: 12px;
		box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
		margin: 20px 0;
	}

	.loading-spinner-container.small .spinner {
		width: 40px;
		height: 40px;
		border-width: 3px;
		border-top-color: var(--primary-color);
		margin-bottom: 15px;
		animation: spin 0.8s linear infinite;
	}

	.loading-spinner-container.small p {
		color: var(--secondary-color);
		font-weight: 500;
		font-size: 1.05rem;
	}

	/* Error message styling */
	.reviews-section .error-message {
		background-color: #fdeaea;
		color: #e74c3c;
		padding: 20px;
		border-radius: 8px;
		margin: 20px 0;
		display: flex;
		align-items: center;
		gap: 15px;
		border-left: 4px solid #e74c3c;
		font-weight: 500;
	}

	.reviews-section .error-message i {
		font-size: 1.5rem;
	}

	/* Related Tours Section Styles */
	.related-tours-section {
		margin-top: 40px;
	}

	.suggestion-badge {
		display: inline-flex;
		align-items: center;
		background-color: #ffd700;
		color: #333;
		padding: 5px 12px;
		border-radius: 20px;
		margin-bottom: 20px;
		font-weight: 600;
		font-size: 0.9rem;
		box-shadow: 0 2px 5px rgba(0,0,0,0.1);
	}

	.suggestion-badge i {
		margin-right: 8px;
		color: #ff8c00;
	}

	.suggestion-prompt {
		background-color: #f8f9fa;
		border-left: 4px solid var(--primary-color);
		padding: 15px;
		margin-top: 20px;
		border-radius: 0 4px 4px 0;
	}

	.suggestion-prompt p {
		margin: 0;
		display: flex;
		align-items: center;
		gap: 10px;
	}

	.suggestion-prompt i {
		color: #ffc107;
		font-size: 1.2rem;
	}

	.suggestion-prompt a {
		color: var(--primary-color);
		font-weight: 600;
		text-decoration: underline;
	}

	.related-tours-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
		gap: 25px;
	}

	.related-tour-card {
		border-radius: var(--border-radius);
		overflow: hidden;
		box-shadow: var(--box-shadow);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
		background-color: var(--white-color);
		position: relative;
	}

	.related-tour-card.suggested {
		border: 2px solid #ffd700;
	}

	.related-tour-card.suggested::before {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		border-style: solid;
		border-width: 0 40px 40px 0;
		border-color: transparent #ffd700 transparent transparent;
		z-index: 1;
	}

	.related-tour-card.suggested::after {
		content: '★';
		position: absolute;
		top: 5px;
		right: 9px;
		color: #333;
		font-size: 14px;
		z-index: 2;
	}

	.related-tour-card:hover {
		transform: translateY(-5px);
		box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
	}

	.related-tour-image {
		height: 180px;
		position: relative;
	}

	.related-tour-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.related-tour-price {
		position: absolute;
		bottom: 10px;
		right: 10px;
		background-color: var(--accent-color);
		color: white;
		padding: 5px 10px;
		border-radius: 4px;
		font-weight: 600;
		font-size: 0.9rem;
	}

	.related-tour-content {
		padding: 15px;
	}

	.related-tour-content h3 {
		margin: 0 0 10px 0;
		font-size: 1.1rem;
		color: var(--secondary-color);
	}

	.related-tour-info {
		display: flex;
		justify-content: space-between;
		margin-bottom: 15px;
		font-size: 0.9rem;
		color: var(--dark-gray);
	}

	.related-tour-info i {
		margin-right: 5px;
		color: var(--primary-color);
	}

	.view-tour-btn {
		display: block;
		text-align: center;
		background-color: var(--primary-color);
		color: white;
		padding: 8px;
		border-radius: 4px;
		text-decoration: none;
		font-weight: 500;
		transition: background-color 0.3s ease;
	}

	.view-tour-btn:hover {
		background-color: #2980b9;
	}


	/* Responsive Adjustments */
	@media (max-width: 991px) {
		.hero-banner {
			/* Disable fixed background on mobile/tablet as it can be jerky */
			background-attachment: scroll;
		}
		.sticky-sidebar {
			position: static; /* Disable sticky sidebar */
		}
	}

	@media (max-width: 768px) {
		.hero-banner {
			height: 40vh;
			min-height: 300px;
		}
		.hero-overlay {
			padding: 25px;
		}
		.section {
			padding: 20px;
		}
		.section h2 {
			font-size: 1.5rem;
			margin-bottom: 20px;
			padding-bottom: 10px;
		}
		.tour-info-grid {
			grid-template-columns: 1fr; /* Stack items */
			gap: 20px;
		}
		.info-item {
			gap: 12px;
		}
		.info-item i {
			font-size: 1.4rem;
		}
		.info-item p {
			font-size: 1rem;
		}
		.info-item .price {
			font-size: 1.1rem;
		}
		.day-header {
			flex-direction: column;
			align-items: flex-start;
			gap: 5px;
		}
		.day-number {
			margin-bottom: 5px;
		}
		.day-header h3 {
			font-size: 1.2rem;
		}
		.booking-button {
			padding: 12px;
			font-size: 1rem;
		}
		.success-message {
			padding: 30px;
		}
		.success-icon {
			font-size: 3rem;
		}
		.success-message h3 {
			font-size: 1.4rem;
		}
	}

	@media (max-width: 480px) {
		.tour-detail-page-container {
			padding: 0 10px;
		}
		.hero-overlay {
			padding: 20px;
		}
		.hero-content h1 {
			font-size: 1.6rem;
		}
		.hero-location {
			font-size: 0.9rem;
		}
		.section {
			padding: 15px;
		}
		.form-group input[type="number"],
		.form-group textarea {
			padding: 10px 12px;
		}
		.booking-summary {
			padding: 15px;
		}
	}

</style>
