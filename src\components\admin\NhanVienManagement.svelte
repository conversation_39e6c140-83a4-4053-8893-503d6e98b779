<script>
  import { onMount } from 'svelte';
  import AddNhanVienForm from './AddNhanVienForm.svelte';
  import EditNhanVienForm from './EditNhanVienForm.svelte';

  let staff = [];
  let currentPage = 1;
  let itemsPerPage = 10;
  let searchQuery = '';
  let showAddStaffPage = false;
  let showEditStaffPage = false;
  let selectedStaffId = null;


  async function fetchStaff() {
    console.log('fetchStaff called');
    try {
      const url = 'http://localhost:5000/api/nhanvien';
      console.log('Fetching from URL:', url);

      const response = await fetch(url);
      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error('Failed to fetch staff');
      }

      const data = await response.json();
      console.log('Received data:', data);


      if (data && data.nhanvien && Array.isArray(data.nhanvien)) {
        staff = data.nhanvien;
        console.log('Staff data updated:', staff);
      } else {
        console.error('Invalid data structure:', data);
        throw new Error('Dữ liệu không hợp lệ');
      }
    } catch (error) {
      console.error('Error fetching staff:', error);
      alert('Có lỗi khi tải danh sách nhân viên: ' + error.message);
    }
  }

  async function handleDelete(id) {
    if (confirm('Bạn có chắc muốn xóa nhân viên này?')) {
      try {
        const response = await fetch(`http://localhost:5000/api/nhanvien/${id}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          throw new Error('Không thể xóa nhân viên');
        }

        await fetchStaff();
      } catch (error) {
        console.error('Error deleting staff:', error);
        alert('Có lỗi khi xóa nhân viên');
      }
    }
  }

  function toggleAddStaffPage() {
    showAddStaffPage = !showAddStaffPage;
    showEditStaffPage = false;
  }
  function handleStaffAdded() {
    fetchStaff();
    toggleAddStaffPage();
  }

  function showEditStaff(id) {
    console.log('showEditStaff called with id:', id);

    if (!id) {
      console.error('Invalid id:', id);
      alert('ID nhân viên không hợp lệ');
      return;
    }

    selectedStaffId = id;
    showEditStaffPage = true;
    showAddStaffPage = false;

    console.log('showEditStaffPage:', showEditStaffPage);
    console.log('selectedStaffId:', selectedStaffId);
  }
  function closeEditStaffPage() {
    console.log('closeEditStaffPage called');
    showEditStaffPage = false;
    selectedStaffId = null;
  }
  function handleStaffUpdated() {
    console.log('handleStaffUpdated called');
    fetchStaff();
    closeEditStaffPage();
  }



  $: filteredStaff = staff.filter(member =>
    member.ho_ten.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.chuc_vu.toLowerCase().includes(searchQuery.toLowerCase())
  );

  $: displayedStaff = filteredStaff.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  $: totalPages = Math.ceil(filteredStaff.length / itemsPerPage);

  function goToPage(page) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
    }
  }

  onMount(fetchStaff);
</script>

<div class="staff-management">
  {#if !showAddStaffPage && !showEditStaffPage}
    <div class="actions">
      <button class="add-button" on:click={toggleAddStaffPage}>
        <i class="fas fa-plus"></i> Thêm Nhân viên
      </button>
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="text"
          placeholder="Tìm kiếm nhân viên..."
          bind:value={searchQuery}
        >
      </div>
    </div>

    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Mã NV</th>
            <th>Họ tên</th>
            <th>Email</th>
            <th>Số điện thoại</th>
            <th>Chức vụ</th>
            <th>Ngày sinh</th>
            <th>Địa chỉ</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {#each displayedStaff as member}
            <tr>
              <td>{member.ma_nhan_vien}</td>
              <td>{member.ho_ten}</td>
              <td>{member.email}</td>
              <td>{member.so_dien_thoai}</td>
              <td>
                <span class="role-badge">
                  {member.chuc_vu}
                </span>
              </td>
              <td>{new Date(member.ngay_sinh).toLocaleDateString('vi-VN')}</td>
              <td>{member.dia_chi}</td>
              <td class="actions-cell">
                <button
                  class="action-btn edit"
                  aria-label="Sửa thông tin"
                  on:click={() => showEditStaff(member.ma_nhan_vien)}
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button
                  class="action-btn delete"
                  aria-label="Xóa nhân viên"
                  on:click={() => handleDelete(member.ma_nhan_vien)}
                >
                  <i class="fas fa-trash"></i>
                </button>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>

    <div class="pagination">
      <button on:click={() => goToPage(currentPage - 1)} disabled={currentPage === 1}>
        Trước
      </button>
      {#each Array(totalPages) as _, index}
        <button
          class:active={currentPage === index + 1}
          on:click={() => goToPage(index + 1)}
        >
          {index + 1}
        </button>
      {/each}
      <button
        on:click={() => goToPage(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        Sau
      </button>
    </div>
  {:else if showAddStaffPage}
    <div class="add-staff-container">
      <div class="add-staff-header">
        <button class="back-button" on:click={toggleAddStaffPage}>
          <i class="fas fa-arrow-left"></i> Quay lại
        </button>
      </div>
      <AddNhanVienForm on:staffAdded={handleStaffAdded} />
    </div>
  {:else if showEditStaffPage}
    <div class="edit-staff-container">
      <div class="edit-staff-header">
        <button class="back-button" on:click={closeEditStaffPage}>
          <i class="fas fa-arrow-left"></i> Quay lại
        </button>
      </div>
      <EditNhanVienForm
        staffId={selectedStaffId}
        on:staffUpdated={() => {
          console.log('staffUpdated event received from EditNhanVienForm');
          handleStaffUpdated();
        }}
        on:cancel={() => {
          console.log('cancel event received from EditNhanVienForm');
          closeEditStaffPage();
        }}
      />
    </div>
  {/if}
</div>

<style>
  .staff-management {
    padding: 1rem;
  }


  .add-staff-container,
  .edit-staff-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .add-staff-header,
  .edit-staff-header {
    margin-bottom: 2rem;
  }

  .back-button {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    transition: color 0.3s;
  }

  .back-button:hover {
    color: #333;
  }

  .actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
  }

  .add-button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .add-button:hover {
    background-color: #45a049;
  }

  .search-box {
    position: relative;
    width: 300px;
  }

  .search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
  }

  .search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    outline: none;
  }

  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 1rem;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
  }

  th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
  }


  th:nth-child(5),
  td:nth-child(5) {
    width: 150px;
    min-width: 150px;
  }

  .role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    background-color: #e3f2fd;
    color: #1565c0;
    display: inline-block;
    min-width: 120px;
    text-align: center;
  }

  .actions-cell {
    display: flex;
    gap: 0.5rem;
  }

  .action-btn {
    border: none;
    background: none;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .action-btn.edit {
    color: #2196F3;
  }

  .action-btn.delete {
    color: #f44336;
  }

  .action-btn:hover {
    background-color: #f5f5f5;
  }

  .pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .pagination button {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    background: white;
    transition: all 0.3s;
  }

  .pagination button.active {
    background-color: #4CAF50;
    color: white;
    border-color: #4CAF50;
  }

  .pagination button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
</style>