<script>
  import { Search, Globe, User, Compass, Settings, Map, MapPin, Cloud, Calendar, Home, Info, Phone, Plane, Train } from "lucide-svelte";
  import { goto } from "$app/navigation";
  import { user } from "../stores/userStore.js";
  import { page } from '$app/stores';

  // Menu item với dropdown là array hoặc không có dropdown
  let menuItems = [
    { name: "Trang Chủ", link: "/", active: true, icon: Home },
    { name: "Giới Thiệu", link: "/AboutUs", active: false, icon: Info },
    { name: "Tours", link: "#", active: true, icon: Compass,},
    { name: "<PERSON>h<PERSON><PERSON> sạn", link: "/hotels", active: false, icon: MapPin },
    { name: "<PERSON><PERSON> nhân", link: "/Profile", active: false, icon: User },
    {
      name: "Tiện Ích",
      link: "#",
      active: false,
      icon: Settings,
      dropdown: [
        { name: "Hỗ trợ", link: "#", active: false, icon: Phone },
        { name: "<PERSON><PERSON> to<PERSON>", link: "/Payment", icon: Settings },
        { name: "Vé máy bay", link: "#", active: false, icon: Plane },
        { name: "Vé tàu", link: "#", active: false, icon: Train },
      ]
    }
  ];

  const goToLogin = () => goto("/login");
  const booknow = () => goto("/");
  const logout = () => {
    user.set(null);
    goto("/");
  };

  // Cập nhật active theo pathname
  $: currentPath = $page.url.pathname;
  $: menuItems = menuItems.map(item => ({
    ...item,
    active: currentPath === item.link
  }));
</script>

<nav class="navbar">
  <div class="logo">
    <img src="/images/slide0.jpg" alt="Travela Logo" />
    <a class="brand-name" href="/">Travela</a>
  </div>

  <ul class="menu">
    {#each menuItems as item}
      <li>
        <a href={item.link} class={item.active ? "active" : ""}>
          {#if item.icon}
            <svelte:component this={item.icon} size={20} />
          {/if}
          {item.name}
        </a>

        {#if Array.isArray(item.dropdown)}
          <ul class="dropdown-menu">
            {#each item.dropdown as dropdownItem}
              <li>
                <a href={dropdownItem.link}>
                  {#if dropdownItem.icon}
                    <svelte:component this={dropdownItem.icon} size={16} />
                  {/if}
                  {dropdownItem.name}
                </a>
              </li>
            {/each}
          </ul>
        {/if}
      </li>
    {/each}
  </ul>

  <div class="nav-icons">
    <!-- <button class="icon-btn">
      <Search size={24} />
    </button> -->

    <!-- <button class="book-btn" on:click={booknow}>Book Now</button> -->

    {#if $user}
      <div class="user-wrapper">
        <button class="user-btn">
          <User size={20} />
          <span>Xin chào, {$user.ho_ten}</span>
        </button>
        <ul class="user-menu">
          <li>
            <button class="logout-btn" on:click={logout}>
              <i class="fas fa-sign-out-alt"></i> Đăng xuất
            </button>
          </li>
        </ul>
      </div>
    {:else}
      <button class="icon-btn" on:click={goToLogin}>
        <User size={24} />
      </button>
    {/if}
  </div>
</nav>

<style>
  .navbar {
    position: fixed;
    top: 0; left: 0; right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .logo img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
  }

  .brand-name {
    color: #333;
    text-decoration: none;
    font-size: 20px;
    font-weight: bold;
  }

  .menu {
    list-style: none;
    display: flex;
    gap: 16px;
    margin: 0;
    padding: 0;
  }

  .menu li {
    position: relative;
  }

  .menu a {
    position: relative;
    color: #333;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding: 8px 14px;
    border-radius: 12px;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .menu a::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 4px;
    width: 0%;
    height: 2px;
    background: linear-gradient(90deg, #00ffb8, #00c3ff);
    transition: width 0.3s ease-in-out;
    border-radius: 10px;
  }

  .menu a:hover::after,
  .menu .active::after {
    width: 100%;
  }

  .menu a:hover {
    color: #00bfa6;
  }

  .menu .active {
    color: #00bfa6;
  }

  .dropdown-menu {
    display: none;
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    background: white;
    list-style: none;
    margin: 0;
    padding: 8px 0;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 160px;
  }

  .dropdown-menu li {
    padding: 8px 16px;
    cursor: pointer;
    white-space: nowrap;
  }

  .dropdown-menu li:hover {
    background: #f0f0f0;
  }

  .dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    color: #333;
    text-decoration: none;
  }

  .menu li:hover .dropdown-menu {
    display: block;
  }

  .nav-icons {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .icon-btn {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* .book-btn {
    background: linear-gradient(135deg, #ff8a00, #ff4f00);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 138, 0, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  } */

  /* .book-btn:hover {
    background: linear-gradient(135deg, #ff4f00, #ff8a00);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 138, 0, 0.3);
  }

  .book-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(255, 138, 0, 0.2);
  } */

  .user-wrapper {
    position: relative;
  }

  .user-btn {
    background: none;
    border: none;
    color: #333;
    cursor: pointer;
    font-weight: bold;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .user-menu {
    display: none;
    position: absolute;
    top: calc(100% + 4px);
    right: 0;
    background: white;
    list-style: none;
    margin: 0;
    padding: 8px 0;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .user-menu li {
    padding: 8px 16px;
    cursor: pointer;
    white-space: nowrap;
  }

  .user-menu li:hover {
    background: #f0f0f0;
  }

  .user-wrapper:hover .user-menu {
    display: block;
  }



  .logout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    padding: 8px 16px;
    font-weight: bold;
    color: #333;
    cursor: pointer;
    text-align: left;
    width: 100%;
  }

  .logout-btn:hover {
    background: #f0f0f0;
  }

  .logout-btn i {
    width: 20px;
    text-align: center;
    color: #ff5252;
  }
</style>
