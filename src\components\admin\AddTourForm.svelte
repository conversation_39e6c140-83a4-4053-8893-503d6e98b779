<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher<{
    tourAdded: void;
  }>();

  let newTour = {
    ten_tour: '',
    mo_ta: '',
    gia: '',
    ngay_bat_dau: '',
    ngay_ket_thuc: '',
    so_cho_trong: '',
    dia_diem: '',
    hinh_anh: '', 
    loai_tour: 'trong_nuoc' 
  };


  let isLoading = false;
  let hasError = false;
  let errorMessage = '';
  let imageFile = null; 
  let imagePreview = ''; 

  function resetForm() {
    newTour = {
      ten_tour: '',
      mo_ta: '',
      gia: '',
      ngay_bat_dau: '',
      ngay_ket_thuc: '',
      so_cho_trong: '',
      dia_diem: '',
      hinh_anh: '', 
      loai_tour: 'trong_nuoc'
    };
    imageFile = null; 
    imagePreview = ''; 
  }

  function handleImageChange(event: Event) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    if (file) {
      imageFile = file;

      if (file.size > 2 * 1024 * 1024) {
        alert('Kích thước ảnh quá lớn. Vui lòng chọn ảnh nhỏ hơn 2MB.');
        imageFile = null;
        imagePreview = '';
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        if (typeof e.target?.result === 'string') {
          imagePreview = e.target.result;
          newTour.hinh_anh = e.target.result;
        }
      };
      reader.readAsDataURL(file);
    } else {
      imageFile = null;
      imagePreview = '';
      newTour.hinh_anh = '';
    }
  }

  async function handleSubmit() {
    isLoading = true;
    hasError = false;
    errorMessage = '';

    try {
      if (!newTour.ten_tour) throw new Error('Tên tour không được để trống');
      if (!newTour.gia) throw new Error('Giá tour không được để trống');
      if (!newTour.ngay_bat_dau) throw new Error('Ngày bắt đầu không được để trống');
      if (!newTour.ngay_ket_thuc) throw new Error('Ngày kết thúc không được để trống');
      if (!newTour.so_cho_trong) throw new Error('Số chỗ trống không được để trống');
      if (!newTour.dia_diem) throw new Error('Địa điểm không được để trống');
      const startDate = new Date(newTour.ngay_bat_dau);
      const endDate = new Date(newTour.ngay_ket_thuc);
      if (startDate > endDate) {
        throw new Error('Ngày bắt đầu phải trước ngày kết thúc');
      }
      let tourDataToSend = { ...newTour };
      if (tourDataToSend.hinh_anh && tourDataToSend.hinh_anh.startsWith('data:image') && tourDataToSend.hinh_anh.length > 100000) {
        console.log('Ảnh quá lớn để gửi dưới dạng Base64, chuyển sang lưu tên file');
        if (imageFile) {
          tourDataToSend.hinh_anh = imageFile.name;
        }
      }
      console.log('Sending tour data with image information');
      const response = await fetch('http://localhost:5000/api/tours', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(tourDataToSend)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Không thể thêm tour');
      }

      const result = await response.json();
      console.log('Tour created successfully:', result);
      dispatch('tourAdded');
      resetForm();
      alert('Thêm tour thành công!');
    } catch (error) {
      console.error('Error adding tour:', error);
      hasError = true;
      errorMessage = error.message;
      alert(`Có lỗi xảy ra: ${errorMessage}`); 
      isLoading = false;
    }
  }
</script>

<div class="add-tour-form">
  <h2>Thêm Tour Mới</h2>

  {#if isLoading}
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Đang xử lý...</p>
    </div>
  {:else}
    <form on:submit|preventDefault={handleSubmit}>
      <div class="form-grid">
        <div class="form-group">
          <label for="tenTour">Tên tour <span class="required">*</span></label>
          <input
            id="tenTour"
            type="text"
            bind:value={newTour.ten_tour}
            placeholder="Nhập tên tour"
            required
          >
        </div>

        <div class="form-group">
          <label for="diaDiem">Địa điểm <span class="required">*</span></label>
          <input
            id="diaDiem"
            type="text"
            bind:value={newTour.dia_diem}
            placeholder="Nhập địa điểm"
            required
          >
        </div>

        <div class="form-group">
          <label for="gia">Giá (VNĐ) <span class="required">*</span></label>
          <input
            id="gia"
            type="number"
            bind:value={newTour.gia}
            placeholder="Nhập giá tour"
            min="0"
            required
          >
        </div>

        <div class="form-group">
          <label for="soChoTrong">Số chỗ trống <span class="required">*</span></label>
          <input
            id="soChoTrong"
            type="number"
            bind:value={newTour.so_cho_trong}
            placeholder="Nhập số chỗ trống"
            min="1"
            required
          >
        </div>

        <div class="form-group">
          <label for="ngayBatDau">Ngày bắt đầu <span class="required">*</span></label>
          <input
            id="ngayBatDau"
            type="date"
            bind:value={newTour.ngay_bat_dau}
            required
          >
        </div>

        <div class="form-group">
          <label for="ngayKetThuc">Ngày kết thúc <span class="required">*</span></label>
          <input
            id="ngayKetThuc"
            type="date"
            bind:value={newTour.ngay_ket_thuc}
            required
          >
        </div>

        <div class="form-group">
          <label for="loaiTour">Loại tour <span class="required">*</span></label>
          <select
            id="loaiTour"
            bind:value={newTour.loai_tour}
            required
          >
            <option value="trong_nuoc">Tour trong nước</option>
            <option value="nuoc_ngoai">Tour nước ngoài</option>
          </select>
        </div>

        <!-- Re-added image upload section -->
        <div class="form-group image-upload">
          <label for="hinhAnh">Hình ảnh</label>
          <input
            id="hinhAnh"
            type="file"
            accept="image/*"
            on:change={handleImageChange}
            aria-describedby="imageHelp"
          >
          <small id="imageHelp" class="form-text text-muted">Chọn file ảnh cho tour.</small>
          {#if imagePreview}
            <div class="image-preview">
              <img src={imagePreview} alt="Xem trước ảnh" />
            </div>
          {/if}
        </div>
      </div>

      <div class="form-group full-width">
        <label for="moTa">Mô tả</label>
        <textarea
          id="moTa"
          bind:value={newTour.mo_ta}
          placeholder="Nhập mô tả chi tiết về tour"
          rows="5"
        ></textarea>
      </div>

      <div class="form-actions">
        <button type="button" class="btn-cancel" on:click={resetForm}>Hủy</button>
        <button type="submit" class="btn-save" disabled={isLoading}>Lưu Tour</button>
      </div>
    </form>
  {/if}

  {#if hasError}
    <div class="error-message">
      <p>{errorMessage}</p>
    </div>
  {/if}
</div>

<style>
  /* Các style khác giữ nguyên */
  .add-tour-form {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); /* Added subtle shadow */
  }

  h2 {
    margin-bottom: 25px; /* Increased margin */
    color: #333;
    font-size: 24px;
    font-weight: 600; /* Slightly bolder */
    border-bottom: 1px solid #eee; /* Added separator */
    padding-bottom: 10px; /* Added padding */
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Responsive grid */
    gap: 25px; /* Increased gap */
  }

  .form-group {
    margin-bottom: 15px;
  }

  .full-width {
    grid-column: 1 / -1;
  }

  label {
    display: block;
    margin-bottom: 8px; /* Increased margin */
    font-weight: 500;
    color: #555; /* Slightly lighter color */
  }

  .required {
    color: #dc3545; /* Standard Bootstrap danger color */
    margin-left: 2px;
  }

  input, select, textarea {
    width: 100%;
    padding: 12px; /* Increased padding */
    border: 1px solid #ced4da; /* Standard Bootstrap border color */
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; /* Added transitions */
  }

  textarea {
    resize: vertical;
    min-height: 100px; /* Set minimum height */
  }

  input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #80bdff; /* Standard Bootstrap focus color */
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25); /* Standard Bootstrap focus shadow */
  }

  /* Style for file input */
  input[type="file"] {
    padding: 8px; /* Adjust padding for file input */
  }

  .form-text {
    font-size: 0.875em;
    color: #6c757d; /* Standard Bootstrap muted color */
    margin-top: 4px;
  }


  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px; /* Increased margin */
    padding-top: 20px; /* Added padding */
    border-top: 1px solid #eee; /* Added separator */
  }

  .btn-cancel, .btn-save {
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
    border: 1px solid transparent; /* Base border */
  }

  .btn-cancel {
    background-color: #f8f9fa; /* Lighter gray */
    border-color: #ced4da;
    color: #343a40; /* Darker text */
  }

  .btn-save {
    background-color: #28a745; /* Standard Bootstrap success color */
    border-color: #28a745;
    color: white;
  }

  .btn-cancel:hover {
    background-color: #e2e6ea;
    border-color: #dae0e5;
  }

  .btn-save:hover {
    background-color: #218838;
    border-color: #1e7e34;
  }

  .btn-save:disabled {
    background-color: #6c757d; /* Muted color when disabled */
    border-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.65;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    min-height: 200px; /* Ensure it takes some space */
  }

  .loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff; /* Standard Bootstrap primary color */
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-message {
    background-color: #f8d7da; /* Standard Bootstrap danger background */
    color: #721c24; /* Standard Bootstrap danger text */
    border: 1px solid #f5c6cb; /* Standard Bootstrap danger border */
    padding: 15px; /* Increased padding */
    border-radius: 4px;
    margin-top: 20px;
  }

  /* Re-added image-related styles */
  .image-upload {
    grid-column: 1 / -1; /* Span full width */
  }

  .image-preview {
    margin-top: 15px; /* Increased margin */
    max-width: 300px; /* Limit preview size */
    border: 1px solid #ddd;
    padding: 5px;
    border-radius: 4px;
    background-color: #f8f9fa;
  }

  .image-preview img {
    display: block; /* Remove extra space below image */
    width: 100%;
    height: auto;
    border-radius: 4px;
  }
</style>
