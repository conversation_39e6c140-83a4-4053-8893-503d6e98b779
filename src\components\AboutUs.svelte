<script>
	import { onMount } from 'svelte';
	import AOS from 'aos';
	import 'aos/dist/aos.css';

	let counters = {
		customers: 0,
		tours: 0,
		destinations: 0,
		years: 0
	};

	const targetValues = {
		customers: 5000,
		tours: 100,
		destinations: 50,
		years: 10
	};

	function startCounting() {
		const duration = 2000; // 2 seconds
		const steps = 60;
		const interval = duration / steps;

		const timer = setInterval(() => {
			let allComplete = true;
			
			for (let key in counters) {
				if (counters[key] < targetValues[key]) {
					counters[key] += Math.ceil(targetValues[key] / steps);
					if (counters[key] > targetValues[key]) {
						counters[key] = targetValues[key];
					}
					allComplete = false;
				}
			}

			counters = counters; // trigger update
			
			if (allComplete) {
				clearInterval(timer);
			}
		}, interval);
	}

	onMount(() => {
		AOS.init({
			duration: 1000,
			easing: 'ease-in-out',
			once: false
		});

		// Start counter when element is in view
		const observer = new IntersectionObserver((entries) => {
			entries.forEach(entry => {
				if (entry.isIntersecting) {
					startCounting();
					observer.disconnect();
				}
			});
		});

		const statsSection = document.querySelector('.stats-section');
		if (statsSection) {
			observer.observe(statsSection);
		}
	});
</script>

<section class="container my-5">
	<div class="row align-items-center">
		<!-- Phần ảnh chồng -->
		<div class="col-md-6 position-relative image-stack" data-aos="fade-right">
			<div class="image-wrapper">
				<img src="/images/slide1.jpg" alt="Tour 1" class="stacked-image img1" />
				<img src="/images/slide2.jpg" alt="Tour 2" class="stacked-image img2" />
				<img src="/images/slide3.jpg" alt="Tour 3" class="stacked-image img3" />
				<img src="/images/slide4.jpg" alt="Tour 4" class="stacked-image img4" />
				<img src="/images/slide5.jpg" alt="Tour 5" class="stacked-image img5" />
			</div>
		</div>

		<!-- Phần slogan -->
		<div class="col-md-6 text-center" data-aos="fade-left">
			<h2>Về chúng tôi</h2>
			<p>Chúng tôi sẽ nỗ lực hết mình để biến giấc mơ du lịch của bạn thành hiện thực.</p>

			<div class="mt-4">
				<p data-aos="fade-left" data-aos-delay="200">"Du lịch không chỉ là hành trình, mà là trải nghiệm suốt đời!"</p>
				<p data-aos="fade-left" data-aos-delay="400">"Khám phá những miền đất mới, tạo ra những ký ức khó quên."</p>
				<p data-aos="fade-left" data-aos-delay="600">"Chúng tôi không chỉ tổ chức tour, chúng tôi tạo ra những chuyến phiêu lưu!"</p>
			</div>
		</div>
	</div>
</section>

<!-- Counter Stats Section -->
<section class="stats-section py-5 bg-light">
	<div class="container">
		<div class="row text-center">
			<div class="col-md-3 col-6 mb-4" data-aos="fade-up">
				<div class="counter-box">
					<i class="fas fa-users mb-3"></i>
					<h3>{counters.customers}+</h3>
					<p>Khách hàng hài lòng</p>
				</div>
			</div>
			<div class="col-md-3 col-6 mb-4" data-aos="fade-up" data-aos-delay="200">
				<div class="counter-box">
					<i class="fas fa-route mb-3"></i>
					<h3>{counters.tours}+</h3>
					<p>Tours đã tổ chức</p>
				</div>
			</div>
			<div class="col-md-3 col-6 mb-4" data-aos="fade-up" data-aos-delay="400">
				<div class="counter-box">
					<i class="fas fa-map-marked-alt mb-3"></i>
					<h3>{counters.destinations}+</h3>
					<p>Điểm đến</p>
				</div>
			</div>
			<div class="col-md-3 col-6 mb-4" data-aos="fade-up" data-aos-delay="600">
				<div class="counter-box">
					<i class="fas fa-calendar-alt mb-3"></i>
					<h3>{counters.years}+</h3>
					<p>Năm kinh nghiệm</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Core Values Section -->
<section class="core-values py-5">
	<div class="container">
		<h2 class="text-center mb-5" data-aos="fade-up">Giá trị cốt lõi</h2>
		<div class="row">
			<div class="col-md-4 mb-4" data-aos="fade-up">
				<div class="value-card">
					<i class="fas fa-heart"></i>
					<h4>Tận tâm</h4>
					<p>Chúng tôi luôn đặt sự hài lòng của khách hàng lên hàng đầu</p>
				</div>
			</div>
			<div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
				<div class="value-card">
					<i class="fas fa-shield-alt"></i>
					<h4>An toàn</h4>
					<p>Đảm bảo an toàn tuyệt đối cho mọi hành trình</p>
				</div>
			</div>
			<div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="400">
				<div class="value-card">
					<i class="fas fa-star"></i>
					<h4>Chất lượng</h4>
					<p>Cam kết dịch vụ chất lượng cao với giá cả hợp lý</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Timeline Section -->
<section class="timeline-section py-5">
	<div class="container">
		<h2 class="text-center mb-5" data-aos="fade-up">Hành trình phát triển</h2>
		<div class="timeline">
			<div class="timeline-item" data-aos="fade-right">
				<div class="timeline-content">
					<h4>2015</h4>
					<p>Thành lập công ty</p>
				</div>
			</div>
			<div class="timeline-item" data-aos="fade-left">
				<div class="timeline-content">
					<h4>2018</h4>
					<p>Mở rộng dịch vụ tour quốc tế</p>
				</div>
			</div>
			<div class="timeline-item" data-aos="fade-right">
				<div class="timeline-content">
					<h4>2020</h4>
					<p>Phát triển tour trải nghiệm độc đáo</p>
				</div>
			</div>
			<div class="timeline-item" data-aos="fade-left">
				<div class="timeline-content">
					<h4>2023</h4>
					<p>Đạt giải thưởng Du lịch xuất sắc</p>
				</div>
			</div>
		</div>
	</div>
</section>

<style>
	.container {
		padding: 4rem 0;
	}

	h2 {
		font-size: 2.8rem;
		font-weight: 800;
		color: #1a237e;
		margin-bottom: 2rem;
		position: relative;
		display: inline-block;
		text-transform: uppercase;
		letter-spacing: 1px;
	}

	h2::after {
		content: '';
		position: absolute;
		bottom: -12px;
		left: 50%;
		transform: translateX(-50%);
		width: 80px;
		height: 4px;
		background: linear-gradient(90deg, #1e88e5, #64b5f6);
		border-radius: 4px;
	}

	p {
		font-size: 1.2rem;
		line-height: 1.9;
		color: #37474f;
		margin-bottom: 1.8rem;
	}

	.mt-4 p {
		font-style: italic;
		background: linear-gradient(45deg, #e3f2fd, #ffffff);
		padding: 2rem;
		border-radius: 15px;
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
		margin: 1.5rem 0;
		transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
		border-left: 5px solid #1e88e5;
	}

	.mt-4 p:hover {
		transform: translateY(-8px) scale(1.02);
		box-shadow: 0 12px 30px rgba(30, 136, 229, 0.15);
	}

	.image-stack {
		position: relative;
		height: 550px;
		perspective: 1200px;
	}

	.image-wrapper {
		position: relative;
		width: 100%;
		height: 100%;
		transform-style: preserve-3d;
	}

	.stacked-image {
		position: absolute;
		width: 340px;
		height: 440px;
		object-fit: cover;
		border-radius: 20px;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
		transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
		border: 8px solid white;
	}

	.img1 { top: 0; left: 0; transform: rotate(-8deg); z-index: 5; }
	.img2 { top: 40px; left: 40px; transform: rotate(5deg); z-index: 4; }
	.img3 { top: 80px; left: 80px; transform: rotate(-3deg); z-index: 3; }
	.img4 { top: 120px; left: 120px; transform: rotate(6deg); z-index: 2; }
	.img5 { top: 160px; left: 160px; transform: rotate(-4deg); z-index: 1; }

	.stacked-image:hover {
		transform: scale(1.1) rotate(0deg) translateZ(50px);
		box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
		z-index: 10;
		cursor: pointer;
	}

	/* Counter Stats Styles */
	.stats-section {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		padding: 4rem 0;
	}

	.counter-box {
		padding: 2.5rem;
		background: white;
		border-radius: 20px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
		transition: all 0.4s ease;
		border: 1px solid rgba(30, 136, 229, 0.1);
	}

	.counter-box:hover {
		transform: translateY(-10px);
		box-shadow: 0 15px 35px rgba(30, 136, 229, 0.2);
		cursor: pointer;
	}

	.counter-box i {
		font-size: 3rem;
		color: #1e88e5;
		margin-bottom: 1rem;
		transition: transform 0.3s ease;
	}

	.counter-box:hover i {
		transform: scale(1.2);
	}

	.counter-box h3 {
		font-size: 3rem;
		font-weight: 800;
		color: #1a237e;
		margin: 1rem 0;
		background: linear-gradient(45deg, #1e88e5, #64b5f6);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	/* Core Values Styles */
	.value-card {
		text-align: center;
		padding: 2.5rem;
		background: white;
		border-radius: 20px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
		transition: all 0.4s ease;
		border: 1px solid rgba(30, 136, 229, 0.1);
		height: 100%;
	}

	.value-card:hover {
		transform: translateY(-10px);
		box-shadow: 0 15px 35px rgba(30, 136, 229, 0.2);
		cursor: pointer;
	}

	.value-card i {
		font-size: 3.5rem;
		color: #1e88e5;
		margin-bottom: 1.5rem;
		transition: all 0.4s ease;
	}

	.value-card:hover i {
		transform: scale(1.2) rotate(360deg);
		color: #1a237e;
	}

	.value-card h4 {
		color: #1a237e;
		margin-bottom: 1.2rem;
		font-size: 1.5rem;
		font-weight: 700;
	}

	/* Timeline Styles */
	.timeline {
		position: relative;
		max-width: 900px;
		margin: 0 auto;
		padding: 2rem 0;
	}

	.timeline::after {
		content: '';
		position: absolute;
		width: 3px;
		background: linear-gradient(to bottom, #1e88e5, #64b5f6);
		top: 0;
		bottom: 0;
		left: 50%;
		margin-left: -1.5px;
		border-radius: 3px;
	}

	.timeline-item {
		padding: 15px 50px;
		position: relative;
		width: 50%;
	}

	.timeline-item::after {
		content: '';
		position: absolute;
		width: 20px;
		height: 20px;
		background: #1e88e5;
		border: 4px solid #fff;
		border-radius: 50%;
		top: 50%;
		transform: translateY(-50%);
		box-shadow: 0 0 0 4px rgba(30, 136, 229, 0.2);
		z-index: 1;
	}

	.timeline-item:nth-child(odd) {
		left: 0;
	}

	.timeline-item:nth-child(odd)::after {
		right: -10px;
	}

	.timeline-item:nth-child(even) {
		left: 50%;
	}

	.timeline-item:nth-child(even)::after {
		left: -10px;
	}

	.timeline-content {
		padding: 25px;
		background: white;
		border-radius: 15px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
		transition: all 0.4s ease;
	}

	.timeline-content:hover {
		transform: translateY(-5px);
		box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
		cursor: pointer;
	}

	.timeline-content h4 {
		color: #1e88e5;
		margin-bottom: 0.8rem;
		font-size: 1.4rem;
		font-weight: 700;
	}

	@media (max-width: 768px) {
		.container {
			padding: 2rem 1rem;
		}

		h2 {
			font-size: 2.2rem;
		}

		.image-stack {
			height: 450px;
			margin-bottom: 3rem;
		}

		.stacked-image {
			width: 280px;
			height: 360px;
		}

		.timeline::after {
			left: 31px;
		}

		.timeline-item {
			width: 100%;
			padding-left: 70px;
			padding-right: 25px;
		}

		.timeline-item:nth-child(even) {
			left: 0;
		}

		.timeline-item::after {
			left: 21px !important;
		}

		.counter-box h3 {
			font-size: 2.5rem;
		}
	}

	@media (max-width: 576px) {
		.stacked-image {
			width: 240px;
			height: 320px;
		}

		.image-stack {
			height: 400px;
		}

		.counter-box {
			padding: 1.5rem;
		}

		.value-card {
			padding: 1.5rem;
		}
	}
</style>
